/* ===== ARTCHOP MAIN CSS - CONSOLIDATED ===== */

/* CSS Variables */
:root {
    /* Colors */
    --color-background: #1a1a1a;
    --color-text: #ffffff;
    --color-primary: #8781bd;
    --color-secondary: #FF42A1;
    --color-blue: #00A2FF;
    --color-dark: #000000;
    --color-light: #ffffff;
    --color-gray: #333333;

    /* Section Colors */
    --color-about-bg: #3498db;
    --color-gallery-bg: #000000;
    --color-contact-bg: #2ecc71;

    /* Spacing */
    --spacing-xs: 10px;
    --spacing-sm: 20px;
    --spacing-md: 40px;
    --spacing-lg: 60px;
    --spacing-xl: 100px;

    /* Animation Timing */
    --timing-fast: 0.3s;
    --timing-medium: 0.5s;
    --timing-slow: 0.8s;

    /* Z-index layers */
    --z-background: 0;
    --z-content: 5;
    --z-sections: 10;
    --z-navbar: 9999;
}

/* ===== RESET & BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Hanken Grotesk', sans-serif;
    background-color: var(--color-background);
    color: var(--color-text);
    overflow-x: hidden;
    scroll-padding-top: 80px;
}

/* ===== TYPOGRAPHY ===== */
h1, h2 {
    font-family: 'Syncopate', sans-serif;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: -0.05em;
    line-height: 0.9;
    color: #ffffff;
}

h1 {
    font-size: clamp(2.5rem, 8vw, 6rem);
    max-width: 50%;
}

h2 {
    font-size: clamp(1.8rem, 5vw, 4rem);
    margin: 0 0 20px 0;
    max-width: 600px;
}

p {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.775rem;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: 0.01em;
    color: #ffffff;
    margin-bottom: 30px;
    max-width: 600px;
}

.subheadline {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: clamp(1rem, 3vw, 1.2rem);
    font-weight: 700;
    line-height: 1.4;
    letter-spacing: 0.5em;
    text-transform: uppercase;
    color: #ffffff;
    margin: 30px 0;
    max-width: 50%;
}

/* ===== NAVBAR ===== */
nav {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 160px);
    max-width: 1200px;
    height: 44px;
    background-color: var(--color-light);
    z-index: var(--z-navbar);
    padding: 0 var(--spacing-sm);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 0;
}

.navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
}

.site-logo {
    height: 100%;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo-container {
    position: relative;
    padding: 12px;
}

.logo-image {
    position: absolute;
    top: 50%;
    left: 0;
    height: 30px;
    width: auto;
    transform: translateY(-50%);
    transition: clip-path 0.25s cubic-bezier(0.4, 0, 0.05, 1);
}

.logo-1 {
    clip-path: inset(0 0 0 0);
}

.logo-2 {
    clip-path: inset(0 100% 0 0);
}

.logo-container:hover .logo-1 {
    clip-path: inset(0 0 0 100%);
}

.logo-container:hover .logo-2 {
    clip-path: inset(0 0 0 0);
}

.nav-links a {
    color: var(--color-dark);
    text-decoration: none;
    font-size: 0.775em;
    position: relative;
    padding: 4px 8px;
    transition: color var(--timing-fast) cubic-bezier(0.4, 0, 0.05, 1);
}

.nav-links a:hover {
    color: var(--color-light);
}

.nav-links a::after {
    content: '';
    position: absolute;
    top: 0;
    left: -5px;
    width: calc(100% + 10px);
    height: 100%;
    background-color: var(--color-secondary);
    z-index: -1;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--timing-fast) cubic-bezier(0.4, 0, 0.05, 1);
}

.nav-links a:hover::after {
    transform: scaleX(1);
}

/* Mobile Navigation */
.hamburger-icon {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    z-index: 1001;
}

.hamburger-icon span {
    display: block;
    width: 22px;
    height: 2px;
    background-color: var(--color-dark);
    margin: 4px 0;
    transition: all var(--timing-fast) ease-in-out;
}

@media (max-width: 768px) {
    .hamburger-icon {
        display: block;
    }

    .nav-links {
        display: none;
        position: fixed;
        top: 44px;
        left: 0;
        right: 0;
        background-color: #ffffff;
        padding: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        z-index: 9998;
    }

    .nav-links.active {
        display: block;
    }

    .hamburger-icon.active span:nth-child(1) {
        transform: translateY(6px) rotate(45deg);
    }

    .hamburger-icon.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger-icon.active span:nth-child(3) {
        transform: translateY(-6px) rotate(-45deg);
    }
}

/* ===== SECTIONS ===== */
section {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    padding: 100px 40px;
    scroll-margin-top: 80px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.fullscreen-section {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: left;
}

.fullscreen-section .about-content,
.fullscreen-section .contact-content,
.fullscreen-section .gallery-content {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    width: 100%;
    max-width: 1200px;
    height: 100%;
    color: #ffffff;
    text-align: left;
    padding: 0 20px;
}

/* Section specific backgrounds */
.hero-section {
    position: relative;
    overflow: hidden;
}

.about-section {
    background: linear-gradient(to bottom, #3498db 0%, #000000 100%) !important;
    z-index: var(--z-sections);
    transform: translateZ(0);
}

#about-2 {
    background: #000 !important;
}

.contact-section {
    background-color: var(--color-contact-bg) !important;
}

/* ===== BUTTONS ===== */
.cta-button {
    background-color: #FF42A1;
    color: #ffffff;
    padding: 15px 30px;
    border-radius: 0;
    text-decoration: none;
    font-family: 'Hanken Grotesk', sans-serif;
    font-weight: 700;
    font-size: 1.2rem;
    line-height: 1.4;
    letter-spacing: 0.01em;
    text-transform: uppercase;
    border: none;
    cursor: pointer;
    position: relative;
    z-index: 1;
    overflow: hidden;
    transition: all 0.3s ease;
    display: inline-block;
    margin-top: 20px;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    transition: all 0.3s ease;
    z-index: -1;
}

.cta-button:hover {
    color: #333333 !important;
}

.cta-button:hover::before {
    left: 0;
}

/* ===== HERO SECTION ===== */
.hero-background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.hero-content-container {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 40px;
}

.hero-content {
    max-width: 1200px;
    width: 100%;
    color: white;
    text-align: left;
}

.hero-carousel {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
}

/* ===== WIDGET SYSTEM ===== */
.widget-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.widget {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    min-height: 200px;
    display: flex;
    flex-direction: column;
}

.widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.widget.wide {
    grid-column: span 2;
}

.widget.tall {
    grid-row: span 2;
}

.widget.large {
    grid-column: span 2;
    grid-row: span 2;
}

.widget.dominant {
    grid-column: span 3;
    grid-row: span 2;
}

.widget-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.widget-icon {
    font-size: 1.2rem;
}

.widget-title {
    font-family: 'Hanken Grotesk', sans-serif;
    font-weight: 700;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.widget-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.widget-label {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
}

/* ===== CONTACT FORM ===== */
.contact-form-new {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px 0;
}

.form-field {
    margin-bottom: 20px;
}

.form-field input,
.form-field textarea {
    width: 100%;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    color: white;
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1rem;
    box-sizing: border-box;
}

.form-field input::placeholder,
.form-field textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-field input:focus,
.form-field textarea:focus {
    outline: none;
    border-color: #FF42A1;
    background: rgba(255, 255, 255, 0.15);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.8);
    font-family: 'Hanken Grotesk', sans-serif;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.send-button {
    width: 100%;
    padding: 15px;
    background: #FF42A1;
    color: white;
    border: none;
    border-radius: 5px;
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.send-button:hover {
    background: #e03185;
}

.send-button:disabled {
    background: #666;
    cursor: not-allowed;
}

#form-message {
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
    font-weight: bold;
}

#form-message.success {
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
    border: 1px solid #27ae60;
}

#form-message.error {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid #e74c3c;
}

/* ===== MODALS ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.active {
    display: flex;
    opacity: 1;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 90%;
    max-height: 90vh;
    width: 600px;
    overflow-y: auto;
    position: relative;
    color: #333;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    font-size: 24px;
    color: #000;
    cursor: pointer;
    padding: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Hanken Grotesk', sans-serif;
    font-weight: normal;
    line-height: 1;
}

.modal-close:hover {
    color: #FF42A1;
}

.modal-header h2 {
    margin: 0 0 20px 0;
    color: #333;
    font-family: 'Syncopate', sans-serif;
    font-size: 1.5rem;
}

.modal-body {
    line-height: 1.6;
}

.modal-body h3 {
    color: #FF42A1;
    margin: 20px 0 10px 0;
    font-family: 'Hanken Grotesk', sans-serif;
}

.modal-body p {
    margin-bottom: 15px;
    font-family: 'Hanken Grotesk', sans-serif;
    color: #333;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 992px) {
    .widget-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .widget.wide,
    .widget.large,
    .widget.dominant {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    section {
        padding: 60px 20px;
    }

    .hero-content-container {
        padding: 0 20px;
    }

    .modal-content {
        width: 95%;
        padding: 20px;
        margin: 20px;
    }

    .widget {
        min-height: 150px;
    }
}

/* ===== SPECIFIC WIDGET STYLES ===== */

/* Mission Widget (ARTCHOP) - Dark theme to stand out */
.mission-widget {
    background: rgba(0, 0, 0, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
}

.mission-widget .widget-title {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

/* Action Widget - Dark theme to match mission */
.action-widget {
    background: rgba(0, 0, 0, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
}

.action-widget .widget-title {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

/* All other widgets have same tint */
.team-widget,
.experience-widget,
.projects-widget,
.skills-widget,
.vision-widget,
.vibe-widget,
.aura-widget,
.map-widget,
.contact-widget {
    background: rgba(255, 255, 255, 0.1);
}

/* Team Breakdown Pie Chart */
.team-breakdown-layout {
    display: flex;
    align-items: center;
    gap: 20px;
    height: 100%;
}

.pie-chart {
    flex-shrink: 0;
    width: 120px;
    height: 120px;
    position: relative;
}

.pie-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.pie-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
}

.pie-legend {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.7rem;
    font-weight: 600;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    flex-shrink: 0;
}

/* Technologies Widget */
.tech-logos {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 100%;
    justify-content: center;
}

.tech-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.tech-logo {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Hanken Grotesk', sans-serif;
    font-weight: 700;
    font-size: 0.7rem;
    color: white;
    flex-shrink: 0;
}

.unity-logo { background: #000000; }
.unreal-logo { background: #0E1128; }
.shader-logo { background: #FF6B35; }
.substance-painter-logo { background: #F2A900; }
.substance-designer-logo { background: #F2A900; }
.marvelous-logo { background: #FF6B6B; }

.tech-name {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.65rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Expertise Grid */
.expertise-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    height: 100%;
    align-content: center;
}

.expertise-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.expertise-checkbox {
    width: 16px;
    height: 16px;
    background: #00ff88;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 700;
    color: #000;
    flex-shrink: 0;
}

.expertise-text {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.65rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

/* Vibe Check Widget */
.vibe-simple {
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.vibe-status-row {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
}

.status-options {
    display: flex;
    gap: 12px;
}

.status-option {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.1rem;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 4px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.status-option.active {
    opacity: 1;
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    text-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
}

.status-indicator-large {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ff4444;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.status-indicator-large.on {
    background: #00ff88;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
    animation: sineWave 4s infinite ease-in-out;
}

@keyframes sineWave {
    0% { transform: scale(1) translateY(0px); box-shadow: 0 0 20px rgba(0, 255, 136, 0.6); }
    25% { transform: scale(1.1) translateY(-1px); box-shadow: 0 0 25px rgba(0, 255, 136, 0.8); }
    50% { transform: scale(1) translateY(0px); box-shadow: 0 0 20px rgba(0, 255, 136, 0.6); }
    75% { transform: scale(0.9) translateY(1px); box-shadow: 0 0 15px rgba(0, 255, 136, 0.4); }
    100% { transform: scale(1) translateY(0px); box-shadow: 0 0 20px rgba(0, 255, 136, 0.6); }
}

/* Aura Meter Widget */
.aura-meter {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 16px;
    height: 100%;
    justify-content: center;
}

.aura-bar {
    width: 100%;
    height: 14px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 0;
    overflow: hidden;
    position: relative;
}

.aura-fill {
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7);
    border-radius: 0;
    position: relative;
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.aura-percentage {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.3rem;
    font-weight: 700;
    text-align: center;
    background: linear-gradient(45deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: rainbow 3s infinite;
    line-height: 1;
}

@keyframes rainbow {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(180deg); }
}

/* World Map / Locations Widget */
.timezone-clocks {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 8px;
    height: 100%;
    align-content: center;
}

.clock-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.clock-face {
    width: 60px;
    height: 60px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    position: relative;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.clock-face.nighttime {
    background: rgba(0, 0, 0, 0.6);
    border-color: rgba(255, 255, 255, 0.2);
}

.clock-face::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 4px;
    background: #ffffff;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
    box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
}

.clock-ticks {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.clock-tick {
    position: absolute;
    width: 2px;
    height: 8px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 1px;
}

.clock-hand {
    position: absolute;
    background: #ffffff;
    border-radius: 2px;
    transform-origin: bottom center;
    z-index: 2;
}

.hour-hand {
    width: 2px;
    height: 18px;
    top: 12px;
    left: 50%;
    margin-left: -1px;
}

.minute-hand {
    width: 1px;
    height: 24px;
    top: 6px;
    left: 50%;
    margin-left: -0.5px;
}

.clock-label {
    text-align: center;
}

.city-name {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.6rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 2px;
}

.time-display {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.65rem;
    font-weight: 600;
    opacity: 0.8;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 100%;
    justify-content: center;
}

.action-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 0;
    font-family: 'Hanken Grotesk', sans-serif;
    font-weight: 700;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.action-pink {
    background: #FF42A1;
    color: white;
}

.action-blue {
    background: #00A2FF;
    color: white;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: white;
    transition: left 0.3s ease;
    z-index: -1;
}

.action-btn:hover {
    color: black;
}

.action-btn:hover::before {
    left: 0;
}

/**
 * ARTCHOP MAIN JAVASCRIPT - CONSOLIDATED
 * All website functionality in one file
 */

// ===== GLOBAL VARIABLES =====
const REEL_ITEMS = 7;

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('Artchop website initializing...');

    // Initialize all functionality
    initHeroSectionAnimations();
    initHeroAnimation();
    initHamburgerMenu();
    initCarousel();
    initSmoothScrolling();
    initScrollBasedAnimations();
    initWorldClocks();
    initContactForm();

    // Initialize gallery if function exists
    if (typeof initGallery === 'function') {
        initGallery();
    }
});

// ===== HERO ANIMATIONS =====
function initHeroSectionAnimations() {
    console.log('Initializing hero section animations');

    // Process hero headlines
    const heroHeadlines = document.querySelectorAll('.hero-content h1, .hero-content h2');
    heroHeadlines.forEach(headline => processElement(headline));

    // Process section headlines
    const sectionHeadlines = document.querySelectorAll('.section h2, .about-section h2, .about-content h2');
    sectionHeadlines.forEach(headline => processElement(headline));

    // Process hero subheadlines
    const heroSubheadlines = document.querySelectorAll('.hero-content p');
    heroSubheadlines.forEach(subheadline => {
        if (!subheadline.classList.contains('processed')) {
            processElement(subheadline);
        }
    });

    // Process hero buttons
    const heroButtons = document.querySelectorAll('.hero-content .cta-button');
    heroButtons.forEach(button => {
        if (!button.classList.contains('processed')) {
            processButton(button);
        }
    });
}

function initHeroAnimation() {
    const activeSlide = document.querySelector('.hero-slide.active');
    if (activeSlide) {
        const heroImage = activeSlide.querySelector('.hero-background-image');
        if (heroImage) {
            heroImage.style.animation = 'none';
            void heroImage.offsetWidth;
            heroImage.style.animation = 'scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards';
        }
    }
}

// ===== TEXT PROCESSING =====
function processElement(element) {
    if (element.classList.contains('processed')) return;

    try {
        const originalText = element.textContent;
        element.classList.add('processed');
        element.dataset.originalText = originalText;

        const words = originalText.split(/\s+/).filter(word => word.trim() !== '');
        if (words.length === 0) return;

        element.innerHTML = '';

        words.forEach((word, wordIndex) => {
            const slotMask = document.createElement('span');
            slotMask.className = 'slot-mask';

            if (element.tagName.match(/^H[1-6]$/i)) {
                slotMask.classList.add('heading-mask');
            } else if (element.tagName.toLowerCase() === 'p') {
                slotMask.classList.add('paragraph-mask');
            }

            const slotReel = document.createElement('div');
            slotReel.className = 'slot-reel';

            for (let i = 0; i < REEL_ITEMS; i++) {
                const slotItem = document.createElement('div');
                slotItem.style.height = '1em';
                slotItem.style.lineHeight = '1';
                slotItem.textContent = word;
                slotReel.appendChild(slotItem);
            }

            const finalItem = document.createElement('div');
            finalItem.style.height = '1em';
            finalItem.style.lineHeight = '1';
            finalItem.textContent = word;
            slotReel.appendChild(finalItem);

            const delay = 0.05 + (wordIndex * 0.08);
            slotReel.style.animationDelay = `${delay}s`;

            slotMask.appendChild(slotReel);
            element.appendChild(slotMask);

            if (wordIndex < words.length - 1) {
                element.appendChild(document.createTextNode(' '));
            }
        });
    } catch (error) {
        console.error('Error processing element:', error);
        element.textContent = originalText;
        element.classList.remove('processed');
    }
}

function processButton(buttonElement) {
    if (!document.body.contains(buttonElement) || buttonElement.classList.contains('processed')) return;

    try {
        const originalText = buttonElement.textContent;
        const originalHref = buttonElement.getAttribute('href') || '#';

        buttonElement.classList.add('processed');
        buttonElement.dataset.originalText = originalText;

        const buttonClone = buttonElement.cloneNode(true);
        const buttonMask = document.createElement('div');
        buttonMask.className = 'button-mask';
        buttonMask.dataset.originalText = originalText;
        buttonMask.dataset.originalHref = originalHref;

        const buttonReel = document.createElement('div');
        buttonReel.className = 'slot-reel';
        buttonReel.appendChild(buttonClone);

        const delay = 0.5;
        const duration = 0.8;

        buttonReel.style.animation = `slotSpin ${duration}s cubic-bezier(0.19, 1, 0.22, 1) forwards`;
        buttonReel.style.animationDelay = `${delay}s`;

        buttonMask.appendChild(buttonReel);

        const parentContainer = buttonElement.parentNode;
        parentContainer.replaceChild(buttonMask, buttonElement);

        buttonClone.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            if (targetId && targetId.startsWith('#')) {
                const target = document.querySelector(targetId);
                if (target) {
                    const targetPosition = target.getBoundingClientRect().top + window.scrollY;
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            } else if (originalHref) {
                window.location.href = originalHref;
            }
        });
    } catch (error) {
        console.error('Error processing button:', error);
        buttonElement.classList.remove('processed');
    }
}

// ===== NAVIGATION =====
function initHamburgerMenu() {
    const hamburger = document.querySelector('.hamburger-icon');
    const navLinks = document.querySelector('.nav-links');

    if (hamburger && navLinks) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navLinks.classList.toggle('active');
        });
    }
}

function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const targetPosition = target.getBoundingClientRect().top + window.scrollY;
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// ===== CAROUSEL =====
function initCarousel() {
    const slides = document.querySelectorAll('.hero-slide');
    if (slides.length <= 1) return;

    let currentSlide = 0;
    const totalSlides = slides.length;
    const transitionTime = 5000;

    function nextSlide() {
        slides[currentSlide].classList.remove('active');
        currentSlide = (currentSlide + 1) % totalSlides;
        slides[currentSlide].classList.add('active');

        const heroImage = slides[currentSlide].querySelector('.hero-background-image');
        if (heroImage) {
            heroImage.style.animation = 'none';
            void heroImage.offsetWidth;
            heroImage.style.animation = 'scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards';
        }
    }

    setInterval(nextSlide, transitionTime);
}

// ===== SCROLL ANIMATIONS =====
function initScrollBasedAnimations() {
    const sections = [
        { id: 'about', selector: '.about-content', sectionClass: 'about-section' },
        { id: 'about-2', selector: '.about-content', sectionClass: 'about-section' },
        { id: 'contact', selector: '.contact-content' }
    ];

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const sectionId = entry.target.id;
            const section = sections.find(s => s.id === sectionId);

            if (section && entry.isIntersecting) {
                if (section.sectionClass) {
                    entry.target.classList.add('active');
                }

                // Process section elements
                const sectionHeadlines = entry.target.querySelectorAll('h1, h2');
                sectionHeadlines.forEach(headline => {
                    headline.style.animation = 'none';
                    headline.offsetHeight;
                    processElement(headline);
                });

                const allParagraphs = entry.target.querySelectorAll('p');
                const sectionSubheadlines = Array.from(allParagraphs).filter(p =>
                    !p.closest('.widget-content')
                );
                sectionSubheadlines.forEach(subheadline => {
                    subheadline.style.animation = 'none';
                    subheadline.offsetHeight;
                    processElement(subheadline);
                });

                const sectionButtons = entry.target.querySelectorAll('.cta-button:not(.action-widget-btn)');
                sectionButtons.forEach(button => {
                    if (!button.closest('.action-widget')) {
                        button.style.animation = 'none';
                        button.offsetHeight;
                        processButton(button);
                    }
                });
            } else if (section && section.sectionClass) {
                entry.target.classList.remove('active');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    sections.forEach(section => {
        const sectionElement = document.getElementById(section.id);
        if (sectionElement) {
            observer.observe(sectionElement);
        }
    });
}

// ===== WORLD CLOCKS =====
const timezones = {
    'sf': 'America/Los_Angeles',
    'saitama': 'Asia/Tokyo',
    'manila': 'Asia/Manila',
    'novi-sad': 'Europe/Belgrade'
};

function initWorldClocks() {
    const hasClocks = Object.keys(timezones).some(city =>
        document.getElementById(`${city}-clock`) || document.getElementById(`${city}-time`)
    );

    if (hasClocks) {
        updateClocks();
        setInterval(updateClocks, 1000);
    }
}

function updateClocks() {
    Object.keys(timezones).forEach(city => {
        const clockFace = document.getElementById(`${city}-clock`);
        const timeDisplay = document.getElementById(`${city}-time`);
        const hourHand = document.getElementById(`${city}-hour`);
        const minuteHand = document.getElementById(`${city}-minute`);

        if (timeDisplay || hourHand || minuteHand) {
            const now = new Date();
            const timeInZone = new Date(now.toLocaleString("en-US", {timeZone: timezones[city]}));

            const hours = timeInZone.getHours();
            const minutes = timeInZone.getMinutes();
            const displayHours = hours % 12 || 12;
            const ampm = hours >= 12 ? 'PM' : 'AM';

            // Update time display
            if (timeDisplay) {
                timeDisplay.textContent = `${displayHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')} ${ampm}`;
            }

            // Update clock hands
            if (hourHand && minuteHand) {
                const hourAngle = (hours % 12) * 30 + (minutes * 0.5);
                const minuteAngle = minutes * 6;

                hourHand.style.transform = `rotate(${hourAngle}deg)`;
                minuteHand.style.transform = `rotate(${minuteAngle}deg)`;
            }

            // Update nighttime styling
            if (clockFace) {
                const isNighttime = hours < 6 || hours >= 18;
                clockFace.classList.toggle('nighttime', isNighttime);
            }
        }
    });
}

// ===== CONTACT FORM =====
function initContactForm() {
    const form = document.getElementById('contact-form-real');
    const messageDiv = document.getElementById('form-message');

    if (!form) return;

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        // Get form values
        const name = document.getElementById('contact-name').value.trim();
        const email = document.getElementById('contact-email').value.trim();
        const message = document.getElementById('contact-message').value.trim();
        const terms = document.getElementById('contact-terms').checked;

        // Validate
        if (!name || !email || !message || !terms) {
            showMessage('Please fill in all fields and accept the terms.', 'error');
            return;
        }

        // Show loading
        submitBtn.textContent = 'Sending...';
        submitBtn.disabled = true;

        // Submit the form
        const formData = new FormData(form);

        fetch(form.action, {
            method: form.method,
            body: formData,
            headers: {
                'Accept': 'application/json'
            }
        })
        .then(response => {
            if (response.ok || response.status === 200) {
                showMessage('✅ Message sent successfully! We\'ll get back to you soon.', 'success');
                form.reset();
            } else {
                throw new Error('Form submission failed');
            }
        })
        .catch(error => {
            console.error('Form error:', error);
            showMessage('❌ Error sending message. Please try again.', 'error');
        })
        .finally(() => {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        });
    });

    function showMessage(text, type) {
        messageDiv.textContent = text;
        messageDiv.className = type;
        messageDiv.style.display = 'block';

        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    }
}

// ===== MODAL FUNCTIONS =====
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
}

// Close modal when clicking outside content or on close button
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        hideModal(e.target.id);
    }
    // Handle close button clicks
    if (e.target.classList.contains('modal-close') || e.target.closest('.modal-close')) {
        const modal = e.target.closest('.modal');
        if (modal) {
            hideModal(modal.id);
        }
    }
});

// ===== UTILITY FUNCTIONS =====
function resetAndReanimateSection(sectionSelector) {
    const section = document.querySelector(sectionSelector);
    if (section) {
        // Reset animations
        const elements = section.querySelectorAll('.processed');
        elements.forEach(el => {
            el.classList.remove('processed');
            el.style.animation = 'none';
            el.offsetHeight; // Trigger reflow
        });

        // Re-trigger animations
        setTimeout(() => {
            const headlines = section.querySelectorAll('h1, h2');
            headlines.forEach(headline => processElement(headline));

            const paragraphs = section.querySelectorAll('p');
            paragraphs.forEach(p => {
                if (!p.closest('.widget-content')) {
                    processElement(p);
                }
            });

            const buttons = section.querySelectorAll('.cta-button');
            buttons.forEach(button => {
                if (!button.closest('.action-widget')) {
                    processButton(button);
                }
            });
        }, 50);
    }
}

// ===== GLOBAL EXPORTS =====
window.showModal = showModal;
window.hideModal = hideModal;
window.resetAndReanimateSection = resetAndReanimateSection;

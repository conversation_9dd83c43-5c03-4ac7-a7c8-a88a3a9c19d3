<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery with Varied Grid Layout</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Hanken Grotesk', sans-serif;
            background-color: #000000;
            color: #ffffff;
            padding: 40px 20px;
        }

        /* Typography */
        h2 {
            font-family: 'Syncopate', sans-serif;
            font-weight: 700;
            text-transform: uppercase;
            font-size: clamp(1.8rem, 5vw, 4rem);
            letter-spacing: -0.05em;
            line-height: 0.9;
            margin-bottom: 30px;
            text-align: left;
        }

        .gallery-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Varied Grid Layout */
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            grid-auto-rows: 200px;
        }

        .gallery-item {
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease;
            border: 2px solid transparent;
        }

        /* Different sizes for variety */
        .gallery-item.size-small {
            grid-row: span 1;
            grid-column: span 1;
        }

        .gallery-item.size-medium {
            grid-row: span 2;
            grid-column: span 1;
        }

        .gallery-item.size-large {
            grid-row: span 2;
            grid-column: span 2;
        }

        .gallery-item.size-wide {
            grid-row: span 1;
            grid-column: span 2;
        }

        .gallery-item.size-tall {
            grid-row: span 3;
            grid-column: span 1;
        }

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .gallery-item:hover {
            transform: scale(1.02);
            border-color: #ffffff;
        }

        .gallery-item:hover img {
            transform: scale(1.1);
        }

        /* Hover overlay */
        .gallery-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .gallery-item:hover::after {
            opacity: 1;
        }

        /* Controls */
        .controls {
            margin-bottom: 30px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .controls button {
            background: #2ecc71;
            color: white;
            border: none;
            padding: 10px 15px;
            cursor: pointer;
            font-size: 14px;
        }

        .controls button:hover {
            background: #27ae60;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .gallery-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 10px;
                grid-auto-rows: 150px;
            }

            /* Simplify sizes on mobile */
            .gallery-item.size-large,
            .gallery-item.size-wide {
                grid-row: span 1;
                grid-column: span 2;
            }

            .gallery-item.size-tall {
                grid-row: span 2;
                grid-column: span 1;
            }
        }
    </style>
</head>
<body>
    <div class="gallery-container">
        <h2>OUR WORK</h2>
        
        <div class="controls">
            <button onclick="shuffleLayout()">Shuffle Layout</button>
            <button onclick="resetLayout()">Reset Layout</button>
            <button onclick="toggleAnimation()">Toggle Animations</button>
        </div>

        <div class="gallery-grid" id="gallery">
            <!-- Gallery items will be generated by JavaScript -->
        </div>
    </div>

    <script>
        // Sample image data (using placeholder images)
        const images = [
            'https://picsum.photos/400/400?random=1',
            'https://picsum.photos/400/600?random=2',
            'https://picsum.photos/600/400?random=3',
            'https://picsum.photos/400/400?random=4',
            'https://picsum.photos/400/800?random=5',
            'https://picsum.photos/800/400?random=6',
            'https://picsum.photos/400/400?random=7',
            'https://picsum.photos/400/600?random=8',
            'https://picsum.photos/400/400?random=9',
            'https://picsum.photos/600/600?random=10',
            'https://picsum.photos/400/400?random=11',
            'https://picsum.photos/400/400?random=12',
            'https://picsum.photos/800/400?random=13',
            'https://picsum.photos/400/400?random=14',
            'https://picsum.photos/400/600?random=15',
            'https://picsum.photos/400/400?random=16',
            'https://picsum.photos/400/400?random=17',
            'https://picsum.photos/400/800?random=18'
        ];

        // Size classes for variety
        const sizeClasses = [
            'size-small', 'size-small', 'size-small', 'size-small', // More small items
            'size-medium', 'size-medium', // Some medium items
            'size-large', // Fewer large items
            'size-wide', 'size-wide', // Some wide items
            'size-tall' // Few tall items
        ];

        let animationsEnabled = true;

        function createGalleryItem(imageSrc, index) {
            const item = document.createElement('div');
            item.className = 'gallery-item';
            
            // Assign a size class (cycle through available sizes)
            const sizeClass = sizeClasses[index % sizeClasses.length];
            item.classList.add(sizeClass);
            
            const img = document.createElement('img');
            img.src = imageSrc;
            img.alt = `Gallery image ${index + 1}`;
            img.loading = 'lazy';
            
            item.appendChild(img);
            
            // Add click handler
            item.addEventListener('click', () => {
                console.log(`Clicked image ${index + 1}`);
                // Here you would open the modal
            });
            
            return item;
        }

        function generateGallery() {
            const gallery = document.getElementById('gallery');
            gallery.innerHTML = '';
            
            images.forEach((imageSrc, index) => {
                const item = createGalleryItem(imageSrc, index);
                gallery.appendChild(item);
            });
        }

        function shuffleLayout() {
            const gallery = document.getElementById('gallery');
            const items = Array.from(gallery.children);
            
            // Shuffle the size classes
            const shuffledSizes = [...sizeClasses].sort(() => Math.random() - 0.5);
            
            items.forEach((item, index) => {
                // Remove existing size classes
                item.classList.remove('size-small', 'size-medium', 'size-large', 'size-wide', 'size-tall');
                
                // Add new size class
                const newSizeClass = shuffledSizes[index % shuffledSizes.length];
                item.classList.add(newSizeClass);
            });
        }

        function resetLayout() {
            generateGallery();
        }

        function toggleAnimation() {
            animationsEnabled = !animationsEnabled;
            const gallery = document.getElementById('gallery');
            
            if (animationsEnabled) {
                gallery.style.transition = '';
                document.querySelectorAll('.gallery-item').forEach(item => {
                    item.style.transition = 'transform 0.3s ease';
                });
            } else {
                gallery.style.transition = 'none';
                document.querySelectorAll('.gallery-item').forEach(item => {
                    item.style.transition = 'none';
                });
            }
        }

        // Initialize gallery
        generateGallery();

        // Add some entrance animations
        setTimeout(() => {
            document.querySelectorAll('.gallery-item').forEach((item, index) => {
                setTimeout(() => {
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(20px)';
                    item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 50);
                }, index * 100);
            });
        }, 100);
    </script>
</body>
</html>

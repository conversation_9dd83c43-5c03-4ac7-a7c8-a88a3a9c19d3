/* CSS Variables for better maintainability */
:root {
    /* Colors */
    --color-background: #1a1a1a;
    --color-text: #ffffff;
    --color-primary: #8781bd; /* Purple accent color */
    --color-secondary: #FF42A1; /* Pink accent color */
    --color-dark: #000000;
    --color-light: #ffffff;
    --color-gray: #333333;
    --color-light-gray: #eeeeee;

    /* Section Colors */
    --color-about-bg: #3498db; /* Bright blue */
    --color-gallery-bg: #000000; /* Black */
    --color-contact-bg: #2ecc71; /* Bright green */

    /* Spacing */
    --spacing-xs: 10px;
    --spacing-sm: 20px;
    --spacing-md: 40px;
    --spacing-lg: 60px;
    --spacing-xl: 100px;

    /* Animation Timing */
    --timing-fast: 0.3s;
    --timing-medium: 0.5s;
    --timing-slow: 0.8s;

    /* Z-index layers */
    --z-background: 0;
    --z-content: 5;
    --z-sections: 10;
    --z-navbar: 9999;
}

/* Navbar Styles */
nav {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 160px);
    max-width: 1200px;
    height: 44px; /* Decreased from 52px to 44px (additional 8px reduction) */
    background-color: var(--color-light);
    z-index: var(--z-navbar); /* Increased z-index to ensure it stays above all elements */
    padding: 0 var(--spacing-sm);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    outline: none;
    border: none;
    box-sizing: content-box;
    overflow: visible;
}

.navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
}

.hamburger-icon {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px; /* Reduced padding */
    z-index: 1001;
}

.hamburger-icon span {
    display: block;
    width: 22px; /* Slightly smaller width */
    height: 2px; /* Thinner lines */
    background-color: var(--color-dark);
    margin: 4px 0; /* Reduced margin */
    transition: all var(--timing-fast) ease-in-out;
}

@media (max-width: 768px) {
    .hamburger-icon {
        display: block;
    }

    .nav-links {
        display: none;
        position: fixed;
        top: 44px; /* Updated to match new navbar height */
        left: 0;
        right: 0;
        background-color: #ffffff;
        padding: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        z-index: 9998; /* High z-index to ensure visibility */
    }

    .nav-links.active {
        display: block;
    }

    .hamburger-icon.active span:nth-child(1) {
        transform: translateY(6px) rotate(45deg); /* Adjusted for smaller spacing */
    }

    .hamburger-icon.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger-icon.active span:nth-child(3) {
        transform: translateY(-6px) rotate(-45deg); /* Adjusted for smaller spacing */
    }
}

.site-logo {
    height: 100%;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo-container {
    position: relative;
    padding: 12px; /* Further reduced from 16px to fit even smaller navbar */
}

.logo-image {
    position: absolute;
    top: 50%;
    left: 0;
    height: 30px; /* Increased from 26px to 30px (4px increase) */
    width: auto;
    transform: translateY(-50%);
    transition: clip-path 0.25s cubic-bezier(0.4, 0, 0.05, 1);
}

.logo-1 {
    clip-path: inset(0 0 0 0);
}

.logo-2 {
    clip-path: inset(0 100% 0 0);
}

.logo-container:hover .logo-1 {
    clip-path: inset(0 0 0 100%);
}

.logo-container:hover .logo-2 {
    clip-path: inset(0 0 0 0);
}

@media (min-width: 769px) {
    .nav-links a {
        color: var(--color-dark);
        text-decoration: none;
        font-size: 0.775em; /* Decreased by approximately 2 points (from ~14.4px to ~12.4px) */
        position: relative;
        padding: 4px 8px;
        transition: color var(--timing-fast) cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover {
        color: var(--color-light);
    }

    .nav-links a::after {
        content: '';
        position: absolute;
        top: 0;
        left: -5px;
        width: calc(100% + 10px); /* Text width plus 5px on each side */
        height: 100%;
        background-color: #00A2FF; /* Blue background for menu hover */
        z-index: -1;
        transform: scaleX(0);
        transform-origin: left;
        transition: transform var(--timing-fast) cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover::after {
        transform: scaleX(1);
    }
}

@media (max-width: 768px) {
    .nav-links.active {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .nav-links a {
        display: inline-block;
        width: auto;
        text-align: center;
        margin: 10px 0;
        padding: 8px 12px;
        color: #000000;
        text-decoration: none;
        font-size: 0.875em;
        position: relative;
    }

    .nav-links a:hover {
        color: #ffffff;
    }

    .nav-links a::after {
        content: '';
        position: absolute;
        top: 0;
        left: -5px;
        width: calc(100% + 10px);
        height: 100%;
        background-color: #00A2FF;
        z-index: -1;
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.25s cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover::after {
        transform: scaleX(1);
    }
}/* Hero Section Styles */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

.hero-slide.active .hero-background-image {
    animation: scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards;
}

@keyframes scaleUpHero {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.25);
    }
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(135, 129, 189, 0.85);
    z-index: 2;
}

.hero-content-container {
    position: absolute;
    top: 50%; /* Center vertically */
    left: 50%;
    transform: translate(-50%, -50%); /* Center both horizontally and vertically */
    width: calc(100% - 160px);
    max-width: 1200px;
    padding: 0;
    z-index: 10; /* Increase z-index to ensure visibility */
}

.hero-content {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 0; /* Remove logo container padding margin */
    max-width: 600px; /* Reduced from 800px to prevent headline from running too long */
    padding: 0;
    text-align: left;
    color: #ffffff;
    z-index: 10; /* Ensure it's above other elements */
}

/* Hero headline style already defined in the global styles */



.hero-content .subheadline {
    max-width: 600px; /* Match headline width constraint */
    margin-bottom: 25px; /* Space before button */
}

.hero-content .subheadline p {
    font-family: 'Hanken Grotesk', sans-serif; /* Using Hanken Grotesk as substitute for Armin Grotesk */
    font-size: 1.2rem; /* Restored larger font size */
    font-weight: 700; /* Bold weight */
    letter-spacing: 0.15em; /* Slightly looser kerning */
    line-height: 1.4; /* Line height */
    margin: 0 0 5px 0; /* Small gap between lines, no bottom margin on last item */
    display: block; /* Ensure each paragraph is on its own line */
}

/* Hero section CTA button with exact subheadline styling */
.hero-content .cta-button:not(.action-widget-btn) {
    display: inline-block;
    margin-top: 0; /* Removed margin since p already has margin-bottom */
    background-color: #FF42A1; /* Pink background - hardcoded to keep it pink */
    color: #ffffff; /* White text */
    padding: 15px 30px; /* Normal button size */
    border-radius: 0; /* Sharp corners */
    text-decoration: none;
    border: none;
    cursor: pointer;

    /* Exact subheadline font styling */
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.2rem; /* Matching the subheadline size */
    font-weight: 700;
    line-height: 1.4;
    letter-spacing: 0.15em;
    text-transform: uppercase;
}

/* Contact Section Styles */
.contact-section {
    position: relative;
    z-index: calc(var(--z-sections) + 2); /* Higher than gallery section */
    background-color: var(--color-contact-bg); /* Bright green */
    transform: translateZ(0); /* Force hardware acceleration */
    padding: var(--spacing-xl) var(--spacing-md);
}

/* Spacer for fixed navbar */
.sticky-navbar-spacer {
    height: 0; /* No need for spacer since we're using fixed positioning */
    width: 100%;
}/*
==============================================
TYPOGRAPHY STYLES
==============================================
*/

/* Global headline styles */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Syncopate', sans-serif;
    font-weight: 700;
    text-transform: uppercase;
}

/*
Responsive Typography with Clamp
--------------------------------
clamp(min, preferred, max)
- min: smallest size (for small screens)
- preferred: size that scales with viewport
- max: largest size (for large screens)
*/

/* Heading Styles */
h1, h2 {
    /* All headlines use the smaller size (previously used for section headings) */
    font-size: clamp(1.8rem, 5vw, 4rem);
    /* Consistent kerning */
    letter-spacing: -0.05em;
    /* Consistent leading */
    line-height: 0.9;
    margin-bottom: 25px;
}

/* H3 - Subsection Headings */
h3 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.8rem, 5vw, 4rem);
    /* Consistent kerning */
    letter-spacing: -0.05em;
    /* Consistent leading */
    line-height: 0.9;
    margin-bottom: 25px;
}

/* H4 - Minor Headings */
h4 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.5rem, 4vw, 3.2rem);
    /* Consistent kerning */
    letter-spacing: -0.05em;
    /* Consistent leading */
    line-height: 0.9;
    margin-bottom: 20px;
}

/* H5 - Small Headings */
h5 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.3rem, 3vw, 2.5rem);
    /* Consistent kerning */
    letter-spacing: -0.05em;
    /* Consistent leading */
    line-height: 0.9;
    margin-bottom: 15px;
}

/* H6 - Smallest Headings */
h6 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.1rem, 2vw, 2rem);
    /* Consistent kerning */
    letter-spacing: -0.05em;
    /* Consistent leading */
    line-height: 0.9;
    margin-bottom: 15px;
}

/* Body text styles */
body {
    font-family: 'Hanken Grotesk', sans-serif;
    line-height: 1.4; /* Tightened leading for body text */
}

/* Menu styles - base size for all text */
.nav-links a {
    font-family: 'Hanken Grotesk', sans-serif;
    font-weight: 500;
    font-size: 0.775rem; /* Exact menu text size */
    letter-spacing: 0.01em; /* Consistent kerning */
    line-height: 1.2; /* Consistent leading */
}

/* Paragraph and subheadline styles - same as menu text but bold */
p {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.775rem; /* Exact same as menu text */
    line-height: 1.2; /* Consistent leading */
    font-weight: 700; /* Bold weight */
    letter-spacing: 0.15em; /* Slightly looser kerning */
    margin-bottom: 20px;
}

/* Button styles - match subheadline */
button:not(.action-widget-btn),
.cta-button:not(.action-widget-btn) {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.2rem !important; /* Match subheadline size */
    font-weight: 700; /* Bold weight */
    letter-spacing: 0.15em; /* Match subheadline kerning */
    line-height: 1.4; /* Match subheadline line height */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .nav-links a,
    p {
        font-size: 0.775rem; /* Maintain consistent size */
        letter-spacing: 0.15em; /* Maintain slightly looser kerning on mobile */
    }

    button:not(.action-widget-btn),
    .cta-button:not(.action-widget-btn) {
        font-size: 1.2rem !important; /* Match subheadline size even on mobile */
    }
}/*
==============================================
ANIMATIONS
==============================================
*/

/* Hero image scale-up animation */
@keyframes scaleUpHero {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.25);
    }
}

/* Slot animation styles - optimized for capital letters */
.slot-mask {
    display: inline-block;
    overflow: hidden;
    vertical-align: bottom;
    height: 1em; /* Optimized for capital letters */
    position: relative; /* Ensure proper stacking */
}

/* For headline */
.heading-mask,
h1 .slot-mask, h2 .slot-mask, h3 .slot-mask, h4 .slot-mask, h5 .slot-mask, h6 .slot-mask {
    height: 1em;
}

/* For subheadline */
.paragraph-mask,
p .slot-mask {
    height: 1em;
}

/* For button */
.button-mask {
    display: inline-block;
    overflow: hidden;
    vertical-align: bottom;
    height: auto; /* Auto height to accommodate any button size */
    margin-top: 0;
    margin-bottom: 0;
    position: relative; /* Ensure proper stacking */
}

/* Ensure button has no margins inside the mask */
.button-mask .cta-button:not(.action-widget-btn) {
    margin: 0;
    padding: 15px 30px; /* Normal button size */
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Add the hover effect to buttons in masks too */
.button-mask .cta-button:not(.action-widget-btn)::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    transition: all 0.3s ease;
    z-index: -1;
}

.button-mask .cta-button:not(.action-widget-btn):hover {
    color: #333333 !important;
}

.button-mask .cta-button:not(.action-widget-btn):hover::before {
    left: 0;
}

.slot-reel {
    display: block;
    transform: none; /* Remove initial transform to prevent flicker */
    line-height: inherit;
    position: relative; /* Ensure proper stacking */
    will-change: transform; /* Optimize for transform animations */
    opacity: 0; /* Start invisible */
    animation: slotSpin 0.8s cubic-bezier(0.19, 1, 0.22, 1) forwards;
}

/* Initial slot animation */
@keyframes slotSpin {
    0% { 
        transform: translateY(700%);
        opacity: 0;
    }
    15% { 
        transform: translateY(500%);
        opacity: 1;
    }
    30% { 
        transform: translateY(300%);
    }
    45% { 
        transform: translateY(100%);
    }
    60% { 
        transform: translateY(-50%);
    }
    75% { 
        transform: translateY(-20%);
    }
    85% { 
        transform: translateY(-10%);
    }
    100% { 
        transform: translateY(0);
        opacity: 1;
    }
}/* Gallery Styles */

/* Gallery header */
.gallery-header {
    text-align: left; /* Change to left-aligned */
    margin-bottom: 30px;
    width: 100%;
    max-width: 600px; /* Match headline width constraint */
}

.gallery-header h2 {
    margin-bottom: 20px;
    text-align: left; /* Ensure heading is left-aligned */
    max-width: 600px; /* Match headline width constraint */
}

.gallery-header p {
    margin-bottom: 0;
    text-align: left; /* Ensure paragraph is left-aligned */
    max-width: 600px; /* Match headline width constraint */
}

/* Gallery container */
.gallery-container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 20px 0;
    box-sizing: border-box;
}

/* Gallery container is responsive by default */

/* Import CSS variables from style.css */
:root {
    /* If these variables aren't defined in style.css, define them here */
    --color-dark: #000000;
    --color-light: #ffffff;
    --color-primary: #8781bd;
    --timing-fast: 0.3s;
    --timing-medium: 0.5s;
    --z-modal: 9000;
}

/* Gallery grid - 2:1 Rectangle Layout */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    width: 100%;
    gap: 12px;
    padding: 0;
    margin: 0;
    background-color: var(--color-dark);
}

/* Gallery item - Base styles */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 0; /* Square corners */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.1);
    aspect-ratio: 2/1; /* 2:1 aspect ratio */
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

/* Fade in revealed items */
.gallery-item.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Ensure images maintain aspect ratio and cover the container */
.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    opacity: 0;
    transition: opacity 0.3s ease-in;
}

/* Fade in revealed items */
.gallery-item.revealed img {
    opacity: 1;
}

/* Different sizes for variety */
.gallery-item.size-small {
    grid-row: span 1;
    grid-column: span 1;
}

.gallery-item.size-medium {
    grid-row: span 2;
    grid-column: span 1;
}

.gallery-item.size-large {
    grid-row: span 2;
    grid-column: span 2;
}

.gallery-item.size-wide {
    grid-row: span 1;
    grid-column: span 2;
}

.gallery-item.size-tall {
    grid-row: span 3;
    grid-column: span 1;
}

/* Hover effects for varied grid */
.gallery-item:hover {
    transform: scale(1.02);
    border-color: var(--color-light);
}

/* Hover effect - magnifying glass */
.gallery-item .magnify-icon {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 32px;
    height: 32px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/><path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 4;
    pointer-events: none;
}

/* Hover overlay */
.gallery-item .hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-primary);
    opacity: 0;
    transition: opacity var(--timing-fast) ease;
    z-index: 3;
    will-change: opacity; /* Performance optimization */
}

.gallery-item:hover .hover-overlay {
    opacity: 0.5;
}

.gallery-item:hover .magnify-icon {
    opacity: 1;
}

/* Selection overlay - removed to prevent unwanted highlighting */
.gallery-item::after {
    content: none; /* Disable the selection overlay */
}

/* Modal styles */
.gallery-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 99999;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    margin: 0 !important;
    padding: 0 !important;
    transform: none !important;
    box-sizing: border-box;
}

.gallery-modal.active {
    opacity: 1;
}

.modal-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 0;
}

.modal-image {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    transform: scale(0.8);
    opacity: 0;
    transition: transform 0.5s ease-out, opacity 0.5s ease-out;
}

.modal-image.active {
    transform: scale(1);
    opacity: 1;
}

.modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    width: 30px; /* Smaller size */
    height: 30px; /* Smaller size */
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1001;
    border: 1px solid white;
}

.modal-close::before,
.modal-close::after {
    content: '';
    position: absolute;
    width: 15px; /* Smaller X */
    height: 2px; /* Thinner lines */
    background-color: white;
}

.modal-close::before {
    transform: rotate(45deg);
}

.modal-close::after {
    transform: rotate(-45deg);
}

.modal-nav {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    pointer-events: none;
    z-index: 1001;
}

.modal-prev,
.modal-next {
    width: 40px; /* Smaller size */
    height: 40px; /* Smaller size */
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    pointer-events: auto;
    z-index: 1002;
    border: 1px solid white;
}

.modal-prev {
    margin-right: auto;
}

.modal-next {
    margin-left: auto;
}

.modal-prev::before,
.modal-next::before {
    content: '';
    width: 10px; /* Smaller arrow */
    height: 10px; /* Smaller arrow */
    border-style: solid;
    border-color: white;
    border-width: 0 0 2px 2px; /* Thinner border */
    display: inline-block;
}

.modal-prev::before {
    transform: rotate(45deg);
    margin-left: 5px;
}

.modal-next::before {
    transform: rotate(-135deg);
    margin-right: 5px;
}

/* Animation styles */
/* Base class for zoom-pan animations */
.animate-zoom-pan {
    position: relative;
    overflow: hidden;
}

/* Animation for circular pan */
.animate-zoom-pan-circular img {
    animation: zoomPanCircular 5.5s ease-in-out;
    transform-origin: center;
    will-change: transform;
}

/* Animation for left-side pan */
.animate-zoom-pan-left img {
    animation: zoomPanLeft 5.5s ease-in-out;
    transform-origin: center;
    will-change: transform;
}

/* Animation for right-side pan */
.animate-zoom-pan-right img {
    animation: zoomPanRight 5.5s ease-in-out;
    transform-origin: center;
    will-change: transform;
}

/* Fade-in scaled animations */
.animate-fade-scale {
    position: relative;
    overflow: hidden;
}

/* Fade-in scaled animation container */
.animate-fade-scale .fade-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-

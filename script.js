/**
 * Artchop Website Animations and Functionality
 * - Hero Image Animation
 * - Text Intro Animations
 * - Carousel Functionality
 * - Smooth Scrolling
 */

// Number of items to show in each reel
const REEL_ITEMS = 7;

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing animations');

    // First, process hero section animations
    initHeroSectionAnimations();

    // Then initialize other functionality with a small delay
    setTimeout(() => {
        initHeroAnimation();
        initHamburgerMenu();
        initCarousel();
        initSmoothScrolling();
        initScrollBasedAnimations();
    }, 100); // Small delay to ensure animations are processed first
});

/**
 * Initialize hero section text animations
 */
function initHeroSectionAnimations() {
    console.log('Initializing hero section animations');

    // Process hero section headlines
    const heroHeadlines = document.querySelectorAll('.hero-content h1, .hero-content h2');
    console.log(`Found ${heroHeadlines.length} hero headlines`);
    heroHeadlines.forEach(headline => {
        // Always process the headline to ensure it's animated
        processElement(headline);
    });
    
    // Process section headlines
    const sectionHeadlines = document.querySelectorAll('.section h2, .about-section h2, .about-content h2');
    console.log(`Found ${sectionHeadlines.length} section headlines`);
    sectionHeadlines.forEach(headline => {
        // Always process the headline to ensure it's animated
        processElement(headline);
    });

    // Process hero section subheadlines
    const heroSubheadlines = document.querySelectorAll('.hero-content p');
    console.log(`Found ${heroSubheadlines.length} hero subheadlines`);
    heroSubheadlines.forEach(subheadline => {
        if (!subheadline.classList.contains('processed')) {
            processElement(subheadline);
        }
    });

    // Process hero section buttons
    const heroButtons = document.querySelectorAll('.hero-content .cta-button');
    console.log(`Found ${heroButtons.length} hero buttons`);
    heroButtons.forEach(button => {
        if (!button.classList.contains('processed')) {
            processButton(button);
        }
    });
}

/**
 * Initialize scroll-based animations using Intersection Observer
 */
function initScrollBasedAnimations() {
    console.log('Initializing scroll-based animations');

    // Create sections array
    const sections = [
        {
            id: 'about',
            selector: '.about-content',
            processed: false,
            sectionClass: 'about-section' // Add this to identify the section element
        },
        {
            id: 'about-2',
            selector: '.about-content',
            processed: false,
            sectionClass: 'about-section'
        },
        {
            id: 'about-3',
            selector: '.about-content',
            processed: false,
            sectionClass: 'about-section'
        },
        {
            id: 'gallery',
            selector: '.gallery-content',
            processed: false
        },
        {
            id: 'contact',
            selector: '.contact-content',
            processed: false
        }
    ];

    // Create intersection observer with lower threshold
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const sectionId = entry.target.id;
            const section = sections.find(s => s.id === sectionId);
            
            if (section) {
                if (entry.isIntersecting) {
                    // Always add active class when section comes into view
                    if (section.sectionClass) {
                        entry.target.classList.add('active');
                    }
                    
                    // Special handling for gallery section
                    if (sectionId === 'gallery' && window.initGallery) {
                        console.log('Gallery section in view, resetting and playing animations');
                        if (window.resetGalleryAnimations) {
                            window.resetGalleryAnimations();
                        }
                        setTimeout(() => {
                            window.initGallery();
                        }, 50);
                    }
                    
                    // Always process elements when section comes into view
                    console.log(`Section ${sectionId} is now visible, animating elements`);

                    // Process section headlines
                    const sectionHeadlines = entry.target.querySelectorAll('h1, h2');
                    sectionHeadlines.forEach(headline => {
                        // Reset animation before reprocessing
                        headline.style.animation = 'none';
                        headline.offsetHeight; // Trigger reflow
                        processElement(headline);
                    });

                    // Process section subheadlines (exclude widget content)
                    const allParagraphs = entry.target.querySelectorAll('p');
                    const sectionSubheadlines = Array.from(allParagraphs).filter(p =>
                        !p.closest('.widget-content')
                    );
                    sectionSubheadlines.forEach(subheadline => {
                        // Reset animation before reprocessing
                        subheadline.style.animation = 'none';
                        subheadline.offsetHeight; // Trigger reflow
                        processElement(subheadline);
                    });

                    // Process section buttons (exclude action widget buttons)
                    const sectionButtons = entry.target.querySelectorAll('.cta-button:not(.action-widget-btn)');
                    sectionButtons.forEach(button => {
                        if (!button.closest('.action-widget')) {
                            // Reset animation before reprocessing
                            button.style.animation = 'none';
                            button.offsetHeight; // Trigger reflow
                            processButton(button);
                        }
                    });
                } else if (section.sectionClass) {
                    // Remove active class when section goes out of view
                    entry.target.classList.remove('active');
                }
            }
        });
    }, {
        threshold: 0.1, // Lower threshold to 10% for more reliable triggering
        rootMargin: '0px 0px -50px 0px' // Slight negative margin to trigger a bit before element enters viewport
    });

    // Observe each section
    sections.forEach(section => {
        const sectionElement = document.getElementById(section.id);
        if (sectionElement) {
            observer.observe(sectionElement);
        }
    });
}

/**
 * Process text element for animation
 * @param {HTMLElement} element - The element to process
 */
function processElement(element) {
    console.log(`Processing element: ${element.tagName} with text: ${element.textContent.substring(0, 20)}...`);

    // Skip if already processed
    if (element.classList.contains('processed')) {
        console.log('Element already processed, skipping');
        return;
    }

    try {
        // Store original text
        const originalText = element.textContent;

        // Mark as processed
        element.classList.add('processed');
        element.dataset.originalText = originalText;

        // Split into words
        const words = originalText.split(/\s+/).filter(word => word.trim() !== '');
        console.log(`Processing ${words.length} words`);

        if (words.length === 0) {
            console.log('No words to process, skipping');
            return;
        }

        // Clear the element
        element.innerHTML = '';

        // Process each word
        words.forEach((word, wordIndex) => {
            // Create slot mask
            const slotMask = document.createElement('span');
            slotMask.className = 'slot-mask';

            // Add specific class for element type
            if (element.tagName.match(/^H[1-6]$/i)) {
                slotMask.classList.add('heading-mask');
            } else if (element.tagName.toLowerCase() === 'p') {
                slotMask.classList.add('paragraph-mask');
            }

            // Create slot reel
            const slotReel = document.createElement('div');
            slotReel.className = 'slot-reel';

            // Generate items for the reel - all showing the actual word
            for (let i = 0; i < REEL_ITEMS; i++) {
                const slotItem = document.createElement('div');
                slotItem.style.height = '1em'; // Optimized for capital letters
                slotItem.style.lineHeight = '1'; // Set to 1 for capital letters
                slotItem.textContent = word;
                slotReel.appendChild(slotItem);
            }

            // Create the final word
            const finalItem = document.createElement('div');
            finalItem.style.height = '1em'; // Optimized for capital letters
            finalItem.style.lineHeight = '1'; // Set to 1 for capital letters
            finalItem.textContent = word;

            slotReel.appendChild(finalItem);

            // Set animation delay - staggered sequence
            const delay = 0.05 + (wordIndex * 0.08);
            slotReel.style.animationDelay = `${delay}s`;

            // Assemble and add to element
            slotMask.appendChild(slotReel);
            element.appendChild(slotMask);

            // Add space after word (except last word)
            if (wordIndex < words.length - 1) {
                element.appendChild(document.createTextNode(' '));
            }
        });
    } catch (error) {
        console.error('Error processing element:', error);
        // Restore original content if there was an error
        element.textContent = originalText;
        element.classList.remove('processed');
    }
}

/**
 * Process button for animation
 * @param {HTMLElement} buttonElement - The button element to process
 */
function processButton(buttonElement) {
    console.log(`Processing button: ${buttonElement.textContent}`);

    // Skip if already processed or not in document
    if (!document.body.contains(buttonElement)) {
        console.log('Button not in document, skipping');
        return;
    }

    // Skip if already processed
    if (buttonElement.classList.contains('processed')) {
        console.log('Button already processed, skipping');
        return;
    }

    try {
        // Store original button properties
        const originalText = buttonElement.textContent;
        const originalHref = buttonElement.getAttribute('href') || '#';

        // Mark as processed
        buttonElement.classList.add('processed');
        buttonElement.dataset.originalText = originalText;

        // Create a clone of the button to use for animation
        const buttonClone = buttonElement.cloneNode(true);

        // Create button mask
        const buttonMask = document.createElement('div');
        buttonMask.className = 'button-mask';
        buttonMask.dataset.originalText = originalText;
        buttonMask.dataset.originalHref = originalHref;

        // Create slot reel for button
        const buttonReel = document.createElement('div');
        buttonReel.className = 'slot-reel';

        class ModalSystem {
            constructor() {
                this.modalContainer = document.getElementById('modalContainer');
                this.currentModal = null;
                this.init();
            }

            init() {
                // Handle link clicks for modals
                document.addEventListener('click', (e) => {
                    // Check if clicked element or its parent has data-modal attribute
                    let link = e.target.closest('[data-modal]');
                    if (!link && e.target.matches('.terms-link')) {
                        link = e.target;
                    }
                    
                    if (link) {
                        e.preventDefault();
                        const modalName = link.getAttribute('data-modal');
                        if (modalName) {
                            this.loadModal(modalName);
                        }
                    }
                });

                // Close modal when clicking outside content
                this.modalContainer.addEventListener('click', (e) => {
                    if (e.target === this.modalContainer) {
                        this.closeModal();
                    }
                });

                // Close with Escape key
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.modalContainer.classList.contains('active')) {
                        this.closeModal();
                    }
                });

                // Handle form submission
                const contactForm = document.getElementById('contact-form');
                if (contactForm) {
                    // Do nothing, allow default form submission
                }
            }

            async loadModal(modalName) {
                try {
                    const response = await fetch(`modals/${modalName}.html`);
                    if (!response.ok) throw new Error('Modal not found');
                    
                    const html = await response.text();
                    this.modalContainer.innerHTML = html;
                    this.modalContainer.classList.add('active');
                    document.body.style.overflow = 'hidden';
                    
                    // Set up close button
                    const closeBtn = this.modalContainer.querySelector('.close-modal');
                    if (closeBtn) {
                        closeBtn.addEventListener('click', () => this.closeModal());
                    }

                    // Set up any modal-specific buttons
                    const closeMessageBtn = this.modalContainer.querySelector('#closeTermsMessage, #closeSuccessMessage');
                    if (closeMessageBtn) {
                        closeMessageBtn.addEventListener('click', () => this.closeModal());
                    }

                    // Store reference to current modal
                    this.currentModal = modalName;
                    
                } catch (error) {
                    console.error('Error loading modal:', error);
                }
            }

            closeModal() {
                this.modalContainer.classList.remove('active');
                document.body.style.overflow = 'auto';
                this.currentModal = null;
            }

            async handleFormSubmit(e) {
                e.preventDefault();
                
                const termsCheckbox = document.getElementById('terms');
                
                if (!termsCheckbox.checked) {
                    // Show terms acceptance modal
                    await this.loadModal('accept-terms');
                    return false;
                }
                
                // Here you would typically submit the form via AJAX
                // For now, we'll just show the success message
                this.loadModal('message-sent');
                
                // Reset form
                e.target.reset();
                return false;
            }
        }

        // Store scroll position during resize
        let windowWidth = window.innerWidth;
        window.addEventListener('resize', function() {
            if (window.innerWidth !== windowWidth) {
                const scrollPosition = window.scrollY;
                requestAnimationFrame(() => {
                    window.scrollTo(0, scrollPosition);
                });
                windowWidth = window.innerWidth;
            }
        });

        // Initialize modal system when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.modalSystem = new ModalSystem();
        });

        buttonReel.appendChild(buttonClone);

        // Set animation delay and duration
        const delay = 0.5;
        const duration = 0.8;

        // Apply the animation to the entire button
        buttonReel.style.animation = `slotSpin ${duration}s cubic-bezier(0.19, 1, 0.22, 1) forwards`;
        buttonReel.style.animationDelay = `${delay}s`;

        // Assemble and add to container
        buttonMask.appendChild(buttonReel);

        // Replace the original button with the animated one
        const parentContainer = buttonElement.parentNode;
        parentContainer.replaceChild(buttonMask, buttonElement);

        // Add click event to the cloned button
        buttonClone.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            if (targetId && targetId.startsWith('#')) {
                const target = document.querySelector(targetId);
                if (target) {
                    const targetPosition = target.getBoundingClientRect().top + window.scrollY;
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            } else if (originalHref) {
                window.location.href = originalHref;
            }
        });

        console.log('Button processed successfully');
    } catch (error) {
        console.error('Error processing button:', error);
        // Don't modify the button if there was an error
        buttonElement.classList.remove('processed');
    }
}

/**
 * Initialize hero image animation
 */
function initHeroAnimation() {
    // Add hero image animation to the active slide
    const activeSlide = document.querySelector('.hero-slide.active');
    if (activeSlide) {
        const heroImage = activeSlide.querySelector('.hero-background-image');
        if (heroImage) {
            // Reset any existing animation
            heroImage.style.animation = 'none';
            void heroImage.offsetWidth; // Force reflow

            // Apply the animation
            heroImage.style.animation = 'scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards';
        }
    }
}

/**
 * Initialize hamburger menu functionality
 */
function initHamburgerMenu() {
    const hamburger = document.querySelector('.hamburger-icon');
    const navLinks = document.querySelector('.nav-links');

    if (hamburger && navLinks) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navLinks.classList.toggle('active');
        });
    }
}

/**
 * Initialize carousel functionality
 */
function initCarousel() {
    const slides = document.querySelectorAll('.hero-slide');
    if (slides.length <= 1) return;

    let currentSlide = 0;
    const totalSlides = slides.length;
    const transitionTime = 5000; // 5 seconds

    // Auto-advance function
    function autoAdvance() {
        slides[currentSlide].classList.remove('active');
        currentSlide = (currentSlide + 1) % totalSlides;
        slides[currentSlide].classList.add('active');

        const heroImage = slides[currentSlide].querySelector('.hero-background-image');
        if (heroImage) {
            heroImage.style.animation = 'none';
            void heroImage.offsetWidth; // Force reflow
            heroImage.style.animation = 'scaleUpHero 10s cubic-bezier(0.25, 0.1, 0.25, 1.0) forwards';
        }
    }

    // Start auto-advancing
    let autoAdvanceInterval = setInterval(autoAdvance, transitionTime);

    // Pause on hover
    const heroCarousel = document.querySelector('.hero-carousel');
    if (heroCarousel) {
        heroCarousel.addEventListener('mouseenter', () => {
            clearInterval(autoAdvanceInterval);
        });

        heroCarousel.addEventListener('mouseleave', () => {
            autoAdvanceInterval = setInterval(autoAdvance, transitionTime);
        });
    }
}

/**
 * Initialize smooth scrolling for navigation
 */
function initSmoothScrolling() {
    // Add click handler for logo to scroll to top
    const logo = document.querySelector('.site-logo');
    if (logo) {
        logo.addEventListener('click', function(e) {
            e.preventDefault();

            // Reset hero section animations
            resetAndReanimateSection('#hero');

            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Add click handlers for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const target = document.querySelector(targetId);

            if (target) {
                // Reset section animations when navigating to it
                resetAndReanimateSection(targetId);

                // Calculate target position
                const targetPosition = target.getBoundingClientRect().top + window.scrollY;
                
                // Scroll to the target
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Reset and reanimate a section
 * @param {string} sectionId - The ID of the section to reset and reanimate
 */
function resetAndReanimateSection(sectionId) {
    console.log(`Resetting and reanimating section: ${sectionId}`);

    // Get the section element
    const section = document.querySelector(sectionId);
    if (!section) return;

    // Find all animated elements in the section
    const animatedElements = section.querySelectorAll('.processed');

    // Reset each element
    animatedElements.forEach(element => {
        // If it's a button mask, handle differently
        if (element.classList.contains('button-mask')) {
            resetButton(element);
        }
        // If it has original text, restore it
        else if (element.dataset.originalText) {
            // Clear any existing animations
            if (window.gsap) {
                gsap.killTweensOf(element);
            }
            // Reset styles
            element.style.animation = 'none';
            element.style.opacity = '1';
            element.style.transform = '';
            element.style.webkitTransform = '';
            // Restore original content and remove processed class
            element.textContent = element.dataset.originalText;
            element.classList.remove('processed');
            // Force reflow
            void element.offsetHeight;
        }
    });

    // Find all slot masks and remove them
    const slotMasks = section.querySelectorAll('.slot-mask, .button-mask');
    slotMasks.forEach(mask => {
        const parent = mask.parentNode;
        if (parent) {
            // If it's a button mask, restore the button
            if (mask.classList.contains('button-mask')) {
                const originalText = mask.dataset.originalText;
                const originalHref = mask.dataset.originalHref || '#';

                // Create a new button
                const newButton = document.createElement('a');
                newButton.className = 'cta-button';
                newButton.href = originalHref;
                newButton.textContent = originalText || 'BUTTON';

                // Replace the mask with the new button
                parent.replaceChild(newButton, mask);
            } else {
                // Otherwise just remove the mask
                parent.removeChild(mask);
            }
        }
    });

    // Re-animate the section after a short delay
    setTimeout(() => {
        if (sectionId === '#hero') {
            initHeroSectionAnimations();
        } else {
            // First, process the main section heading if it exists
            const mainHeading = section.querySelector('h2');
            if (mainHeading) {
                // Force reset the main heading
                mainHeading.style.animation = 'none';
                mainHeading.style.opacity = '1';
                mainHeading.style.transform = '';
                mainHeading.style.webkitTransform = '';
                if (mainHeading.dataset.originalText) {
                    mainHeading.textContent = mainHeading.dataset.originalText;
                }
                mainHeading.classList.remove('processed');
                // Force reflow
                void mainHeading.offsetHeight;
                // Process the element
                processElement(mainHeading);
            }


            // Process section headlines - target h2 in about-content specifically
            const aboutContent = section.querySelector('.about-content');
            if (aboutContent) {
                const aboutHeadlines = aboutContent.querySelectorAll('h2');
                aboutHeadlines.forEach(headline => {
                    if (!headline.closest('.widget-content')) {
                        // Force reset the headline
                        headline.style.animation = 'none';
                        headline.style.opacity = '1';
                        headline.style.transform = '';
                        headline.style.webkitTransform = '';
                        if (headline.dataset.originalText) {
                            headline.textContent = headline.dataset.originalText;
                        }
                        headline.classList.remove('processed');
                        // Force reflow
                        void headline.offsetHeight;
                        // Process the element
                        processElement(headline);
                    }
                });
            }


            // Process other headlines in the section
            const sectionHeadlines = section.querySelectorAll('h1, h2');
            sectionHeadlines.forEach(headline => {
                if (!headline.closest('.widget-content') && !headline.closest('.about-content')) {
                    processElement(headline);
                }
            });

            // Process section subheadlines (exclude widget content)
            const allParagraphs = section.querySelectorAll('p');
            const sectionSubheadlines = Array.from(allParagraphs).filter(p =>
                !p.closest('.widget-content')
            );
            sectionSubheadlines.forEach(subheadline => {
                processElement(subheadline);
            });

            // Process section buttons
            const sectionButtons = section.querySelectorAll('.cta-button');
            sectionButtons.forEach(button => {
                processButton(button);
            });
        }
    }, 50); // Reduced delay for better UX
}

/**
 * Reset a button element
 * @param {HTMLElement} buttonMask - The button mask element to reset
 */
function resetButton(buttonMask) {
    try {
        const originalText = buttonMask.dataset.originalText;
        const originalHref = buttonMask.dataset.originalHref || '#';
        const parent = buttonMask.parentNode;

        if (parent) {
            // Create a new button
            const newButton = document.createElement('a');
            newButton.className = 'cta-button';
            newButton.href = originalHref;
            newButton.textContent = originalText || 'BUTTON';

            // Replace the mask with the new button
            parent.replaceChild(newButton, buttonMask);
        }
    } catch (error) {
        console.error('Error resetting button:', error);
    }
}

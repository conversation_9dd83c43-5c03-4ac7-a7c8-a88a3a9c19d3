<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Idle Headline</title>
    <link href="https://fonts.googleapis.com/css2?family=Syncopate:wght@400;700&family=Hanken+Grotesk:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            font-family: 'Hanken Grotesk', sans-serif;
            font-weight: 400;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
            line-height: 1.4;
        }
        
        .container {
            margin-bottom: 50px;
            text-align: center;
            max-width: 800px;
        }
        
        h1 {
            font-family: 'Syncopate', sans-serif;
            font-weight: 700;
            text-transform: uppercase;
            font-size: clamp(2.5rem, 7vw, 5.5rem);
            letter-spacing: -0.05em;
            line-height: 0.9;
            margin-bottom: 30px;
        }
        
        .subheadline {
            font-family: 'Hanken Grotesk', sans-serif;
            font-size: 1.2rem;
            line-height: 1.4;
            font-weight: 400;
            margin-bottom: 30px;
            letter-spacing: 0.01em;
        }
        
        .cta-button {
            display: inline-block;
            background-color: #ffffff;
            color: #1a1a1a;
            padding: 15px 30px;
            text-decoration: none;
            font-size: 1.1em;
            font-weight: 700;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s ease;
            letter-spacing: 0.02em;
            font-family: 'Hanken Grotesk', sans-serif;
        }
        
        .cta-button:hover {
            background-color: #eeeeee;
        }
        
        /* Slot animation styles */
        .slot-mask {
            display: inline-block;
            overflow: hidden;
            vertical-align: bottom;
            height: 1.2em;
        }
        
        /* For headline */
        h1 .slot-mask, h2 .slot-mask, h3 .slot-mask, h4 .slot-mask, h5 .slot-mask, h6 .slot-mask {
            height: 1.2em;
        }
        
        /* For subheadline */
        .subheadline .slot-mask, p .slot-mask {
            height: 1.2em;
        }
        
        /* For button - adjust height to match button */
        .button-mask {
            display: inline-block;
            overflow: hidden;
            vertical-align: bottom;
            height: 54px; /* Match button height (15px padding top/bottom + font size) */
        }
        
        .slot-reel {
            display: block;
            transform: translateY(700%);
        }
        
        /* Initial slot animation */
        @keyframes slotSpin {
            0% { transform: translateY(700%); }
            15% { transform: translateY(500%); }
            30% { transform: translateY(300%); }
            45% { transform: translateY(100%); }
            60% { transform: translateY(-50%); }
            75% { transform: translateY(-20%); }
            85% { transform: translateY(-10%); }
            100% { transform: translateY(0); }
        }
        
        /* Character styles for idle animations */
        .char {
            display: inline-block;
            position: relative;
            overflow: hidden;
            height: 1.2em;
            width: 0.7em;
            vertical-align: bottom;
            text-align: center;
        }
        
        /* Idle animations with different speeds */
        @keyframes idleScroll {
            0% { transform: translateY(0); }
            25% { transform: translateY(-100%); }
            26% { transform: translateY(100%); }
            50% { transform: translateY(0); }
            100% { transform: translateY(0); }
        }
        
        @keyframes idleScrollDouble {
            0% { transform: translateY(0); }
            15% { transform: translateY(-100%); }
            16% { transform: translateY(100%); }
            30% { transform: translateY(0); }
            45% { transform: translateY(-100%); }
            46% { transform: translateY(100%); }
            60% { transform: translateY(0); }
            100% { transform: translateY(0); }
        }
        
        @keyframes idleScrollTriple {
            0% { transform: translateY(0); }
            10% { transform: translateY(-100%); }
            11% { transform: translateY(100%); }
            20% { transform: translateY(0); }
            30% { transform: translateY(-100%); }
            31% { transform: translateY(100%); }
            40% { transform: translateY(0); }
            50% { transform: translateY(-100%); }
            51% { transform: translateY(100%); }
            60% { transform: translateY(0); }
            100% { transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 id="headline">CREATIVE PARTNER FOR GAMES</h1>
        <p id="subheadline" class="subheadline">Catchy sub-headline or description that matches your site's style.</p>
        <div id="button-container"></div>
    </div>
    
    <script>
        // Number of items to show in each reel
        const REEL_ITEMS = 7;
        
        // Store all character elements for idle animations
        const allCharElements = [];

        document.addEventListener('DOMContentLoaded', () => {
            // Process the headline
            const headline = document.getElementById('headline');
            processTextElement(headline, true); // Enable idle animation for headline
            
            // Process the subheadline
            const subheadline = document.getElementById('subheadline');
            processTextElement(subheadline, false); // No idle animation for subheadline
            
            // Process the button as a whole unit
            processButton();
            
            // Start idle animations after intro animations complete
            setTimeout(() => {
                startIdleAnimations();
            }, 3000); // Wait 3 seconds after page load
        });

        function processTextElement(element, enableIdle) {
            // Get the original text
            const originalText = element.textContent;
            
            // Split into words
            const words = originalText.split(/\s+/);
            
            // Clear the element
            element.innerHTML = '';
            
            // Process each word
            words.forEach((word, wordIndex) => {
                // Create slot mask
                const slotMask = document.createElement('span');
                slotMask.className = 'slot-mask';
                
                // Create slot reel
                const slotReel = document.createElement('div');
                slotReel.className = 'slot-reel';
                
                // Generate items for the reel - all showing the actual word
                for (let i = 0; i < REEL_ITEMS; i++) {
                    const slotItem = document.createElement('div');
                    slotItem.style.height = '1.2em';
                    slotItem.style.lineHeight = '1.2em';
                    slotItem.textContent = word;
                    slotReel.appendChild(slotItem);
                }
                
                // Create the final word with individual character containers for idle animations
                const finalItem = document.createElement('div');
                finalItem.style.height = '1.2em';
                finalItem.style.lineHeight = '1.2em';
                
                if (enableIdle) {
                    // Process each character in the word for idle animation
                    [...word].forEach(char => {
                        // Create character container for idle animations
                        const charContainer = document.createElement('span');
                        charContainer.className = 'char';
                        
                        // Create character content
                        const charContent = document.createElement('span');
                        charContent.textContent = char;
                        charContent.style.position = 'absolute';
                        charContent.style.width = '100%';
                        charContent.style.height = '100%';
                        charContent.style.top = '0';
                        charContent.style.left = '0';
                        
                        // Add content to container
                        charContainer.appendChild(charContent);
                        
                        // Store reference to character element for idle animations
                        allCharElements.push({
                            container: charContainer,
                            content: charContent,
                            originalChar: char
                        });
                        
                        finalItem.appendChild(charContainer);
                    });
                } else {
                    // Just add the word as text for elements without idle animation
                    finalItem.textContent = word;
                }
                
                slotReel.appendChild(finalItem);
                
                // Set animation delay - fast sequence
                const delay = 0.05 + (wordIndex * 0.08);
                const duration = 0.8;
                
                // Apply the animation
                slotReel.style.animation = `slotSpin ${duration}s cubic-bezier(0.19, 1, 0.22, 1) forwards`;
                slotReel.style.animationDelay = `${delay}s`;
                
                // Assemble and add to element
                slotMask.appendChild(slotReel);
                element.appendChild(slotMask);
                
                // Add space after word (except last word)
                if (wordIndex < words.length - 1) {
                    element.appendChild(document.createTextNode(' '));
                }
            });
        }
        
        function processButton() {
            // Create button container
            const buttonContainer = document.getElementById('button-container');
            
            // Create button mask
            const buttonMask = document.createElement('div');
            buttonMask.className = 'button-mask';
            
            // Create slot reel for button
            const buttonReel = document.createElement('div');
            buttonReel.className = 'slot-reel';
            
            // Create a single button
            const button = document.createElement('a');
            button.href = '#';
            button.className = 'cta-button';
            button.textContent = 'GET IN TOUCH';
            button.style.margin = '0'; // Remove margin for button
            
            // Add button to reel
            buttonReel.appendChild(button);
            
            // Set animation delay - after headline and subheadline
            const delay = 0.5; // Delay after headline and subheadline
            const duration = 0.8;
            
            // Apply the animation
            buttonReel.style.animation = `slotSpin ${duration}s cubic-bezier(0.19, 1, 0.22, 1) forwards`;
            buttonReel.style.animationDelay = `${delay}s`;
            
            // Assemble and add to container
            buttonMask.appendChild(buttonReel);
            buttonContainer.appendChild(buttonMask);
        }
        
        function startIdleAnimations() {
            // Run idle animations periodically
            idleAnimate();
        }
        
        function idleAnimate() {
            // Determine how many characters to animate (1-3)
            const howMany = Math.floor(Math.random() * 3) + 1;
            
            // Randomly select characters to animate
            const picked = [...allCharElements]
                .sort(() => 0.5 - Math.random()) // Shuffle array
                .slice(0, howMany);              // Take first few elements
            
            // Animate each selected character
            picked.forEach(charObj => {
                const content = charObj.content;
                
                // Skip if already animating
                if (content.dataset.animating === 'true') {
                    return;
                }
                
                // Mark as animating
                content.dataset.animating = 'true';
                
                // Determine animation parameters
                const scrollCount = Math.floor(Math.random() * 3) + 1; // 1-3 scrolls
                const duration = scrollCount === 1 ? 0.8 : (scrollCount === 2 ? 1.6 : 2.4);
                
                // Apply the animation using CSS
                if (scrollCount === 1) {
                    content.style.animation = `idleScroll ${duration}s cubic-bezier(0.19, 1, 0.22, 1)`;
                } else if (scrollCount === 2) {
                    content.style.animation = `idleScrollDouble ${duration}s cubic-bezier(0.19, 1, 0.22, 1)`;
                } else {
                    content.style.animation = `idleScrollTriple ${duration}s cubic-bezier(0.19, 1, 0.22, 1)`;
                }
                
                // Reset after animation completes
                setTimeout(() => {
                    content.style.animation = '';
                    content.dataset.animating = 'false';
                }, duration * 1000);
            });
            
            // Schedule next animation with random timing (1.2-2.4 seconds)
            const nextDelay = 1200 + Math.random() * 1200;
            setTimeout(() => {
                idleAnimate();
            }, nextDelay);
        }
    </script>
</body>
</html>

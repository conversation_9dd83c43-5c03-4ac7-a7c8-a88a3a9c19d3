<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intro Animation Only (No Idle)</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@700&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
        }
        
        .container {
            margin-bottom: 50px;
            text-align: center;
        }
        
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #333;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
        
        /* Slot animation styles */
        .slot-mask {
            display: inline-block;
            overflow: hidden;
            vertical-align: bottom;
            height: 1.2em;
        }
        
        .slot-reel {
            display: block;
            transform: translateY(700%);
        }
        
        /* Initial slot animation */
        @keyframes slotSpin {
            0% { transform: translateY(700%); }
            15% { transform: translateY(500%); }
            30% { transform: translateY(300%); }
            45% { transform: translateY(100%); }
            60% { transform: translateY(-50%); }
            75% { transform: translateY(-20%); }
            85% { transform: translateY(-10%); }
            100% { transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 id="headline">INTRO ANIMATION ONLY</h1>
        <a href="#" class="button" id="button">CLICK ME</a>
    </div>
    
    <script>
        // Number of items to show in each reel
        const REEL_ITEMS = 7;
        
        document.addEventListener('DOMContentLoaded', () => {
            // Process the headline
            const headline = document.getElementById('headline');
            processElement(headline);
            
            // Process the button
            const button = document.getElementById('button');
            processElement(button);
        });
        
        function processElement(element) {
            // Get the original text
            const originalText = element.textContent;
            
            // Split into words
            const words = originalText.split(/\s+/);
            
            // Clear the element
            element.innerHTML = '';
            
            // Process each word
            words.forEach((word, wordIndex) => {
                // Create slot mask
                const slotMask = document.createElement('span');
                slotMask.className = 'slot-mask';
                
                // Create slot reel
                const slotReel = document.createElement('div');
                slotReel.className = 'slot-reel';
                
                // Generate items for the reel - all showing the actual word
                for (let i = 0; i < REEL_ITEMS; i++) {
                    const slotItem = document.createElement('div');
                    slotItem.style.height = '1.2em';
                    slotItem.style.lineHeight = '1.2em';
                    slotItem.textContent = word;
                    slotReel.appendChild(slotItem);
                }
                
                // Create the final word
                const finalItem = document.createElement('div');
                finalItem.style.height = '1.2em';
                finalItem.style.lineHeight = '1.2em';
                finalItem.textContent = word;
                
                slotReel.appendChild(finalItem);
                
                // Set animation delay - fast sequence
                const delay = 0.05 + (wordIndex * 0.08);
                const duration = 0.8;
                
                // Apply the animation
                slotReel.style.animation = `slotSpin ${duration}s cubic-bezier(0.19, 1, 0.22, 1) forwards`;
                slotReel.style.animationDelay = `${delay}s`;
                
                // Assemble and add to element
                slotMask.appendChild(slotReel);
                element.appendChild(slotMask);
                
                // Add space after word (except last word)
                if (wordIndex < words.length - 1) {
                    element.appendChild(document.createTextNode(' '));
                }
            });
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Section with 3D Background</title>
    <!-- Include Three.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Hanken Grotesk', sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* Typography */
        h2 {
            font-family: 'Syncopate', sans-serif;
            font-weight: 700;
            text-transform: uppercase;
            font-size: clamp(1.8rem, 5vw, 4rem);
            letter-spacing: -0.05em;
            line-height: 0.9;
            margin-bottom: 25px;
        }

        /* 3D Background Canvas */
        #bg-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        /* Contact Section */
        .contact-section {
            position: relative;
            min-height: 100vh;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 100px 40px;
            z-index: 1;
        }

        .contact-content {
            max-width: 600px;
            text-align: left;
        }

        /* Optional Controls */
        .controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
        }

        .controls button {
            background: #2ecc71;
            color: white;
            border: none;
            padding: 5px 10px;
            margin: 0 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- 3D Background Canvas -->
    <canvas id="bg-canvas"></canvas>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="contact-content">
            <h2>CONTACT</h2>
        </div>
    </section>

    <!-- Optional Controls -->
    <div class="controls">
        <button id="animation-toggle">Pause Animation</button>
        <button id="effect-toggle">Toggle Effect</button>
    </div>

    <script>
        // Three.js setup
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({
            canvas: document.getElementById('bg-canvas'),
            antialias: true,
            alpha: true
        });
        
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        
        // Set background color (green with some transparency)
        scene.background = new THREE.Color(0x2ecc71);
        scene.fog = new THREE.FogExp2(0x2ecc71, 0.035);
        
        // Camera position
        camera.position.set(0, 0, 30);
        
        // Create a group to hold all objects
        const group = new THREE.Group();
        scene.add(group);
        
        // Create particles
        const particlesGeometry = new THREE.BufferGeometry();
        const particlesCount = 1500;
        
        const posArray = new Float32Array(particlesCount * 3);
        const scaleArray = new Float32Array(particlesCount);
        
        // Fill arrays with random positions and scales
        for (let i = 0; i < particlesCount * 3; i += 3) {
            // Position
            posArray[i] = (Math.random() - 0.5) * 100;
            posArray[i + 1] = (Math.random() - 0.5) * 100;
            posArray[i + 2] = (Math.random() - 0.5) * 100;
            
            // Scale
            scaleArray[i / 3] = Math.random();
        }
        
        particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
        particlesGeometry.setAttribute('scale', new THREE.BufferAttribute(scaleArray, 1));
        
        // Material
        const particlesMaterial = new THREE.PointsMaterial({
            size: 0.2,
            color: 0xffffff,
            transparent: true,
            opacity: 0.8,
            sizeAttenuation: true
        });
        
        // Create points
        const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
        group.add(particlesMesh);
        
        // Add some geometric shapes
        const geometries = [
            new THREE.IcosahedronGeometry(5, 0),
            new THREE.OctahedronGeometry(5, 0),
            new THREE.TetrahedronGeometry(5, 0)
        ];
        
        const material = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            wireframe: true,
            transparent: true,
            opacity: 0.3
        });
        
        for (let i = 0; i < 10; i++) {
            const geometry = geometries[Math.floor(Math.random() * geometries.length)];
            const mesh = new THREE.Mesh(geometry, material);
            
            mesh.position.x = (Math.random() - 0.5) * 60;
            mesh.position.y = (Math.random() - 0.5) * 60;
            mesh.position.z = (Math.random() - 0.5) * 60;
            
            mesh.rotation.x = Math.random() * Math.PI;
            mesh.rotation.y = Math.random() * Math.PI;
            
            mesh.scale.setScalar(Math.random() * 0.5 + 0.5);
            
            group.add(mesh);
        }
        
        // Animation variables
        let animationActive = true;
        let currentEffect = 0;
        const effects = [
            () => { // Rotation effect
                group.rotation.y += 0.001;
                group.rotation.x += 0.0005;
            },
            () => { // Wave effect
                const time = Date.now() * 0.001;
                const positions = particlesMesh.geometry.attributes.position.array;
                
                for (let i = 0; i < positions.length; i += 3) {
                    const x = positions[i];
                    const y = positions[i + 1];
                    const z = positions[i + 2];
                    
                    // Apply wave effect
                    positions[i + 1] = y + Math.sin(time + x * 0.1) * 0.1;
                }
                
                particlesMesh.geometry.attributes.position.needsUpdate = true;
            }
        ];
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            if (animationActive) {
                effects[currentEffect]();
            }
            
            renderer.render(scene, camera);
        }
        
        animate();
        
        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // Controls
        document.getElementById('animation-toggle').addEventListener('click', function() {
            animationActive = !animationActive;
            this.textContent = animationActive ? 'Pause Animation' : 'Resume Animation';
        });
        
        document.getElementById('effect-toggle').addEventListener('click', function() {
            currentEffect = (currentEffect + 1) % effects.length;
            this.textContent = `Effect ${currentEffect + 1}`;
        });
    </script>
</body>
</html>

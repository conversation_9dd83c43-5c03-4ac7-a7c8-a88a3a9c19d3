<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">

    <!-- SEO Meta Tags -->
    <title>Artchop!</title>
    <meta name="description" content="Artchop is a scalable, full-stack creative studio for games. We provide high-fidelity game art, animation, code, and content for developers, publishers, and platforms.">
    <meta name="keywords" content="game art outsourcing, game art studio, external development for games, character modeling, animation services for games, UI/UX for games, 3D game art, digital doubles, scalable creative for games">
    <meta name="author" content="Artchop">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://www.artchop.net">

    <!-- Mobile Meta Tags -->
    <meta name="theme-color" content="#8781bd">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">

    <!-- Favicon -->
    <link rel="icon" href="images/flavi.png" type="image/png">
    <link rel="apple-touch-icon" href="images/flavi.png">

    <!-- Social Media Preview -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Artchop!">
    <meta property="og:description" content="Scalable creative strike force for games—trusted by top studios for characters, worlds, pipelines, and content built to ship.">
    <meta property="og:image" content="images/artchoop-social.png">
    <meta property="og:url" content="https://www.artchop.net">
    <meta property="og:site_name" content="Artchop Studio">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Artchop!">
    <meta name="twitter:description" content="Scalable creative strike force for games—trusted by top studios for characters, worlds, pipelines, and content built to ship.">
    <meta name="twitter:image" content="images/artchoop-social.png">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="about-widgets.css">
    <link rel="stylesheet" href="contact.css">
    <!-- Google Fonts with display=swap for better performance -->
    <link href="https://fonts.googleapis.com/css2?family=Hanken+Grotesk:wght@400;500;700&family=Syncopate:wght@400;700&display=swap" rel="stylesheet">

    <!-- Preload critical fonts for better performance -->
    <link rel="preload" href="https://fonts.gstatic.com/s/hankengrotesk/v8/ieVq2YZDLWuGJpnzaiwFXS9tYtpY_Q.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://fonts.gstatic.com/s/syncopate/v19/pe0sMIuPIYBCpEV5eFdCBfe5.woff2" as="font" type="font/woff2" crossorigin>
    <!-- GSAP and ScrollTrigger CDN links -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollToPlugin.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
    <!-- Animation scripts -->
    <script src="script.js" defer></script>
</head>
<body>
    <!-- Preloader -->
    <div class="preloader" id="preloader">
        <img src="images/Artchop_A.png" alt="Artchop" class="preloader-logo">
        <div class="preloader-progress">
            <div class="preloader-bar" id="preloader-bar"></div>
        </div>
        <div class="preloader-text" id="preloader-text">Loading...</div>
    </div>
    <nav class="navbar">
        <div class="navbar-container">
            <a href="#hero" class="site-logo">
                <div class="logo-container">
                    <img src="images/artchop-logo-1.png" class="logo-image logo-1">
                    <img src="images/artchop-logo-2.png" class="logo-image logo-2">
                </div>
            </a>
            <button class="hamburger-icon" aria-label="Toggle navigation menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="nav-links" id="mobile-menu">
                <a href="#about">ABOUT</a>
                <a href="#about-2">GALLERY</a>
                <a href="#contact">CONTACT</a>
            </div>
        </div>
    </nav>
    <div class="sticky-navbar-spacer"></div>

    <!-- Hero Section -->
    <section id="hero" class="hero-section section">
        <div class="hero-overlay"></div>
        <div class="hero-carousel">
            <div class="hero-slide active">
                <img src="images/Hero1.png" alt="Hero 1" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero2.png" alt="Hero 2" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero3.png" alt="Hero 3" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero4.png" alt="Hero 4" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero5.png" alt="Hero 5" class="hero-background-image">
            </div>
        </div>
        <div class="hero-content-container">
            <div class="hero-content">
                <h1>IDEAL CREATIVE PARTNERS FOR GAMES</h1>
                <div class="subheadline">
                    <p>FULL-STACK CREATIVE</p>
                    <p>CHARACTERS</p>
                    <p>WORLDS</p>
                    <p>PIPELINES</p>
                </div>
                <a href="#about" class="cta-button">GET TO KNOW US</a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section section fullscreen-section">
        <div class="about-content">
            <h2>WHO WE ARE</h2>

            <div class="widget-grid">
                <!-- ARTCHOP! Widget - FIRST -->
                <div class="widget mission-widget dominant">
                    <div class="widget-header">
                        <div class="widget-icon">🎯</div>
                        <div class="widget-title">ARTCHOP!</div>
                    </div>
                    <div class="widget-content">
                        <p><span style="font-weight: 700;">WE'RE A MODERN CREATIVE STUDIO BUILT FOR COLLABORATION.</span></p>
                        <br>
                        <p>Backed by deep development experience, creative instincts, and strong technical know-how, Artchop is structured to reduce friction and de-risk outsourcing. Our US-based front office handles communication and coordination, while our global senior team plugs in fast and gets to work.</p>
                        <br>
                        <p>For our clients, we fill gaps, support overflow, or take ownership of entire initiatives. We integrate quickly, align easily, and deliver consistently. We've contributed to award-winning games, long-term partnerships, and high-stakes projects that needed reliable hands when it mattered most.</p>
                    </div>
                </div>

                <!-- Team Breakdown Pie Chart -->
                <div class="widget projects-widget large">
                    <div class="widget-header">
                        <div class="widget-icon">📊</div>
                        <div class="widget-title">Team Breakdown</div>
                    </div>
                    <div class="widget-content">
                        <div class="team-breakdown-layout">
                            <div class="pie-legend">
                                <div class="legend-item"><span class="legend-color" style="background: #e74c3c;"></span>3D MODELERS (10)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #f39c12;"></span>PROGRAMMERS (8)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #2ecc71;"></span>DD SPECIALISTS (6)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #3498db;"></span>CONCEPT ARTISTS (5)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #9b59b6;"></span>ANIMATORS (3)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #f1c40f;"></span>ART MANAGERS (3)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #ff69b4;"></span>TECHNICAL ARTISTS (2)</div>
                            </div>
                            <div class="pie-chart">
                                <svg viewBox="0 0 100 100" class="pie-svg">
                                    <!-- Background circle -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="20"/>

                                    <!-- 3D Modelers: 10/37 = 27.0% of 251.2 circumference = 67.8 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#e74c3c" stroke-width="20"
                                            stroke-dashoffset="62.8" transform="rotate(-90 50 50)" style="--final-dash-array: 67.8 183.4"/>
                                    <!-- Programmers: 8/37 = 21.6% = 54.3 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#f39c12" stroke-width="20"
                                            stroke-dashoffset="-5.0" transform="rotate(-90 50 50)" style="--final-dash-array: 54.3 196.9"/>
                                    <!-- DD Specialists: 6/37 = 16.2% = 40.7 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#2ecc71" stroke-width="20"
                                            stroke-dashoffset="-59.3" transform="rotate(-90 50 50)" style="--final-dash-array: 40.7 210.5"/>
                                    <!-- Concept Artists: 5/37 = 13.5% = 33.9 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#3498db" stroke-width="20"
                                            stroke-dashoffset="-100.0" transform="rotate(-90 50 50)" style="--final-dash-array: 33.9 217.3"/>
                                    <!-- Animators: 3/37 = 8.1% = 20.3 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#9b59b6" stroke-width="20"
                                            stroke-dashoffset="-133.9" transform="rotate(-90 50 50)" style="--final-dash-array: 20.3 230.9"/>
                                    <!-- Art Managers: 3/37 = 8.1% = 20.3 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#f1c40f" stroke-width="20"
                                            stroke-dashoffset="-154.2" transform="rotate(-90 50 50)" style="--final-dash-array: 20.3 230.9"/>
                                    <!-- Technical Artists: 2/37 = 5.4% = 13.6 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#ff69b4" stroke-width="20"
                                            stroke-dashoffset="-174.5" transform="rotate(-90 50 50)" style="--final-dash-array: 13.6 237.6"/>
                                </svg>
                                <div class="pie-center">37</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Technologies Widget -->
                <div class="widget vision-widget tall">
                    <div class="widget-header">
                        <div class="widget-icon">💻</div>
                        <div class="widget-title">Technologies</div>
                    </div>
                    <div class="widget-content">
                        <div class="tech-logos">
                            <div class="tech-item">
                                <div class="tech-logo unity-logo">U</div>
                                <div class="tech-name">UNITY</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo unreal-logo">UE</div>
                                <div class="tech-name">UNREAL</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo shader-logo">SG</div>
                                <div class="tech-name">SHADER GRAPH</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo substance-painter-logo">SP</div>
                                <div class="tech-name">SUBSTANCE 3D PAINTER</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo substance-designer-logo">SD</div>
                                <div class="tech-name">SUBSTANCE 3D DESIGNER</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo marvelous-logo">MD</div>
                                <div class="tech-name">MARVELOUS DESIGNER</div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- Expertise Widget (Wide) -->
                <div class="widget skills-widget wide">
                    <div class="widget-header">
                        <div class="widget-icon">⚡</div>
                        <div class="widget-title">Expertise</div>
                    </div>
                    <div class="widget-content">
                        <div class="expertise-grid">
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">CONCEPT, KEY & PRODUCTION ART</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">HARD SURFACE MODELS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">MODULAR GAME ASSETS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">CHARACTER MODELS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">TEXTURE & MATERIALS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">HD DIGITAL DOUBLES</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">ENVIRONMENTS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">RIGGING</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vibe Check Widget -->
                <div class="widget vibe-widget">
                    <div class="widget-header">
                        <div class="widget-icon">✨</div>
                        <div class="widget-title">Vibe Check</div>
                    </div>
                    <div class="widget-content">
                        <div class="vibe-simple">
                            <div class="widget-label">CURRENT STATUS</div>
                            <div class="vibe-status-row">
                                <div class="status-options">
                                    <span class="status-option">OFF</span>
                                    <span class="status-option active">ON</span>
                                </div>
                                <div class="status-indicator-large on"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Aura Meter Widget -->
                <div class="widget aura-widget">
                    <div class="widget-header">
                        <div class="widget-icon">🌟</div>
                        <div class="widget-title">Aura Meter</div>
                    </div>
                    <div class="widget-content">
                        <div class="aura-meter">
                            <div class="aura-bar">
                                <div class="aura-fill"></div>
                            </div>
                            <div class="aura-percentage">100%</div>
                        </div>
                        <div class="widget-label" style="text-align: center;">MAXIMUM AURA</div>
                    </div>
                </div>

                <!-- World Map Widget -->
                <div class="widget map-widget wide">
                    <div class="widget-header">
                        <div class="widget-icon">🗺️</div>
                        <div class="widget-title">Locations</div>
                    </div>
                    <div class="widget-content">
                        <div class="world-map">
                            <!-- Time Zone Clocks -->
                            <div class="timezone-clocks">
                                <div class="clock-item">
                                    <div class="clock-face" id="sf-clock">
                                        <div class="clock-ticks">
                                            <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                            <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                        </div>
                                        <div class="clock-hand hour-hand" id="sf-hour"></div>
                                        <div class="clock-hand minute-hand" id="sf-minute"></div>
                                    </div>
                                    <div class="clock-label">
                                        <div class="city-name">SAN FRANCISCO</div>
                                        <div class="time-display" id="sf-time">--:--</div>
                                    </div>
                                </div>

                                <div class="clock-item">
                                    <div class="clock-face" id="saitama-clock">
                                        <div class="clock-ticks">
                                            <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                            <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                        </div>
                                        <div class="clock-hand hour-hand" id="saitama-hour"></div>
                                        <div class="clock-hand minute-hand" id="saitama-minute"></div>
                                    </div>
                                    <div class="clock-label">
                                        <div class="city-name">SAITAMA</div>
                                        <div class="time-display" id="saitama-time">--:--</div>
                                    </div>
                                </div>

                                <div class="clock-item">
                                    <div class="clock-face" id="manila-clock">
                                        <div class="clock-ticks">
                                            <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                            <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                        </div>
                                        <div class="clock-hand hour-hand" id="manila-hour"></div>
                                        <div class="clock-hand minute-hand" id="manila-minute"></div>
                                    </div>
                                    <div class="clock-label">
                                        <div class="city-name">MANILA</div>
                                        <div class="time-display" id="manila-time">--:--</div>
                                    </div>
                                </div>

                                <div class="clock-item">
                                    <div class="clock-face" id="novi-sad-clock">
                                        <div class="clock-ticks">
                                            <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                            <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                        </div>
                                        <div class="clock-hand hour-hand" id="novi-sad-hour"></div>
                                        <div class="clock-hand minute-hand" id="novi-sad-minute"></div>
                                    </div>
                                    <div class="clock-label">
                                        <div class="city-name">NOVI SAD</div>
                                        <div class="time-display" id="novi-sad-time">--:--</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons Widget -->
                <div class="widget action-widget wide">
                    <div class="widget-header">
                        <div class="widget-icon">🚀</div>
                        <div class="widget-title">Take Action</div>
                    </div>
                    <div class="widget-content">
                        <div class="action-buttons">
                            <button class="action-btn action-pink" onclick="const targetId1 = 'about-2'; const target1 = document.getElementById(targetId1); if (target1) { resetAndReanimateSection('#' + targetId1); const targetPosition1 = target1.getBoundingClientRect().top + window.scrollY; window.scrollTo({ top: targetPosition1, behavior: 'smooth' }); }">
                                VIEW OUR WORK
                            </button>
                            <button class="action-btn action-blue" onclick="const target2 = document.getElementById('contact'); if (target2) { resetAndReanimateSection('#' + target2.id); const targetPosition2 = target2.getBoundingClientRect().top + window.scrollY; window.scrollTo({ top: targetPosition2, behavior: 'smooth' }); }">
                                REACH OUT TO US
                            </button>
                        </div>
                    </div>
                </div>
        </div>
    </section>

    <!-- Our Work Section -->
    <section id="about-2" class="about-section section fullscreen-section" style="background-color: #000000;">
        <div class="about-content">
            <h2>OUR WORK</h2>
            <div class="widget-grid">
                <!-- Widgets will go here -->
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section section fullscreen-section">
        <div class="about-content">
            <h2>LET'S GET TO WORK!</h2>
            <div class="widget-grid">
                <!-- Contact Form Widget -->
                <div class="widget contact-widget wide">
                    <div class="contact-form-container">
                        <p class="contact-form-subheadline">Let's talk about how we can support your next project and deliver tangible results.</p>

                        <form action="https://formsubmit.co/<EMAIL>" method="POST" class="contact-form-new" id="contact-form-real">
                            <input type="hidden" name="_subject" value="New Contact from Artchop.net">
                            <input type="hidden" name="_captcha" value="false">
                            <input type="hidden" name="_template" value="table">
                            <input type="hidden" name="_autoresponse" value="Thank you for contacting Artchop! We've received your message and will get back to you within 24 hours. We're excited to discuss your project and how we can help bring your vision to life.">
                            <input type="text" name="_honey" style="display:none">

                            <div class="form-field">
                                <input type="text" name="name" id="contact-name" placeholder="Your Name" required>
                            </div>
                            <div class="form-field">
                                <input type="email" name="email" id="contact-email" placeholder="Your Email" required>
                            </div>
                            <div class="form-field">
                                <textarea name="message" id="contact-message" placeholder="Your Message" rows="4" required></textarea>
                            </div>
                            <div class="form-field">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="contact-terms" required>
                                    <span>By continuing you agree to our
                                        <a href="#" onclick="showModal('termsModal'); return false;" style="color: #FF42A1; font-weight: bold; text-decoration: none;">Terms of Service</a>
                                        and
                                        <a href="#" onclick="showModal('privacyModal'); return false;" style="color: #FF42A1; font-weight: bold; text-decoration: none;">Privacy Policy</a>
                                    </span>
                                </label>
                            </div>
                            <div id="form-message" style="display: none;"></div>
                            <div class="form-field">
                                <button type="submit" class="send-button">Send Message</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
    /* New Contact Form Styles */
    .contact-form-new {
        max-width: 500px;
        margin: 0 auto;
        padding: 20px 0;
    }

    .form-field {
        margin-bottom: 20px;
    }

    .form-field input,
    .form-field textarea {
        width: 100%;
        padding: 15px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 5px;
        color: white;
        font-family: 'Hanken Grotesk', sans-serif;
        font-size: 1rem;
        box-sizing: border-box;
    }

    .form-field input::placeholder,
    .form-field textarea::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .form-field input:focus,
    .form-field textarea:focus {
        outline: none;
        border-color: #FF42A1;
        background: rgba(255, 255, 255, 0.15);
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        gap: 10px;
        color: rgba(255, 255, 255, 0.8);
        font-family: 'Hanken Grotesk', sans-serif;
        cursor: pointer;
    }

    .checkbox-label input[type="checkbox"] {
        width: auto;
        margin: 0;
    }

    .send-button {
        width: 100%;
        padding: 15px;
        background: #FF42A1;
        color: white;
        border: none;
        border-radius: 5px;
        font-family: 'Hanken Grotesk', sans-serif;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .send-button:hover {
        background: #e03185;
    }

    .send-button:disabled {
        background: #666;
        cursor: not-allowed;
    }

    #form-message {
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 15px;
        font-weight: bold;
    }

    #form-message.success {
        background: rgba(46, 204, 113, 0.1);
        color: #27ae60;
        border: 1px solid #27ae60;
    }

    #form-message.error {
        background: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
        border: 1px solid #e74c3c;
    }

    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 10000;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .modal.active {
        display: flex;
        opacity: 1;
    }

    .modal-content {
        background: white;
        border-radius: 8px;
        max-width: 90%;
        max-height: 90vh;
        width: 600px;
        overflow-y: auto;
        position: relative;
        color: #333;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .modal-close {
        position: absolute;
        top: 15px;
        right: 20px;
        background: none;
        border: none;
        font-size: 24px;
        color: #000;
        cursor: pointer;
        padding: 5px;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Hanken Grotesk', sans-serif;
        font-weight: normal;
        line-height: 1;
    }

    .modal-close:hover {
        color: #FF42A1;
    }

    .modal-header h2 {
        margin: 0 0 20px 0;
        color: #333;
        font-family: 'Syncopate', sans-serif;
        font-size: 1.5rem;
    }

    .modal-body {
        line-height: 1.6;
    }

    .modal-body h3 {
        color: #FF42A1;
        margin: 20px 0 10px 0;
        font-family: 'Hanken Grotesk', sans-serif;
    }

    .modal-body p {
        margin-bottom: 15px;
        font-family: 'Hanken Grotesk', sans-serif;
    }

    @media (max-width: 768px) {
        .modal-content {
            width: 95%;
            padding: 20px;
            margin: 20px;
        }
    }
    </style>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Artchop Studio",
        "url": "https://www.artchop.net",
        "logo": "https://www.artchop.net/images/flavi.png",
        "description": "Artchop is a scalable, full-stack creative studio for games. We provide high-fidelity game art, animation, code, and content for developers, publishers, and platforms.",
        "slogan": "Scalable creative strike force for games"
    }
    </script>

    <!-- Main JavaScript -->
    <script src="gallery.js"></script>
    <script>
    // Initialize gallery when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof initGallery === 'function') {
            initGallery();
        }

        // Initialize new contact form
        initContactForm();
    });

    function initContactForm() {
        const form = document.getElementById('contact-form-real');
        const messageDiv = document.getElementById('form-message');

        if (!form) return;

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            // Get form values
            const name = document.getElementById('contact-name').value.trim();
            const email = document.getElementById('contact-email').value.trim();
            const message = document.getElementById('contact-message').value.trim();
            const terms = document.getElementById('contact-terms').checked;

            // Validate
            if (!name || !email || !message || !terms) {
                showMessage('Please fill in all fields and accept the terms.', 'error');
                return;
            }

            // Show loading
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;

            // Submit the actual form
            const formData = new FormData(form);

            fetch(form.action, {
                method: form.method,
                body: formData,
                headers: {
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                console.log('Form response:', response.status);
                if (response.ok || response.status === 200) {
                    showMessage('✅ Message sent successfully! We\'ll get back to you soon.', 'success');
                    form.reset();
                } else {
                    throw new Error('Form submission failed');
                }
            })
            .catch(error => {
                console.error('Form error:', error);
                showMessage('❌ Error sending message. Please try again.', 'error');
            })
            .finally(() => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });

        function showMessage(text, type) {
            messageDiv.textContent = text;
            messageDiv.className = type;
            messageDiv.style.display = 'block';

            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }
    }

    // Modal functions
    function showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    function hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    // Close modal when clicking outside content or on close button
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            hideModal(e.target.id);
        }
        // Handle close button clicks
        if (e.target.classList.contains('modal-close') || e.target.closest('.modal-close')) {
            const modal = e.target.closest('.modal');
            if (modal) {
                hideModal(modal.id);
            }
        }
    });
    </script>
    <!-- Terms of Service Modal -->
    <div id="termsModal" class="modal">
        <div class="modal-content">
            <button class="modal-close">&times;</button>
            <div class="modal-header">
                <h2>Artchop.net Terms of Service</h2>
            </div>
            <div class="modal-body">
                <h3>1. Introduction</h3>
                <p>Welcome to Artchop. By accessing this website or using our services, you agree to be bound by these Terms of Service. If you do not agree with any part of these terms, please do not use the site.</p>

                <h3>2. Use of Services</h3>
                <p>Artchop provides creative services for game development and related industries. All services are provided "as is." We are not responsible for delays, data loss, or communication errors, and we make no guarantees regarding uptime or availability.</p>

                <h3>3. User Conduct</h3>
                <p>You are responsible for your use of the site and any communications submitted. Fraudulent, abusive, or unlawful behavior may result in termination of access.</p>

                <h3>4. Intellectual Property</h3>
                <p>All content on this site—including text, graphics, logos, images, and layouts—is the property of Artchop or its licensors and is protected by intellectual property laws. You may not copy, reproduce, or distribute any content without permission.</p>

                <h3>5. Disclaimer of Warranties</h3>
                <p>We provide this website and our services "as is" without warranties of any kind, express or implied, including but not limited to merchantability or fitness for a particular purpose.</p>

                <h3>6. Limitation of Liability</h3>
                <p>Artchop is not liable for any indirect, incidental, special, consequential, or punitive damages arising from your use of this site or services, even if we've been advised of the possibility of such damages.</p>

                <h3>7. Changes to These Terms</h3>
                <p>We may update these Terms at any time. Continued use of the site after changes are posted constitutes your acceptance of those changes.</p>

                <h3>8. Governing Law</h3>
                <p>These Terms are governed by the laws of the State of California, USA, without regard to its conflict of law rules.</p>

                <p class="last-updated">Last Updated: November 8, 2023</p>
            </div>
        </div>
    </div>

    <!-- Privacy Policy Modal -->
    <div id="privacyModal" class="modal">
        <div class="modal-content">
            <button class="modal-close">&times;</button>
            <div class="modal-header">
                <h2>Artchop.net Privacy Policy</h2>
            </div>
            <div class="modal-body">
                <h3>1. Introduction</h3>
                <p>At Artchop, we take your privacy seriously. This Privacy Policy explains what personal information we collect, how we use it, and the steps we take to protect it.</p>

                <h3>2. Information We Collect</h3>
                <p>We may collect personal information when you fill out a form, contact us, subscribe to our newsletter, or communicate with us directly. This may include your name, email address, and any other details you choose to provide.</p>

                <h3>3. How We Use Your Information</h3>
                <p>We use the information we collect to:</p>
                <ul>
                    <li>Respond to inquiries or requests</li>
                    <li>Improve our website and services</li>
                    <li>Send occasional updates or newsletters (only if you've opted in)</li>
                    <li>Maintain internal records</li>
                </ul>

                <h3>4. How We Protect Your Information</h3>
                <p>We implement industry-standard security measures to protect your information from unauthorized access, alteration, or disclosure.</p>

                <h3>5. Cookies</h3>
                <p>Our website uses cookies to enhance user experience and collect limited analytics data. You can choose to

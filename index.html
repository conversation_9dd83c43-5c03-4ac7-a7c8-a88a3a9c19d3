<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">

    <!-- SEO Meta Tags -->
    <title>Artchop!</title>
    <meta name="description" content="Artchop is a scalable, full-stack creative studio for games. We provide high-fidelity game art, animation, code, and content for developers, publishers, and platforms.">
    <meta name="keywords" content="game art outsourcing, game art studio, external development for games, character modeling, animation services for games, UI/UX for games, 3D game art, digital doubles, scalable creative for games">
    <meta name="author" content="Artchop">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://www.artchop.net">

    <!-- Mobile Meta Tags -->
    <meta name="theme-color" content="#8781bd">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">

    <!-- Favicon -->
    <link rel="icon" href="images/flavi.png" type="image/png">
    <link rel="apple-touch-icon" href="images/flavi.png">

    <!-- Social Media Preview -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Artchop!">
    <meta property="og:description" content="Scalable creative strike force for games—trusted by top studios for characters, worlds, pipelines, and content built to ship.">
    <meta property="og:image" content="images/artchoop-social.png">
    <meta property="og:url" content="https://www.artchop.net">
    <meta property="og:site_name" content="Artchop Studio">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Artchop!">
    <meta name="twitter:description" content="Scalable creative strike force for games—trusted by top studios for characters, worlds, pipelines, and content built to ship.">
    <meta name="twitter:image" content="images/artchoop-social.png">
    <link rel="stylesheet" href="fixed-style.css">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="gallery.css">
    <link rel="stylesheet" href="about-widgets.css">
    <link rel="stylesheet" href="action-buttons.css">
    <link rel="stylesheet" href="contact.css">
    <!-- Google Fonts with display=swap for better performance -->
    <link href="https://fonts.googleapis.com/css2?family=Hanken+Grotesk:wght@400;500;700&family=Syncopate:wght@400;700&display=swap" rel="stylesheet">

    <!-- Preload critical fonts for better performance -->
    <link rel="preload" href="https://fonts.gstatic.com/s/hankengrotesk/v8/ieVq2YZDLWuGJpnzaiwFXS9tYtpY_Q.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://fonts.gstatic.com/s/syncopate/v19/pe0sMIuPIYBCpEV5eFdCBfe5.woff2" as="font" type="font/woff2" crossorigin>
    <!-- GSAP and ScrollTrigger CDN links -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollToPlugin.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
    <!-- Animation scripts -->
    <script src="script.js" defer></script>
    <script src="gallery.js" defer></script>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-container">
            <a href="#hero" class="site-logo">
                <div class="logo-container">
                    <img src="images/artchop-logo-1.png" class="logo-image logo-1">
                    <img src="images/artchop-logo-2.png" class="logo-image logo-2">
                </div>
            </a>
            <button class="hamburger-icon" aria-label="Toggle navigation menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="nav-links" id="mobile-menu">
                <a href="#about">ABOUT</a>
                <a href="#about-2">GALLERY</a>
                <a href="#contact">CONTACT</a>
            </div>
        </div>
    </nav>
    <div class="sticky-navbar-spacer"></div>

    <!-- Hero Section -->
    <section id="hero" class="hero-section section">
        <div class="hero-overlay"></div>
        <div class="hero-carousel">
            <div class="hero-slide active">
                <img src="images/Hero1.png" alt="Hero 1" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero2.png" alt="Hero 2" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero3.png" alt="Hero 3" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero4.png" alt="Hero 4" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero5.png" alt="Hero 5" class="hero-background-image">
            </div>
        </div>
        <div class="hero-content-container">
            <div class="hero-content">
                <h1>IDEAL CREATIVE PARTNERS FOR GAMES</h1>
                <div class="subheadline">
                    <p>FULL-STACK CREATIVE</p>
                    <p>CHARACTERS</p>
                    <p>WORLDS</p>
                    <p>PIPELINES</p>
                </div>
                <a href="#about" class="cta-button">GET TO KNOW US</a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section section fullscreen-section">
        <div class="about-content">
            <h2>WHO WE ARE</h2>

            <div class="widget-grid">
                <!-- ARTCHOP! Widget - FIRST -->
                <div class="widget mission-widget dominant">
                    <div class="widget-header">
                        <div class="widget-icon">🎯</div>
                        <div class="widget-title">ARTCHOP!</div>
                    </div>
                    <div class="widget-content">
                        <p><span style="font-weight: 700;">WE'RE A MODERN CREATIVE STUDIO BUILT FOR COLLABORATION.</span></p>
                        <br>
                        <p>Backed by deep development experience, creative instincts, and strong technical know-how, Artchop is structured to reduce friction and de-risk outsourcing. Our US-based front office handles communication and coordination, while our global senior team plugs in fast and gets to work.</p>
                        <br>
                        <p>For our clients, we fill gaps, support overflow, or take ownership of entire initiatives. We integrate quickly, align easily, and deliver consistently. We've contributed to award-winning games, long-term partnerships, and high-stakes projects that needed reliable hands when it mattered most.</p>
                    </div>
                </div>

                <!-- Team Breakdown Pie Chart -->
                <div class="widget projects-widget large">
                    <div class="widget-header">
                        <div class="widget-icon">📊</div>
                        <div class="widget-title">Team Breakdown</div>
                    </div>
                    <div class="widget-content">
                        <div class="team-breakdown-layout">
                            <div class="pie-legend">
                                <div class="legend-item"><span class="legend-color" style="background: #e74c3c;"></span>3D MODELERS (10)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #f39c12;"></span>PROGRAMMERS (8)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #2ecc71;"></span>DD SPECIALISTS (6)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #3498db;"></span>CONCEPT ARTISTS (5)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #9b59b6;"></span>ANIMATORS (3)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #f1c40f;"></span>ART MANAGERS (3)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #ff69b4;"></span>TECHNICAL ARTISTS (2)</div>
                            </div>
                            <div class="pie-chart">
                                <svg viewBox="0 0 100 100" class="pie-svg">
                                    <!-- Background circle -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="20"/>

                                    <!-- 3D Modelers: 10/37 = 27.0% of 251.2 circumference = 67.8 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#e74c3c" stroke-width="20"
                                            stroke-dashoffset="62.8" transform="rotate(-90 50 50)" style="--final-dash-array: 67.8 183.4"/>
                                    <!-- Programmers: 8/37 = 21.6% = 54.3 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#f39c12" stroke-width="20"
                                            stroke-dashoffset="-5.0" transform="rotate(-90 50 50)" style="--final-dash-array: 54.3 196.9"/>
                                    <!-- DD Specialists: 6/37 = 16.2% = 40.7 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#2ecc71" stroke-width="20"
                                            stroke-dashoffset="-59.3" transform="rotate(-90 50 50)" style="--final-dash-array: 40.7 210.5"/>
                                    <!-- Concept Artists: 5/37 = 13.5% = 33.9 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#3498db" stroke-width="20"
                                            stroke-dashoffset="-100.0" transform="rotate(-90 50 50)" style="--final-dash-array: 33.9 217.3"/>
                                    <!-- Animators: 3/37 = 8.1% = 20.3 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#9b59b6" stroke-width="20"
                                            stroke-dashoffset="-133.9" transform="rotate(-90 50 50)" style="--final-dash-array: 20.3 230.9"/>
                                    <!-- Art Managers: 3/37 = 8.1% = 20.3 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#f1c40f" stroke-width="20"
                                            stroke-dashoffset="-154.2" transform="rotate(-90 50 50)" style="--final-dash-array: 20.3 230.9"/>
                                    <!-- Technical Artists: 2/37 = 5.4% = 13.6 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#ff69b4" stroke-width="20"
                                            stroke-dashoffset="-174.5" transform="rotate(-90 50 50)" style="--final-dash-array: 13.6 237.6"/>
                                </svg>
                                <div class="pie-center">37</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Technologies Widget -->
                <div class="widget vision-widget tall">
                    <div class="widget-header">
                        <div class="widget-icon">💻</div>
                        <div class="widget-title">Technologies</div>
                    </div>
                    <div class="widget-content">
                        <div class="tech-logos">
                            <div class="tech-item">
                                <div class="tech-logo unity-logo">U</div>
                                <div class="tech-name">UNITY</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo unreal-logo">UE</div>
                                <div class="tech-name">UNREAL</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo shader-logo">SG</div>
                                <div class="tech-name">SHADER GRAPH</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo substance-painter-logo">SP</div>
                                <div class="tech-name">SUBSTANCE 3D PAINTER</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo substance-designer-logo">SD</div>
                                <div class="tech-name">SUBSTANCE 3D DESIGNER</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo marvelous-logo">MD</div>
                                <div class="tech-name">MARVELOUS DESIGNER</div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- Expertise Widget (Wide) -->
                <div class="widget skills-widget wide">
                    <div class="widget-header">
                        <div class="widget-icon">⚡</div>
                        <div class="widget-title">Expertise</div>
                    </div>
                    <div class="widget-content">
                        <div class="expertise-grid">
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">CONCEPT, KEY & PRODUCTION ART</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">HARD SURFACE MODELS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">MODULAR GAME ASSETS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">CHARACTER MODELS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">TEXTURE & MATERIALS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">HD DIGITAL DOUBLES</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">ENVIRONMENTS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">RIGGING</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vibe Check Widget -->
                <div class="widget vibe-widget">
                    <div class="widget-header">
                        <div class="widget-icon">✨</div>
                        <div class="widget-title">Vibe Check</div>
                    </div>
                    <div class="widget-content">
                        <div class="vibe-simple">
                            <div class="widget-label">CURRENT STATUS</div>
                            <div class="vibe-status-row">
                                <div class="status-options">
                                    <span class="status-option">OFF</span>
                                    <span class="status-option active">ON</span>
                                </div>
                                <div class="status-indicator-large on"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Aura Meter Widget -->
                <div class="widget aura-widget">
                    <div class="widget-header">
                        <div class="widget-icon">🌟</div>
                        <div class="widget-title">Aura Meter</div>
                    </div>
                    <div class="widget-content">
                        <div class="aura-meter">
                            <div class="aura-bar">
                                <div class="aura-fill"></div>
                            </div>
                            <div class="aura-percentage">100%</div>
                        </div>
                        <div class="widget-label" style="text-align: center;">MAXIMUM AURA</div>
                    </div>
                </div>

                <!-- World Map Widget -->
                <div class="widget map-widget wide">
                    <div class="widget-header">
                        <div class="widget-icon">🗺️</div>
                        <div class="widget-title">Locations</div>
                    </div>
                    <div class="widget-content">
                        <div class="world-map">
                            <!-- Time Zone Clocks -->
                            <div class="timezone-clocks">
                                <div class="clock-item">
                                    <div class="clock-face" id="sf-clock">
                                        <div class="clock-ticks">
                                            <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                            <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                        </div>
                                        <div class="clock-hand hour-hand" id="sf-hour"></div>
                                        <div class="clock-hand minute-hand" id="sf-minute"></div>
                                    </div>
                                    <div class="clock-label">
                                        <div class="city-name">SAN FRANCISCO</div>
                                        <div class="time-display" id="sf-time">--:--</div>
                                    </div>
                                </div>

                                <div class="clock-item">
                                    <div class="clock-face" id="saitama-clock">
                                        <div class="clock-ticks">
                                            <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                            <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                        </div>
                                        <div class="clock-hand hour-hand" id="saitama-hour"></div>
                                        <div class="clock-hand minute-hand" id="saitama-minute"></div>
                                    </div>
                                    <div class="clock-label">
                                        <div class="city-name">SAITAMA</div>
                                        <div class="time-display" id="saitama-time">--:--</div>
                                    </div>
                                </div>

                                <div class="clock-item">
                                    <div class="clock-face" id="manila-clock">
                                        <div class="clock-ticks">
                                            <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                            <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                        </div>
                                        <div class="clock-hand hour-hand" id="manila-hour"></div>
                                        <div class="clock-hand minute-hand" id="manila-minute"></div>
                                    </div>
                                    <div class="clock-label">
                                        <div class="city-name">MANILA</div>
                                        <div class="time-display" id="manila-time">--:--</div>
                                    </div>
                                </div>

                                <div class="clock-item">
                                    <div class="clock-face" id="novi-sad-clock">
                                        <div class="clock-ticks">
                                            <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                            <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                        </div>
                                        <div class="clock-hand hour-hand" id="novi-sad-hour"></div>
                                        <div class="clock-hand minute-hand" id="novi-sad-minute"></div>
                                    </div>
                                    <div class="clock-label">
                                        <div class="city-name">NOVI SAD</div>
                                        <div class="time-display" id="novi-sad-time">--:--</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons Widget -->
                <div class="widget action-widget wide">
                    <div class="widget-header">
                        <div class="widget-icon">🚀</div>
                        <div class="widget-title">Take Action</div>
                    </div>
                    <div class="widget-content">
                        <div class="action-buttons">
                            <button class="action-btn action-pink" onclick="const targetId1 = 'about-2'; const target1 = document.getElementById(targetId1); if (target1) { resetAndReanimateSection('#' + targetId1); const targetPosition1 = target1.getBoundingClientRect().top + window.scrollY; window.scrollTo({ top: targetPosition1, behavior: 'smooth' }); }">
                                VIEW OUR WORK
                            </button>
                            <button class="action-btn action-blue" onclick="const target2 = document.getElementById('contact'); if (target2) { resetAndReanimateSection('#' + target2.id); const targetPosition2 = target2.getBoundingClientRect().top + window.scrollY; window.scrollTo({ top: targetPosition2, behavior: 'smooth' }); }">
                                REACH OUT TO US
                            </button>
                        </div>
                    </div>
                </div>
        </div>
    </section>

    <!-- Our Work Section -->
    <section id="about-2" class="about-section section fullscreen-section" style="background-color: #000000;">
        <div class="about-content">
            <h2>OUR WORK</h2>
            <div class="widget-grid">
                <!-- Widgets will go here -->
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section section fullscreen-section">
        <div class="about-content">
            <h2>LET'S GET TO WORK!</h2>
            <div class="widget-grid">
                <!-- Contact Form Widget -->
                <div class="widget contact-widget wide">
                    <div class="contact-form-container">
                        <p class="contact-form-subheadline">Let's talk about how we can support your next project and deliver tangible results.</p>

                        <form id="contact-form" class="contact-form" novalidate>
                            <!-- Prevent spam -->
                            <input type="text" name="_honey" style="display:none">
                            <!-- Disable Captcha -->
                            <input type="hidden" name="_captcha" value="false">
                            <!-- Disable auto response -->
                            <input type="hidden" name="_autoresponse" value="">
                            <!-- Template -->
                            <input type="hidden" name="_template" value="table">
                            <!-- Subject line -->
                            <input type="hidden" name="_subject" value="New Contact Form Submission from Artchop.net">
                            <div class="form-group">
                                <input type="text" id="name" name="name" required minlength="2" maxlength="100" placeholder=" ">
                                <label for="name">Your Name</label>
                            </div>
                            <div class="form-group">
                                <input type="email" id="email" name="email" required placeholder=" ">
                                <label for="email">Your Email</label>
                            </div>
                            <div class="form-group">
                                <textarea id="message" name="message" rows="4" required minlength="10" maxlength="1000" placeholder=" "></textarea>
                                <label for="message">Your Message</label>
                            </div>
                            <div class="form-group terms-group">
                                <div style="display: flex; align-items: center; flex-wrap: wrap; gap: 4px;">
                                    <input type="checkbox" id="terms" name="terms" required>
                                    <label for="terms" style="margin: 0; padding: 0; display: inline; position: static; transform: none; pointer-events: auto;">
                                        By continuing you agree to our
                                    </label>
                                    <a href="#" onclick="showModal('termsModal'); return false;" style="color: #FF42A1; font-weight: bold; text-decoration: none; margin: 0 4px;">
                                        Terms of Service
                                    </a>
                                    <span style="color: #FF42A1;">and</span>
                                    <a href="#" onclick="showModal('privacyModal'); return false;" style="color: #FF42A1; font-weight: bold; text-decoration: none; margin: 0 4px;">
                                        Privacy Policy
                                    </a>
                                </div>
                            </div>
                            <button type="button" class="cta-button" id="contact-submit-btn">Send Message</button>
                        </form>

                        <script>
                        // Button click handler to prevent page jumping
                        (function() {
                            const button = document.getElementById('contact-submit-btn');
                            const form = document.getElementById('contact-form');

                            if (button && form) {
                                button.addEventListener('click', function(e) {
                                    e.preventDefault();
                                    e.stopPropagation();

                                    const originalText = button.textContent;

                                    // Basic validation
                                    const name = document.getElementById('name').value.trim();
                                    const email = document.getElementById('email').value.trim();
                                    const message = document.getElementById('message').value.trim();
                                    const terms = document.getElementById('terms').checked;

                                    if (!name || !email || !message || !terms) {
                                        alert('Please fill in all fields and accept the terms.');
                                        return;
                                    }

                                    // Show loading
                                    button.textContent = 'Sending...';
                                    button.disabled = true;

                                    // Submit form
                                    const formData = new FormData(form);
                                    fetch('https://formsubmit.co/<EMAIL>', {
                                        method: 'POST',
                                        body: formData
                                    })
                                    .then(response => {
                                        if (response.ok) {
                                            // Show success message
                                            const msg = document.createElement('div');
                                            msg.innerHTML = '✅ Message sent successfully! We\'ll get back to you soon.';
                                            msg.style.cssText = 'padding: 15px; margin: 15px 0; border-radius: 5px; font-weight: bold; background: rgba(46, 204, 113, 0.1); color: #27ae60; border: 1px solid #27ae60;';
                                            form.parentNode.insertBefore(msg, form);
                                            form.reset();
                                            setTimeout(() => msg.remove(), 5000);
                                        } else {
                                            throw new Error('Network error');
                                        }
                                    })
                                    .catch(error => {
                                        // Show error message
                                        const msg = document.createElement('div');
                                        msg.innerHTML = '❌ Error sending message. Please try again.';
                                        msg.style.cssText = 'padding: 15px; margin: 15px 0; border-radius: 5px; font-weight: bold; background: rgba(231, 76, 60, 0.1); color: #e74c3c; border: 1px solid #e74c3c;';
                                        form.parentNode.insertBefore(msg, form);
                                        setTimeout(() => msg.remove(), 5000);
                                    })
                                    .finally(() => {
                                        button.textContent = originalText;
                                        button.disabled = false;
                                    });
                                });
                            }
                        })();
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Artchop Studio",
        "url": "https://www.artchop.net",
        "logo": "https://www.artchop.net/images/flavi.png",
        "description": "Artchop is a scalable, full-stack creative studio for games. We provide high-fidelity game art, animation, code, and content for developers, publishers, and platforms.",
        "slogan": "Scalable creative strike force for games"
    }
    </script>

    <!-- Main JavaScript -->
    <script src="gallery.js"></script>
    <script>
    // Initialize gallery when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof initGallery === 'function') {
            initGallery();
        }
    });

    // Simple modal functions
    function showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            setTimeout(() => {
                modal.style.opacity = '1';
            }, 10);
        }
    }

    function hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
                document.body.style.overflow = '';
            }, 300);
        }
    }

    // Close modal when clicking outside content
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            hideModal(e.target.id);
        }
    });
    </script>
    <!-- Terms of Service Modal -->
    <div id="termsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Artchop.net Terms of Service</h2>
                <div style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
                    <a href="#" onclick="hideModal('termsModal'); return false;" style="font-size: 32px; color: #333; text-decoration: none; display: block; width: 40px; height: 40px; text-align: center; line-height: 40px;">&times;</a>
                </div>
            </div>
            <div class="modal-body">
                <h3>1. Introduction</h3>
                <p>Welcome to Artchop. By accessing this website or using our services, you agree to be bound by these Terms of Service. If you do not agree with any part of these terms, please do not use the site.</p>

                <h3>2. Use of Services</h3>
                <p>Artchop provides creative services for game development and related industries. All services are provided "as is." We are not responsible for delays, data loss, or communication errors, and we make no guarantees regarding uptime or availability.</p>

                <h3>3. User Conduct</h3>
                <p>You are responsible for your use of the site and any communications submitted. Fraudulent, abusive, or unlawful behavior may result in termination of access.</p>

                <h3>4. Intellectual Property</h3>
                <p>All content on this site—including text, graphics, logos, images, and layouts—is the property of Artchop or its licensors and is protected by intellectual property laws. You may not copy, reproduce, or distribute any content without permission.</p>

                <h3>5. Disclaimer of Warranties</h3>
                <p>We provide this website and our services "as is" without warranties of any kind, express or implied, including but not limited to merchantability or fitness for a particular purpose.</p>

                <h3>6. Limitation of Liability</h3>
                <p>Artchop is not liable for any indirect, incidental, special, consequential, or punitive damages arising from your use of this site or services, even if we've been advised of the possibility of such damages.</p>

                <h3>7. Changes to These Terms</h3>
                <p>We may update these Terms at any time. Continued use of the site after changes are posted constitutes your acceptance of those changes.</p>

                <h3>8. Governing Law</h3>
                <p>These Terms are governed by the laws of the State of California, USA, without regard to its conflict of law rules.</p>

                <p class="last-updated">Last Updated: November 8, 2023</p>
            </div>
        </div>
    </div>

    <!-- Privacy Policy Modal -->
    <div id="privacyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Artchop.net Privacy Policy</h2>
                <div style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
                    <a href="#" onclick="hideModal('privacyModal'); return false;" style="font-size: 32px; color: #333; text-decoration: none; display: block; width: 40px; height: 40px; text-align: center; line-height: 40px;">&times;</a>
                </div>
            </div>
            <div class="modal-body">
                <h3>1. Introduction</h3>
                <p>At Artchop, we take your privacy seriously. This Privacy Policy explains what personal information we collect, how we use it, and the steps we take to protect it.</p>

                <h3>2. Information We Collect</h3>
                <p>We may collect personal information when you fill out a form, contact us, subscribe to our newsletter, or communicate with us directly. This may include your name, email address, and any other details you choose to provide.</p>

                <h3>3. How We Use Your Information</h3>
                <p>We use the information we collect to:</p>
                <ul>
                    <li>Respond to inquiries or requests</li>
                    <li>Improve our website and services</li>
                    <li>Send occasional updates or newsletters (only if you've opted in)</li>
                    <li>Maintain internal records</li>
                </ul>

                <h3>4. How We Protect Your Information</h3>
                <p>We implement industry-standard security measures to protect your information from unauthorized access, alteration, or disclosure.</p>

                <h3>5. Cookies</h3>
                <p>Our website uses cookies to enhance user experience and collect limited analytics data. You can choose to

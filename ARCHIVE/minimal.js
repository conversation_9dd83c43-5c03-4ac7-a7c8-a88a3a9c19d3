/**
 * Minimal Idle Animation
 * A simple, non-invasive animation that won't break layout
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('Minimal idle animation initialized');
    
    // Wait for intro animations to complete
    setTimeout(() => {
        // Add a subtle text shadow animation to idle-target elements
        const idleTargets = document.querySelectorAll('.idle-target');
        
        idleTargets.forEach(target => {
            // Add a class for the animation
            target.classList.add('text-shadow-pulse');
        });
    }, 2500);
});

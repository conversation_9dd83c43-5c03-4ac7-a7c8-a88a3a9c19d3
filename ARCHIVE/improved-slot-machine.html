<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Improved Slot Machine Animation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #222;
            color: white;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }

        button:hover {
            background-color: #45a049;
        }

        button.active {
            background-color: #2196F3;
        }

        .slot-machine {
            width: 300px;
            height: 300px;
            margin: 0 auto;
            background-color: #333;
            border: 10px solid gold;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            position: relative;
            overflow: hidden;
        }

        /* Slot window - where symbols are visible */
        .slot-window {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        /* Slot reel - the strip of symbols */
        .slot-reel {
            position: absolute;
            width: 100%;
            /* Height is much taller than the window to contain multiple symbols */
            height: 1000%;
            top: 0;
            left: 0;
        }

        /* Horizontal reel */
        .horizontal-reel {
            position: absolute;
            width: 1000%;
            height: 100%;
            top: 0;
            left: 0;
            display: flex;
        }

        /* Individual symbol */
        .symbol {
            width: 100%;
            height: 10%; /* 10% height for 10 symbols */
            background-size: cover;
            background-position: center;
        }

        /* Horizontal symbol */
        .horizontal-symbol {
            width: 10%; /* 10% width for 10 symbols */
            height: 100%;
            flex-shrink: 0;
            background-size: cover;
            background-position: center;
        }

        /* Animation classes with adjusted speeds and directions */
        .vertical-variable {
            animation: verticalScrollVariable 6s ease-in-out infinite;
        }

        .vertical-constant {
            animation: verticalScrollConstant 7s linear infinite;
        }

        .horizontal-variable {
            animation: horizontalScrollVariable 6s ease-in-out infinite;
        }

        .horizontal-constant {
            animation: horizontalScrollConstant 7s linear infinite;
        }

        /* Variable speed vertical scroll - speeds up, does revolutions, then slows to still */
        @keyframes verticalScrollVariable {
            /* Start from still */
            0% { transform: translateY(0%); }

            /* Speed up */
            10% { transform: translateY(-40%); }

            /* Full speed for revolutions */
            20% { transform: translateY(-90%); }
            40% { transform: translateY(-180%); }

            /* Slow down to still */
            60% { transform: translateY(-270%); }
            70% { transform: translateY(-290%); }

            /* Stay still at a complete symbol */
            80% { transform: translateY(-300%); }

            /* Stay still until next cycle */
            100% { transform: translateY(-300%); }
        }

        /* Constant speed vertical scroll - opposite direction (bottom to top) */
        @keyframes verticalScrollConstant {
            0% { transform: translateY(0%); }
            100% { transform: translateY(180%); } /* Move down by 9 symbols (180% of reel height) */
        }

        /* Variable speed horizontal scroll - speeds up, does revolutions, then slows to still */
        @keyframes horizontalScrollVariable {
            /* Start from still */
            0% { transform: translateX(0%); }

            /* Speed up */
            10% { transform: translateX(-40%); }

            /* Full speed for revolutions */
            20% { transform: translateX(-90%); }
            40% { transform: translateX(-180%); }

            /* Slow down to still */
            60% { transform: translateX(-270%); }
            70% { transform: translateX(-290%); }

            /* Stay still at a complete symbol */
            80% { transform: translateX(-300%); }

            /* Stay still until next cycle */
            100% { transform: translateX(-300%); }
        }

        /* Constant speed horizontal scroll - opposite direction (right to left) */
        @keyframes horizontalScrollConstant {
            0% { transform: translateX(0%); }
            100% { transform: translateX(180%); } /* Move right by 9 symbols (180% of reel width) */
        }
    </style>
</head>
<body>
    <h1>Improved Slot Machine Animation</h1>

    <div class="controls">
        <button id="btn-vertical-variable" class="active">Vertical Variable Speed</button>
        <button id="btn-vertical-constant">Vertical Constant Speed</button>
        <button id="btn-horizontal-variable">Horizontal Variable Speed</button>
        <button id="btn-horizontal-constant">Horizontal Constant Speed</button>
        <button id="btn-upload">Upload Image</button>
    </div>

    <input type="file" id="image-upload" accept="image/*" style="display: none;">

    <div class="slot-machine">
        <div class="slot-window" id="slot-window">
            <!-- Content will be generated by JavaScript -->
        </div>
    </div>

    <script>
        // Get elements
        const slotWindow = document.getElementById('slot-window');
        const btnVerticalVariable = document.getElementById('btn-vertical-variable');
        const btnVerticalConstant = document.getElementById('btn-vertical-constant');
        const btnHorizontalVariable = document.getElementById('btn-horizontal-variable');
        const btnHorizontalConstant = document.getElementById('btn-horizontal-constant');
        const btnUpload = document.getElementById('btn-upload');
        const imageUpload = document.getElementById('image-upload');

        // Default image URL
        let currentImageUrl = 'https://images.unsplash.com/photo-1682687982501-1e58ab814714';

        // Initialize with vertical variable speed
        createVerticalReel('variable');

        // Button event listeners
        btnVerticalVariable.addEventListener('click', function() {
            setActiveButton(this);
            createVerticalReel('variable');
        });

        btnVerticalConstant.addEventListener('click', function() {
            setActiveButton(this);
            createVerticalReel('constant');
        });

        btnHorizontalVariable.addEventListener('click', function() {
            setActiveButton(this);
            createHorizontalReel('variable');
        });

        btnHorizontalConstant.addEventListener('click', function() {
            setActiveButton(this);
            createHorizontalReel('constant');
        });

        // Image upload handling
        btnUpload.addEventListener('click', function() {
            imageUpload.click();
        });

        imageUpload.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    currentImageUrl = e.target.result;
                    // Refresh current animation
                    if (btnVerticalVariable.classList.contains('active')) {
                        createVerticalReel('variable');
                    } else if (btnVerticalConstant.classList.contains('active')) {
                        createVerticalReel('constant');
                    } else if (btnHorizontalVariable.classList.contains('active')) {
                        createHorizontalReel('variable');
                    } else {
                        createHorizontalReel('constant');
                    }
                };
                reader.readAsDataURL(file);
            }
        });

        // Helper function to set active button
        function setActiveButton(activeButton) {
            const buttons = document.querySelectorAll('.controls button');
            buttons.forEach(btn => btn.classList.remove('active'));
            activeButton.classList.add('active');
        }

        // Create vertical reel
        function createVerticalReel(speedType) {
            // Create reel with 5 copies of the image
            const reel = document.createElement('div');
            reel.className = 'slot-reel';

            // Add animation class based on speed type
            if (speedType === 'variable') {
                reel.classList.add('vertical-variable');
            } else {
                reel.classList.add('vertical-constant');
            }

            // Create 10 symbols (enough to ensure seamless looping with more revolutions)
            for (let i = 0; i < 10; i++) {
                const symbol = document.createElement('div');
                symbol.className = 'symbol';
                symbol.style.backgroundImage = `url('${currentImageUrl}')`;
                reel.appendChild(symbol);
            }

            // Clear and add to slot window
            slotWindow.innerHTML = '';
            slotWindow.appendChild(reel);
        }

        // Create horizontal reel
        function createHorizontalReel(speedType) {
            // Create reel with 5 copies of the image
            const reel = document.createElement('div');
            reel.className = 'horizontal-reel';

            // Add animation class based on speed type
            if (speedType === 'variable') {
                reel.classList.add('horizontal-variable');
            } else {
                reel.classList.add('horizontal-constant');
            }

            // Create 10 symbols (enough to ensure seamless looping with more revolutions)
            for (let i = 0; i < 10; i++) {
                const symbol = document.createElement('div');
                symbol.className = 'horizontal-symbol';
                symbol.style.backgroundImage = `url('${currentImageUrl}')`;
                reel.appendChild(symbol);
            }

            // Clear and add to slot window
            slotWindow.innerHTML = '';
            slotWindow.appendChild(reel);
        }
    </script>
</body>
</html>

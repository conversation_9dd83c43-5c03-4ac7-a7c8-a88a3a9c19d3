/*
==============================================
ANIMATION FIXES
==============================================
*/

/* 
1. BASELINE FIX
---------------
*/

/* Fix baseline alignment by using absolute positioning */
.word-clip {
    position: relative;
    display: inline-block;
    overflow: hidden;
    position: relative; /* Enable absolute positioning */
}

.word-inner {
    display: inline-block;
    position: absolute; /* Position absolutely */
    bottom: 0; /* Align to bottom of clip - fixes baseline */
    left: 0;
    width: 100%;
    transform: translateY(100%);
}

/* 
2. TIGHTER LEADING
-----------------
*/

/* Specific heights for different elements to match their line heights */
h1 .word-clip {
    height: 0.9em; /* Match h1 line-height - tightens leading */
}

.hero-content h1 .word-clip {
    height: 0.85em; /* Match hero h1 line-height - tightens leading */
}

h2 .word-clip {
    height: 0.92em; /* Match h2 line-height - tightens leading */
}

p.intro .word-clip {
    height: 1.4em; /* Match paragraph line-height - tightens leading */
}

/* 
3. WHOLE BUTTON ANIMATION
------------------------
*/

/* Button specific styling */
.cta-button.intro {
    overflow: hidden;
    position: relative;
    display: inline-block;
}

.cta-button.intro::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateY(100%);
    animation: buttonRise 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    animation-delay: 0.5s;
}

@keyframes buttonRise {
    0% {
        transform: translateY(100%);
    }
    100% {
        transform: translateY(0);
    }
}

/*
==============================================
FIXED ANIMATIONS - RISE IN PLACE, WORD BY WORD
==============================================
*/

/* Hero image scale-up animation */
@keyframes scaleUpHero {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.1);
    }
}

/*
1. WORD ANIMATION CONTAINERS
---------------------------
*/

/* Container for word animations - preserves layout */
.word-wrap {
    position: relative;
    display: inline-block;
}

/* Clip container for revealing effect */
.word-clip {
    position: relative;
    display: inline-block;
    overflow: hidden;
    position: relative; /* Enable absolute positioning - fixes baseline */
}

/* Specific heights for different elements - tightens leading */
h1 .word-clip {
    height: 0.9em; /* Tighter leading for h1 */
}

.hero-content h1 .word-clip {
    height: 0.85em; /* Tighter leading for hero h1 */
}

h2 .word-clip {
    height: 0.92em; /* Tighter leading for h2 */
}

p .word-clip {
    height: 1.4em; /* Tighter leading for paragraphs */
}

/* Actual word that animates */
.word-inner {
    display: inline-block;
    position: absolute; /* Position absolutely - fixes baseline */
    bottom: 0; /* Align to bottom of mask - fixes baseline */
    left: 0;
    width: 100%;
    height: auto; /* Allow height to adjust to content */
    min-height: 1em; /* Ensure minimum height */
    transform: translateY(100%);
    animation: wordRise 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    opacity: 1; /* Ensure visibility */
}

/* Animation for words rising in place */
@keyframes wordRise {
    0% {
        transform: translateY(100%);
    }
    100% {
        transform: translateY(0);
    }
}

/*
2. ANIMATION DELAYS
-----------------
*/

/* Word animation delays */
.word-delay-1 { animation-delay: 0.05s; }
.word-delay-2 { animation-delay: 0.1s; }
.word-delay-3 { animation-delay: 0.15s; }
.word-delay-4 { animation-delay: 0.2s; }
.word-delay-5 { animation-delay: 0.25s; }
.word-delay-6 { animation-delay: 0.3s; }
.word-delay-7 { animation-delay: 0.35s; }
.word-delay-8 { animation-delay: 0.4s; }
.word-delay-9 { animation-delay: 0.45s; }
.word-delay-10 { animation-delay: 0.5s; }
.word-delay-11 { animation-delay: 0.55s; }
.word-delay-12 { animation-delay: 0.6s; }
.word-delay-13 { animation-delay: 0.65s; }
.word-delay-14 { animation-delay: 0.7s; }
.word-delay-15 { animation-delay: 0.75s; }
.word-delay-16 { animation-delay: 0.8s; }
.word-delay-17 { animation-delay: 0.85s; }
.word-delay-18 { animation-delay: 0.9s; }
.word-delay-19 { animation-delay: 0.95s; }
.word-delay-20 { animation-delay: 1.0s; }

/* Element animation delays */
.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.3s; }
.delay-3 { animation-delay: 0.5s; }
.delay-4 { animation-delay: 0.7s; }
.delay-5 { animation-delay: 0.9s; }

/*
3. SPECIAL ELEMENT ANIMATIONS
---------------------------
*/

/* For non-text elements like buttons and images */
.intro-element {
    opacity: 0;
    transform: translateY(30px);
    animation: elementRise 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    transform-origin: bottom center; /* Set transform origin to bottom */
}

/* Special handling for CTA button */
.cta-button.intro {
    overflow: hidden;
    position: relative;
    display: inline-block;
    color: transparent; /* Hide the original text */
}

.cta-button.intro::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateY(100%);
    animation: buttonRise 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    animation-delay: 0.5s;
    opacity: 1; /* Ensure visibility */
    color: inherit; /* Inherit text color */
    font-size: inherit; /* Inherit font size */
    font-weight: inherit; /* Inherit font weight */
    text-decoration: inherit; /* Inherit text decoration */
}

@keyframes buttonRise {
    0% {
        transform: translateY(100%);
    }
    100% {
        transform: translateY(0);
    }
}

/* Animation for elements rising in place */
@keyframes elementRise {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

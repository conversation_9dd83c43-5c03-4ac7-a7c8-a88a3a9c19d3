/* 
==============================================
MINIMAL IDLE ANIMATION
==============================================
*/

/* Text shadow pulse animation */
.text-shadow-pulse {
    animation: textShadowPulse 8s infinite;
}

/* Different timing for each headline */
h1.text-shadow-pulse {
    animation-delay: 0s;
}

h2.text-shadow-pulse {
    animation-delay: -3s;
}

h3.text-shadow-pulse {
    animation-delay: -5s;
}

/* Text shadow animation */
@keyframes textShadowPulse {
    0%, 100% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    
    /* First glitch moment */
    2% {
        text-shadow: 1px 0 0 rgba(255, 0, 0, 0.6), -1px 0 0 rgba(0, 0, 255, 0.6);
    }
    3% {
        text-shadow: -1px 0 0 rgba(255, 0, 0, 0.6), 1px 0 0 rgba(0, 0, 255, 0.6);
    }
    4% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    
    /* Second glitch moment */
    30% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    30.5% {
        text-shadow: 2px 0 0 rgba(255, 0, 0, 0.8), -2px 0 0 rgba(0, 0, 255, 0.8);
    }
    30.7% {
        text-shadow: -2px 0 0 rgba(255, 0, 0, 0.8), 2px 0 0 rgba(0, 0, 255, 0.8);
    }
    31% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    
    /* Third glitch moment */
    58% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    58.2% {
        text-shadow: 1px 0 0 rgba(255, 0, 0, 0.7), -1px 0 0 rgba(0, 0, 255, 0.7);
    }
    58.4% {
        text-shadow: -1px 0 0 rgba(255, 0, 0, 0.7), 1px 0 0 rgba(0, 0, 255, 0.7);
    }
    58.6% {
        text-shadow: 1px 0 0 rgba(255, 0, 0, 0.7), -1px 0 0 rgba(0, 0, 255, 0.7);
    }
    58.8% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    
    /* Fourth glitch moment */
    80% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    80.5% {
        text-shadow: -1px 0 0 rgba(255, 0, 0, 0.6), 1px 0 0 rgba(0, 0, 255, 0.6);
    }
    81% {
        text-shadow: 1px 0 0 rgba(255, 0, 0, 0.6), -1px 0 0 rgba(0, 0, 255, 0.6);
    }
    81.5% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
}

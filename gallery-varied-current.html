<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Artchop Gallery - Varied Grid</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hanken+Grotesk:wght@400;500;700&family=Syncopate:wght@400;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Hanken Grotesk', sans-serif;
            background-color: #000000;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* Typography */
        h2 {
            font-family: 'Syncopate', sans-serif;
            font-weight: 700;
            text-transform: uppercase;
            font-size: clamp(1.8rem, 5vw, 4rem);
            letter-spacing: -0.05em;
            line-height: 0.9;
            margin-bottom: 30px;
            text-align: left;
        }

        /* Gallery Section */
        .gallery-section {
            min-height: 100vh;
            padding: 100px 40px;
            background-color: #000000;
            position: relative;
        }

        .gallery-content {
            max-width: 1200px;
            margin: 0 auto;
            text-align: left;
        }

        .gallery-header {
            margin-bottom: 30px;
        }

        /* Varied Grid Layout */
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            grid-auto-rows: 200px;
        }

        .gallery-item {
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease;
            border: 2px solid transparent;
            background: #111;
        }

        /* Different sizes for variety */
        .gallery-item.size-small {
            grid-row: span 1;
            grid-column: span 1;
        }

        .gallery-item.size-medium {
            grid-row: span 2;
            grid-column: span 1;
        }

        .gallery-item.size-large {
            grid-row: span 2;
            grid-column: span 2;
        }

        .gallery-item.size-wide {
            grid-row: span 1;
            grid-column: span 2;
        }

        .gallery-item.size-tall {
            grid-row: span 3;
            grid-column: span 1;
        }

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .gallery-item:hover {
            transform: scale(1.02);
            border-color: #ffffff;
        }

        .gallery-item:hover img {
            transform: scale(1.1);
        }

        /* Hover overlay with magnifying glass */
        .gallery-item::after {
            content: '🔍';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2rem;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 2;
        }

        .gallery-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 1;
        }

        .gallery-item:hover::before,
        .gallery-item:hover::after {
            opacity: 1;
        }

        /* Modal Styles */
        .gallery-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .gallery-modal.active {
            opacity: 1;
        }

        .modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-image {
            max-width: 100%;
            max-height: 90vh;
            object-fit: contain;
            transform: scale(0.8);
            opacity: 0;
            transition: transform 0.5s ease-out, opacity 0.5s ease-out;
        }

        .modal-image.active {
            transform: scale(1);
            opacity: 1;
        }

        .modal-close {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 30px;
            height: 30px;
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1001;
            border: 1px solid white;
        }

        .modal-close::before,
        .modal-close::after {
            content: '';
            position: absolute;
            width: 15px;
            height: 2px;
            background-color: white;
        }

        .modal-close::before {
            transform: rotate(45deg);
        }

        .modal-close::after {
            transform: rotate(-45deg);
        }

        .modal-nav {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            pointer-events: none;
            z-index: 1001;
        }

        .modal-prev,
        .modal-next {
            width: 40px;
            height: 40px;
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            pointer-events: auto;
            z-index: 1002;
            border: 1px solid white;
        }

        .modal-prev::before,
        .modal-next::before {
            content: '';
            width: 10px;
            height: 10px;
            border-style: solid;
            border-color: white;
            border-width: 0 0 2px 2px;
            display: inline-block;
        }

        .modal-prev::before {
            transform: rotate(45deg);
        }

        .modal-next::before {
            transform: rotate(-135deg);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .gallery-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 10px;
                grid-auto-rows: 150px;
            }

            .gallery-item.size-large,
            .gallery-item.size-wide {
                grid-row: span 1;
                grid-column: span 2;
            }

            .gallery-item.size-tall {
                grid-row: span 2;
                grid-column: span 1;
            }
        }
    </style>
</head>
<body>
    <!-- Gallery Section -->
    <section class="gallery-section">
        <div class="gallery-content">
            <!-- Gallery Header -->
            <div class="gallery-header">
                <h2>OUR WORK</h2>
            </div>

            <!-- Gallery Grid -->
            <div class="gallery-grid" id="gallery">
                <!-- Gallery items will be generated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Gallery Modal -->
    <div class="gallery-modal" id="gallery-modal">
        <div class="modal-content">
            <img class="modal-image" id="modal-image" src="" alt="">
            <div class="modal-nav">
                <div class="modal-prev" id="modal-prev"></div>
                <div class="modal-next" id="modal-next"></div>
            </div>
        </div>
        <div class="modal-close" id="modal-close"></div>
    </div>

    <script>
        // Gallery images (using your existing images)
        const galleryImages = [
            'images/gallery/image1.jpg',
            'images/gallery/image2.jpg',
            'images/gallery/image3.jpg',
            'images/gallery/image4.jpg',
            'images/gallery/image5.jpg',
            'images/gallery/image6.jpg',
            'images/gallery/image7.jpg',
            'images/gallery/image8.jpg',
            'images/gallery/image9.jpg',
            'images/gallery/image10.jpg',
            'images/gallery/image11.jpg',
            'images/gallery/image12.jpg',
            'images/gallery/image13.jpg',
            'images/gallery/image14.jpg',
            'images/gallery/image15.jpg',
            'images/gallery/image16.jpg'
        ];

        // Size classes for variety
        const sizeClasses = [
            'size-small', 'size-small', 'size-small', 'size-small', // More small items
            'size-medium', 'size-medium', // Some medium items
            'size-large', // Fewer large items
            'size-wide', 'size-wide', // Some wide items
            'size-tall' // Few tall items
        ];

        let currentIndex = 0;

        function createGalleryItem(imageSrc, index) {
            const item = document.createElement('div');
            item.className = 'gallery-item';
            item.dataset.index = index;
            
            // Assign a size class
            const sizeClass = sizeClasses[index % sizeClasses.length];
            item.classList.add(sizeClass);
            
            const img = document.createElement('img');
            img.src = imageSrc;
            img.alt = `Gallery image ${index + 1}`;
            img.loading = 'lazy';
            
            item.appendChild(img);
            
            // Add click handler
            item.addEventListener('click', () => {
                openModal(index);
            });
            
            return item;
        }

        function generateGallery() {
            const gallery = document.getElementById('gallery');
            gallery.innerHTML = '';
            
            galleryImages.forEach((imageSrc, index) => {
                const item = createGalleryItem(imageSrc, index);
                gallery.appendChild(item);
            });

            // Add entrance animations
            setTimeout(() => {
                document.querySelectorAll('.gallery-item').forEach((item, index) => {
                    setTimeout(() => {
                        item.style.opacity = '0';
                        item.style.transform = 'translateY(20px)';
                        item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                        
                        setTimeout(() => {
                            item.style.opacity = '1';
                            item.style.transform = 'translateY(0)';
                        }, 50);
                    }, index * 100);
                });
            }, 100);
        }

        // Modal functionality
        function openModal(index) {
            currentIndex = index;
            const modal = document.getElementById('gallery-modal');
            const modalImage = document.getElementById('modal-image');
            
            modalImage.src = galleryImages[index];
            modal.style.display = 'flex';
            
            setTimeout(() => {
                modal.classList.add('active');
                modalImage.classList.add('active');
            }, 10);
        }

        function closeModal() {
            const modal = document.getElementById('gallery-modal');
            const modalImage = document.getElementById('modal-image');
            
            modal.classList.remove('active');
            modalImage.classList.remove('active');
            
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        function nextImage() {
            currentIndex = (currentIndex + 1) % galleryImages.length;
            const modalImage = document.getElementById('modal-image');
            
            modalImage.classList.remove('active');
            setTimeout(() => {
                modalImage.src = galleryImages[currentIndex];
                modalImage.classList.add('active');
            }, 250);
        }

        function prevImage() {
            currentIndex = (currentIndex - 1 + galleryImages.length) % galleryImages.length;
            const modalImage = document.getElementById('modal-image');
            
            modalImage.classList.remove('active');
            setTimeout(() => {
                modalImage.src = galleryImages[currentIndex];
                modalImage.classList.add('active');
            }, 250);
        }

        // Event listeners
        document.getElementById('modal-close').addEventListener('click', closeModal);
        document.getElementById('modal-prev').addEventListener('click', prevImage);
        document.getElementById('modal-next').addEventListener('click', nextImage);

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            const modal = document.getElementById('gallery-modal');
            if (modal.style.display === 'flex') {
                if (e.key === 'Escape') closeModal();
                if (e.key === 'ArrowLeft') prevImage();
                if (e.key === 'ArrowRight') nextImage();
            }
        });

        // Close modal when clicking outside
        document.getElementById('gallery-modal').addEventListener('click', (e) => {
            if (e.target.id === 'gallery-modal') {
                closeModal();
            }
        });

        // Initialize gallery
        generateGallery();
    </script>
</body>
</html>

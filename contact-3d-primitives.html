<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Section with Advanced 3D Primitives</title>
    <!-- Include Three.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Hanken Grotesk', sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            overflow-x: hidden;
            cursor: default;
        }

        /* Typography */
        h2 {
            font-family: 'Syncopate', sans-serif;
            font-weight: 700;
            text-transform: uppercase;
            font-size: clamp(1.8rem, 5vw, 4rem);
            letter-spacing: -0.05em;
            line-height: 0.9;
            margin-bottom: 25px;
        }

        /* 3D Background Canvas */
        #bg-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        /* Contact Section */
        .contact-section {
            position: relative;
            min-height: 100vh;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 100px 40px;
            z-index: 1;
        }

        .contact-content {
            max-width: 600px;
            text-align: left;
        }

        /* Controls */
        .controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .controls button {
            background: #2ecc71;
            color: white;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- 3D Background Canvas -->
    <canvas id="bg-canvas"></canvas>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="contact-content">
            <h2>CONTACT</h2>
        </div>
    </section>

    <!-- Controls -->
    <div class="controls">
        <button id="toggle-wireframe">Toggle Wireframe</button>
        <button id="toggle-rotation">Toggle Auto-Rotation</button>
        <button id="cycle-primitives">Cycle Primitive Types</button>
    </div>

    <script>
        // Three.js setup
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({
            canvas: document.getElementById('bg-canvas'),
            antialias: true,
            alpha: true
        });
        
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        
        // Set background color (green with some transparency)
        scene.background = new THREE.Color(0x2ecc71);
        scene.fog = new THREE.FogExp2(0x2ecc71, 0.02);
        
        // Camera position
        camera.position.set(0, 0, 30);
        
        // Create a group to hold all objects
        const group = new THREE.Group();
        scene.add(group);
        
        // Create particles
        const particlesGeometry = new THREE.BufferGeometry();
        const particlesCount = 1500;
        
        const posArray = new Float32Array(particlesCount * 3);
        
        // Fill arrays with random positions
        for (let i = 0; i < particlesCount * 3; i += 3) {
            // Position
            posArray[i] = (Math.random() - 0.5) * 100;
            posArray[i + 1] = (Math.random() - 0.5) * 100;
            posArray[i + 2] = (Math.random() - 0.5) * 100;
        }
        
        particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
        
        // Material
        const particlesMaterial = new THREE.PointsMaterial({
            size: 0.2,
            color: 0xffffff,
            transparent: true,
            opacity: 0.8,
            sizeAttenuation: true
        });
        
        // Create points
        const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
        group.add(particlesMesh);
        
        // Define all available primitive types
        const primitiveCreators = [
            // Basic primitives
            () => new THREE.BoxGeometry(5, 5, 5),
            () => new THREE.SphereGeometry(3, 16, 16),
            () => new THREE.ConeGeometry(3, 6, 16),
            () => new THREE.CylinderGeometry(2, 2, 6, 16),
            () => new THREE.TorusGeometry(3, 1, 16, 50),
            () => new THREE.TorusKnotGeometry(3, 1, 64, 8, 2, 3),
            
            // Platonic solids
            () => new THREE.TetrahedronGeometry(4),
            () => new THREE.OctahedronGeometry(4),
            () => new THREE.DodecahedronGeometry(4),
            () => new THREE.IcosahedronGeometry(4),
            
            // Other interesting shapes
            () => new THREE.RingGeometry(2, 4, 16),
            () => new THREE.CircleGeometry(4, 16),
            () => new THREE.PlaneGeometry(6, 6, 1, 1),
        ];
        
        // Material for the primitives
        let wireframe = true;
        const material = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            wireframe: wireframe,
            transparent: true,
            opacity: 0.3
        });
        
        // Create meshes
        const meshes = [];
        let currentPrimitiveSet = 0;
        
        function createPrimitives() {
            // Clear existing meshes
            meshes.forEach(mesh => group.remove(mesh));
            meshes.length = 0;
            
            // Create new set of primitives
            const startIndex = (currentPrimitiveSet * 5) % primitiveCreators.length;
            
            for (let i = 0; i < 15; i++) {
                const index = (startIndex + i) % primitiveCreators.length;
                const geometry = primitiveCreators[index]();
                const mesh = new THREE.Mesh(geometry, material.clone());
                
                mesh.position.x = (Math.random() - 0.5) * 80;
                mesh.position.y = (Math.random() - 0.5) * 80;
                mesh.position.z = (Math.random() - 0.5) * 80;
                
                mesh.rotation.x = Math.random() * Math.PI;
                mesh.rotation.y = Math.random() * Math.PI;
                
                mesh.scale.setScalar(Math.random() * 0.5 + 0.5);
                
                group.add(mesh);
                meshes.push(mesh);
            }
        }
        
        // Initial creation
        createPrimitives();
        
        // Mouse movement tracking
        let mouseX = 0;
        let mouseY = 0;
        let targetX = 0;
        let targetY = 0;
        
        // Auto-rotation flag
        let autoRotate = false;
        
        // Track mouse position
        document.addEventListener('mousemove', (event) => {
            // Calculate normalized mouse position (-1 to 1)
            mouseX = (event.clientX / window.innerWidth) * 2 - 1;
            mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
        });
        
        // For touch devices
        document.addEventListener('touchmove', (event) => {
            if (event.touches.length > 0) {
                // Calculate normalized touch position (-1 to 1)
                mouseX = (event.touches[0].clientX / window.innerWidth) * 2 - 1;
                mouseY = -(event.touches[0].clientY / window.innerHeight) * 2 + 1;
                
                // Prevent scrolling
                event.preventDefault();
            }
        }, { passive: false });
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            if (autoRotate) {
                // Auto-rotate the entire group
                group.rotation.y += 0.005;
                group.rotation.x += 0.002;
            } else {
                // Smooth camera movement based on mouse position
                targetX = mouseX * 10;
                targetY = mouseY * 10;
                
                camera.position.x += (targetX - camera.position.x) * 0.05;
                camera.position.y += (targetY - camera.position.y) * 0.05;
                camera.lookAt(scene.position);
            }
            
            // Rotate meshes slightly
            meshes.forEach((mesh, i) => {
                mesh.rotation.x += 0.001 * (i % 2 ? 1 : -1);
                mesh.rotation.y += 0.001 * (i % 3 ? 1 : -1);
            });
            
            // Subtle particle rotation
            particlesMesh.rotation.y += 0.0005;
            
            renderer.render(scene, camera);
        }
        
        animate();
        
        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // Controls
        document.getElementById('toggle-wireframe').addEventListener('click', function() {
            wireframe = !wireframe;
            meshes.forEach(mesh => {
                mesh.material.wireframe = wireframe;
            });
            this.textContent = wireframe ? 'Show Solid' : 'Show Wireframe';
        });
        
        document.getElementById('toggle-rotation').addEventListener('click', function() {
            autoRotate = !autoRotate;
            this.textContent = autoRotate ? 'Use Mouse Control' : 'Use Auto-Rotation';
        });
        
        document.getElementById('cycle-primitives').addEventListener('click', function() {
            currentPrimitiveSet++;
            createPrimitives();
        });
    </script>
</body>
</html>

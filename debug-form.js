// Debug script to make the form visible
document.addEventListener('DOMContentLoaded', function() {
    console.log('Debug script loaded');
    
    // Find the contact form and its container
    const contactWidget = document.querySelector('.contact-widget');
    const contactForm = document.getElementById('contact-form');
    
    if (contactWidget) {
        console.log('Found contact widget');
        // Force make it visible
        contactWidget.style.opacity = '1';
        contactWidget.style.visibility = 'visible';
        contactWidget.style.display = 'block';
    } else {
        console.error('Contact widget not found!');
    }
    
    if (contactForm) {
        console.log('Found contact form');
        // Force make it visible
        contactForm.style.opacity = '1';
        contactForm.style.visibility = 'visible';
        contactForm.style.display = 'block';
    } else {
        console.error('Contact form not found!');
    }
    
    // Log all form elements
    const formElements = document.querySelectorAll('form, .form-group, input, textarea, button');
    console.log('Form elements found:', formElements.length);
    formElements.forEach(el => {
        console.log('Element:', el.tagName, 'ID:', el.id, 'Classes:', el.className);
    });
});

// Simple Gallery Implementation

document.addEventListener('DOMContentLoaded', function() {
    const galleryGrid = document.getElementById('gallery-grid');
    if (!galleryGrid) return;

    // Gallery images
    const imagePaths = [];
    for (let i = 1; i <= 28; i++) {
        imagePaths.push(`images/gallery/AC_Gallery${i}.jpeg`);
    }

    // Create gallery items
    imagePaths.forEach((imagePath, index) => {
        const galleryItem = document.createElement('div');
        galleryItem.className = 'gallery-item';
        galleryItem.dataset.index = index;
        
        const img = new Image();
        img.onload = function() {
            galleryItem.classList.add('loaded');
        };
        img.onerror = function() {
            console.error('Failed to load image:', imagePath);
            galleryItem.style.background = '#f0f0f0';
            galleryItem.innerHTML = `<div style="padding: 10px; text-align: center; color: #999;">Image ${index + 1} not found</div>`;
        };
        
        img.src = imagePath;
        img.alt = `Gallery image ${index + 1}`;
        img.loading = 'lazy';
        
        galleryItem.appendChild(img);
        galleryGrid.appendChild(galleryItem);
    });
});

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded, initializing gallery...');
    
    // Gallery elements
    const galleryGrid = document.getElementById('gallery-grid');
    
    if (!galleryGrid) {
        console.error('Gallery grid element not found!');
        return;
    }
    
    console.log('Gallery grid found:', galleryGrid);
    
    const modal = document.getElementById('gallery-modal');
    const modalImage = document.getElementById('modal-image');
    const modalClose = document.getElementById('modal-close');
    const modalPrev = document.getElementById('modal-prev');
    const modalNext = document.getElementById('modal-next');

    // Gallery state
    let galleryImages = [];
    let currentIndex = 0;

    // Initialize gallery
    initGallery();

    // Event listeners
    if (modalClose) modalClose.addEventListener('click', closeModal);
    if (modalPrev) modalPrev.addEventListener('click', prevImage);
    if (modalNext) modalNext.addEventListener('click', nextImage);

    // Close modal when clicking outside the image
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });
    }

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (modal && modal.style.display === 'flex') {
            if (e.key === 'Escape') {
                closeModal();
            } else if (e.key === 'ArrowLeft') {
                prevImage();
            } else if (e.key === 'ArrowRight') {
                nextImage();
            }
        }
    });

    // Initialize gallery
    function initGallery() {
        console.log('Initializing gallery...');
        try {
            // Using actual gallery images from images/gallery directory
            const imagePaths = [
                'images/gallery/AC_Gallery1.jpeg',
                'images/gallery/AC_Gallery2.jpeg',
                'images/gallery/AC_Gallery3.jpeg',
                'images/gallery/AC_Gallery4.jpeg',
                'images/gallery/AC_Gallery5.jpeg',
                'images/gallery/AC_Gallery6.jpeg',
                'images/gallery/AC_Gallery7.jpeg',
                'images/gallery/AC_Gallery8.jpeg',
                'images/gallery/AC_Gallery9.jpeg',
                'images/gallery/AC_Gallery10.jpeg',
                'images/gallery/AC_Gallery11.jpeg',
                'images/gallery/AC_Gallery12.jpeg',
                'images/gallery/AC_Gallery13.jpeg',
                'images/gallery/AC_Gallery14.jpeg',
                'images/gallery/AC_Gallery15.jpeg',
                'images/gallery/AC_Gallery16.jpeg',
                'images/gallery/AC_Gallery17.jpeg',
                'images/gallery/AC_Gallery18.jpeg',
                'images/gallery/AC_Gallery19.jpeg',
                'images/gallery/AC_Gallery20.jpeg',
                'images/gallery/AC_Gallery21.jpeg',
                'images/gallery/AC_Gallery22.jpeg',
                'images/gallery/AC_Gallery23.jpeg',
                'images/gallery/AC_Gallery24.jpeg',
                'images/gallery/AC_Gallery25.jpeg',
                'images/gallery/AC_Gallery26.jpeg',
                'images/gallery/AC_Gallery27.jpeg',
                'images/gallery/AC_Gallery28.jpeg'
            ];

            galleryImages = imagePaths;

            // Size classes for variety
            const sizeClasses = [
                'size-small', 'size-small', 'size-small', 'size-small',
                'size-medium', 'size-medium',
                'size-large',
                'size-wide', 'size-wide',
                'size-tall'
            ];

            console.log(`Creating ${galleryImages.length} gallery items...`);
            // Create gallery items
            galleryImages.forEach((imagePath, index) => {
                console.log(`Creating gallery item ${index + 1}: ${imagePath}`);
                const galleryItem = document.createElement('div');
                galleryItem.className = 'gallery-item';
                galleryItem.dataset.index = index;
                
                // Set initial styles for animation
                galleryItem.style.opacity = '0';
                galleryItem.style.transform = 'translateY(20px)';
                galleryItem.style.transition = 'opacity 0.7s ease-out, transform 0.7s ease-out';
                galleryItem.style.transitionDelay = `${index * 0.03}s`;

                // Assign a size class for varied layout
                const sizeClass = sizeClasses[index % sizeClasses.length];
                galleryItem.classList.add(sizeClass);

                const img = document.createElement('img');
                
                // Use native lazy loading if available
                if ('loading' in HTMLImageElement.prototype) {
                    img.loading = 'lazy';
                    img.src = imagePath;
                } else {
                    // Fallback for browsers without native lazy loading
                    img.dataset.src = imagePath;
                    img.src = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" ' +
                        'viewBox="0 0 1 1"%3E%3C/svg%3E'; // Tiny placeholder
                    img.classList.add('lazy-image');
                }
                img.alt = `Gallery image ${index + 1}`;

                // Create hover overlay element
                const hoverOverlay = document.createElement('div');
                hoverOverlay.className = 'hover-overlay';

                // Create magnify icon element
                const magnifyIcon = document.createElement('div');
                magnifyIcon.className = 'magnify-icon';

                // Store the image path for animations
                galleryItem.dataset.imagePath = imagePath;

                // Add animation-ready class
                galleryItem.classList.add('animation-ready');


                galleryItem.appendChild(img);
                galleryItem.appendChild(hoverOverlay);
                galleryItem.appendChild(magnifyIcon);
                galleryGrid.appendChild(galleryItem);

                // Add click event
                galleryItem.addEventListener('click', () => {
                    openModal(index);
                });
            });


            // Initialize lazy loading for browsers without native support
            if (!('loading' in HTMLImageElement.prototype)) {
                console.log('Initializing lazy loading...');
                initLazyLoading();
            } else {
                console.log('Using native lazy loading');
            }
            
            // Initialize intersection observer for animations
            console.log('Initializing intersection observer...');
            initIntersectionObserver();
            
            console.log('Gallery initialization complete');

        } catch (error) {
            console.error('Error loading gallery images:', error);
        }
    }


    // Initialize intersection observer for animations
    function initIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // Observe all gallery items
        document.querySelectorAll('.gallery-item').forEach(item => {
            observer.observe(item);
        });
    }


    // Initialize lazy loading for browsers without native support
    function initLazyLoading() {
        const lazyImages = document.querySelectorAll('.lazy-image');

        if (lazyImages.length > 0) {
            // Use Intersection Observer if available
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy-image');
                            imageObserver.unobserve(img);
                        }
                    });
                }, {
                    rootMargin: '50px 0px', // Load images a bit before they come into view
                    threshold: 0.01
                });

                lazyImages.forEach(img => imageObserver.observe(img));
            } else {
                // Fallback for browsers without IntersectionObserver
                function lazyLoad() {
                    lazyImages.forEach(img => {
                        if (img.getBoundingClientRect().top <= window.innerHeight &&
                            img.getBoundingClientRect().bottom >= 0 &&
                            getComputedStyle(img).display !== 'none') {
                            img.src = img.dataset.src;
                            img.classList.remove('lazy-image');
                        }
                    });
                }

                
                // Initial check
                lazyLoad();
                
                // Check on scroll and resize
                window.addEventListener('scroll', lazyLoad);
                window.addEventListener('resize', lazyLoad);
            }
        }
    }


    // Function to open modal with expand animation
    function openModal(index) {
        currentIndex = index;
        const imagePath = galleryImages[index];
        
        if (!modal || !modalImage) return;
        
        // Set the image source
        modalImage.src = imagePath;
        
        // Show the modal with animation
        modal.style.display = 'flex';
        setTimeout(() => {
            modal.classList.add('active');
        }, 10);
        
        // Prevent scrolling on body when modal is open
        document.body.style.overflow = 'hidden';
        
        // Update selected state in grid
        updateSelectedState();
    }
    
    // Function to close modal with animation
    function closeModal() {
        if (!modal) return;
        
        // Start fade out animation
        modal.classList.remove('active');
        
        // Wait for animation to complete before hiding
        setTimeout(() => {
            modal.style.display = 'none';
            
            // Re-enable scrolling on body
            document.body.style.overflow = '';
            
            // Update selected state in grid
            updateSelectedState();
        }, 300);
    }
    
    // Function to navigate to previous image with animation
    function prevImage() {
        currentIndex = (currentIndex - 1 + galleryImages.length) % galleryImages.length;
        modalImage.src = galleryImages[currentIndex];
        updateSelectedState();
    }
    
    // Function to navigate to next image with animation
    function nextImage() {
        currentIndex = (currentIndex + 1) % galleryImages.length;
        modalImage.src = galleryImages[currentIndex];
        updateSelectedState();
    }
    
    // Update selected state in grid
    function updateSelectedState() {
        const items = document.querySelectorAll('.gallery-item');
        items.forEach((item, index) => {
            if (index === currentIndex) {
                item.classList.add('selected');
                // Scroll to selected item if not in view
                if (!isElementInViewport(item)) {
                    item.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }
            } else {
                item.classList.remove('selected');
            }
        });
    }
    
    // Helper function to check if an element is in the viewport
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
});

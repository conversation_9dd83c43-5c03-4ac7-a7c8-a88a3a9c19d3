<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thank You - Artchop!</title>
    <link rel="icon" type="image/png" href="images/flavi.png">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Hanken+Grotesk:wght@400;500;700&family=Syncopate:wght@400;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Hanken Grotesk', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .thank-you-container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: #FF42A1;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Syncopate', sans-serif;
            font-weight: 700;
            font-size: 24px;
            color: white;
        }
        
        h1 {
            font-family: 'Syncopate', sans-serif;
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #FF42A1;
            text-transform: uppercase;
            letter-spacing: -0.05em;
        }
        
        p {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .cta-button {
            display: inline-block;
            padding: 15px 30px;
            background: #FF42A1;
            color: white;
            text-decoration: none;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            border-radius: 0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: white;
            transition: left 0.3s ease;
            z-index: -1;
        }
        
        .cta-button:hover::before {
            left: 0;
        }
        
        .cta-button:hover {
            color: #FF42A1;
        }
        
        .checkmark {
            width: 60px;
            height: 60px;
            margin: 0 auto 20px;
            background: #00ff88;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            animation: checkmarkPop 0.6s ease-out;
        }
        
        @keyframes checkmarkPop {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        @media (max-width: 768px) {
            .thank-you-container {
                margin: 20px;
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            p {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="thank-you-container">
        <div class="checkmark">✓</div>
        <div class="logo">A!</div>
        <h1>Thank You!</h1>
        <p>Your message has been sent successfully. We'll get back to you within 24 hours to discuss your project and how we can help bring your vision to life.</p>
        <a href="index.html" class="cta-button">Back to Home</a>
    </div>
    
    <script>
        // Auto redirect after 10 seconds
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 10000);
    </script>
</body>
</html>

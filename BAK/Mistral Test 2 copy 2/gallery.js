// Gallery Implementation

// Global variable to track if gallery is initialized
let isGalleryInitialized = false;

// Function to reset gallery animations
function resetGalleryAnimations() {
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    // Reset all items to initial state
    galleryItems.forEach(item => {
        // Remove revealed class and reset opacity/transform
        item.classList.remove('revealed');
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        
        // Reset any animation classes
        item.classList.remove(
            'animate-slot-scroll',
            'animate-slot-scroll-horizontal',
            'animate-slot-scroll-vertical',
            'animate-zoom-pan',
            'animate-zoom-pan-circular',
            'animate-zoom-pan-left',
            'animate-zoom-pan-right',
            'animate-fade-scale'
        );
        
        // Remove any animation containers
        const containers = item.querySelectorAll('.scroll-container, .scroll-container-vertical, .constant-container, .fade-container');
        containers.forEach(container => container.remove());
    });
}

// Function to start the gallery animations
function startSequentialAnimation() {
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    // Reset all items to initial state
    galleryItems.forEach(item => {
        item.classList.remove('revealed');
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
    });
    
    // Function to reveal items sequentially
    function revealNextItem(index) {
        if (index >= galleryItems.length) {
            // After all items are revealed, start the animations
            if (window.startGalleryAnimations) {
                window.startGalleryAnimations();
            }
            return;
        }
        
        // Force reflow to ensure the reset is applied before starting animation
        void galleryItems[index].offsetHeight;


        // Reveal current item
        galleryItems[index].classList.add('revealed');

        // Queue next item with a short delay
        setTimeout(() => {
            revealNextItem(index + 1);
        }, 100); // Fast 100ms delay between items
    }

    // Start with the first item
    revealNextItem(0);
}

// Initialize gallery on DOM content loaded
document.addEventListener('DOMContentLoaded', function() {
    // Make functions globally available
    window.resetGalleryAnimations = resetGalleryAnimations;
    
    // Initialize the gallery function
    window.initGallery = function(resetOnly = false) {
        // Reset animations in any case
        resetGalleryAnimations();
        
        // If we're only resetting, don't proceed with full initialization
        if (resetOnly) {
            return;
        }
        
        // Mark as initialized
        isGalleryInitialized = true;
        
        // Start the sequential animation
        startSequentialAnimation();
    };
    
    // Initialize lazy loading if needed
    if (typeof initLazyLoading === 'function') {
        initLazyLoading();
    }
    
    // Optimize animations when page is not visible
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            // Pause intensive animations when page is not visible
            document.querySelectorAll('.animate-slot-scroll').forEach(item => {
                const containers = item.querySelectorAll('.scroll-container, .scroll-container-vertical');
                containers.forEach(container => {
                    container.style.animationPlayState = 'paused';
                });

                // Pause requestAnimationFrame animations
                const reels = item.querySelectorAll('[id^="horizontal-constant-reel"], [id^="vertical-constant-reel"]');
                reels.forEach(reel => {
                    if (reel.id && window.animationIds && window.animationIds[reel.id]) {
                        cancelAnimationFrame(window.animationIds[reel.id]);
                    }
                });
            });

            // Pause zoom-pan animations
            document.querySelectorAll('.animate-zoom-pan img').forEach(img => {
                img.style.animationPlayState = 'paused';
            });
        } else {
            // Resume animations when page becomes visible again
            document.querySelectorAll('.animate-slot-scroll').forEach(item => {
                const containers = item.querySelectorAll('.scroll-container, .scroll-container-vertical');
                containers.forEach(container => {
                    container.style.animationPlayState = 'running';
                });

                // Resume constant speed animations
                const horizontalReels = item.querySelectorAll('[id^="horizontal-constant-reel"]');
                horizontalReels.forEach(reel => {
                    if (reel.id && reel.parentNode) {
                        const reel1Id = reel.id;
                        const reel2Id = reel.id.replace('reel1', 'reel2');
                        const reel2 = document.getElementById(reel2Id);
                        if (reel2) {
                            animateHorizontalConstant(reel1Id, reel2Id);
                        }
                    }
                });

                const verticalReels = item.querySelectorAll('[id^="vertical-constant-reel"]');
                verticalReels.forEach(reel => {
                    if (reel.id && reel.parentNode) {
                        const reel1Id = reel.id;
                        const reel2Id = reel.id.replace('reel1', 'reel2');
                        const reel2 = document.getElementById(reel2Id);
                        if (reel2) {
                            animateVerticalConstant(reel1Id, reel2Id);
                        }
                    }
                });
            });

            // Resume zoom-pan animations
            document.querySelectorAll('.animate-zoom-pan img').forEach(img => {
                img.style.animationPlayState = 'running';
            });
        }
    });
    // Gallery elements
    const galleryGrid = document.getElementById('gallery-grid');
    const modal = document.getElementById('gallery-modal');
    const modalImage = document.getElementById('modal-image');
    const modalClose = document.getElementById('modal-close');
    const modalPrev = document.getElementById('modal-prev');
    const modalNext = document.getElementById('modal-next');

    // Gallery state
    let galleryImages = [];
    let currentIndex = 0;

    // Initialize gallery
    initGallery();

    // Event listeners
    modalClose.addEventListener('click', closeModal);
    modalPrev.addEventListener('click', prevImage);
    modalNext.addEventListener('click', nextImage);

    // Close modal when clicking outside the image
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (modal.style.display === 'flex') {
            if (e.key === 'Escape') {
                closeModal();
            } else if (e.key === 'ArrowLeft') {
                prevImage();
            } else if (e.key === 'ArrowRight') {
                nextImage();
            }
        }
    });

    // Initialize gallery
    function initGallery() {
        try {
            // Using actual gallery images from images/gallery directory
            const imagePaths = [
                'images/gallery/AC_Gallery1.jpeg',
                'images/gallery/AC_Gallery2.jpeg',
                'images/gallery/AC_Gallery3.jpeg',
                'images/gallery/AC_Gallery4.jpeg',
                'images/gallery/AC_Gallery5.jpeg',
                'images/gallery/AC_Gallery6.jpeg',
                'images/gallery/AC_Gallery7.jpeg',
                'images/gallery/AC_Gallery8.jpeg',
                'images/gallery/AC_Gallery9.jpeg',
                'images/gallery/AC_Gallery10.jpeg',
                'images/gallery/AC_Gallery11.jpeg',
                'images/gallery/AC_Gallery12.jpeg',
                'images/gallery/AC_Gallery13.jpeg',
                'images/gallery/AC_Gallery14.jpeg',
                'images/gallery/AC_Gallery15.jpeg',
                'images/gallery/AC_Gallery16.jpeg',
                'images/gallery/AC_Gallery17.jpeg',
                'images/gallery/AC_Gallery18.jpeg',
                'images/gallery/AC_Gallery19.jpeg',
                'images/gallery/AC_Gallery20.jpeg',
                'images/gallery/AC_Gallery21.jpeg',
                'images/gallery/AC_Gallery22.jpeg',
                'images/gallery/AC_Gallery23.jpeg',
                'images/gallery/AC_Gallery24.jpeg',
                'images/gallery/AC_Gallery25.jpeg',
                'images/gallery/AC_Gallery26.jpeg',
                'images/gallery/AC_Gallery27.jpeg',
                'images/gallery/AC_Gallery28.jpeg'
            ];

            galleryImages = imagePaths;

            // Size classes for variety
            const sizeClasses = [
                'size-small', 'size-small', 'size-small', 'size-small', // More small items
                'size-medium', 'size-medium', // Some medium items
                'size-large', // Fewer large items
                'size-wide', 'size-wide', // Some wide items
                'size-tall' // Few tall items
            ];

            // Create gallery items
            galleryImages.forEach((imagePath, index) => {
                const galleryItem = document.createElement('div');
                galleryItem.className = 'gallery-item';
                galleryItem.dataset.index = index;

                // Assign a size class for varied layout
                const sizeClass = sizeClasses[index % sizeClasses.length];
                galleryItem.classList.add(sizeClass);

                const img = document.createElement('img');
                // Use lazy loading for better performance
                if ('loading' in HTMLImageElement.prototype) {
                    // Browser supports native lazy loading
                    img.loading = 'lazy';
                    img.src = imagePath;
                } else {
                    // Fallback for browsers without native lazy loading
                    img.dataset.src = imagePath;
                    img.src = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"%3E%3C/svg%3E'; // Tiny placeholder
                    img.classList.add('lazy-image');
                }
                img.alt = `Gallery image ${index + 1}`;

                // Create hover overlay element
                const hoverOverlay = document.createElement('div');
                hoverOverlay.className = 'hover-overlay';

                // Create magnify icon element
                const magnifyIcon = document.createElement('div');
                magnifyIcon.className = 'magnify-icon';

                // Store the image path for animations
                galleryItem.dataset.imagePath = imagePath;

                // Add animation-ready class to all items
                galleryItem.classList.add('animation-ready');

                galleryItem.appendChild(img);
                galleryItem.appendChild(hoverOverlay);
                galleryItem.appendChild(magnifyIcon);
                galleryGrid.appendChild(galleryItem);

                // Add click event
                galleryItem.addEventListener('click', () => {
                    openModal(index);
                });
            });

            // Implement lazy loading for browsers without native support
            function initLazyLoading() {
                const lazyImages = document.querySelectorAll('.lazy-image');

                if (lazyImages.length > 0) {
                    // Use Intersection Observer if available
                    if ('IntersectionObserver' in window) {
                        const imageObserver = new IntersectionObserver((entries) => {
                            entries.forEach(entry => {
                                if (entry.isIntersecting) {
                                    const img = entry.target;
                                    img.src = img.dataset.src;
                                    img.classList.remove('lazy-image');
                                    imageObserver.unobserve(img);
                                }
                            });
                        }, {
                            rootMargin: '50px 0px', // Load images a bit before they come into view
                            threshold: 0.01
                        });

                        lazyImages.forEach(img => imageObserver.observe(img));
                    } else {
                        // Fallback for browsers without IntersectionObserver
                        function lazyLoad() {
                            lazyImages.forEach(img => {
                                if (img.getBoundingClientRect().top <= window.innerHeight &&
                                    img.getBoundingClientRect().bottom >= 0 &&
                                    getComputedStyle(img).display !== "none") {
                                    img.src = img.dataset.src;
                                    img.classList.remove('lazy-image');
                                }
                            });

                            // If all images have been loaded, stop checking
                            if (document.querySelectorAll('.lazy-image').length === 0) {
                                document.removeEventListener('scroll', lazyLoad);
                                window.removeEventListener('resize', lazyLoad);
                                window.removeEventListener('orientationChange', lazyLoad);
                            }
                        }

                        // Add event listeners for scroll, resize, and orientation change
                        document.addEventListener('scroll', lazyLoad);
                        window.addEventListener('resize', lazyLoad);
                        window.addEventListener('orientationChange', lazyLoad);

                        // Initial load
                        lazyLoad();
                    }
                }
            }

            // Initialize lazy loading
            initLazyLoading();
            
            // Start the sequential animation after a small delay
            setTimeout(() => {
                startSequentialAnimation();
            }, 300); // Small initial delay before starting

            // Function to handle gallery animations with true randomness and no duplicates
            window.startGalleryAnimations = function() {
                const galleryItems = document.querySelectorAll('.gallery-item');

                // Animation types from gallery-implementation.html
                const animationTypes = [
                    {
                        name: 'zoom-pan-circular',
                        apply: (item) => {
                            resetItemClasses(item);
                            item.classList.add('animate-zoom-pan');
                            item.classList.add('animate-zoom-pan-circular');

                            // Get the image element
                            const img = item.querySelector('img');
                            if (img) {
                                // Make sure animation is running
                                img.style.animationPlayState = 'running';
                            }
                        }
                    },
                    {
                        name: 'zoom-pan-left',
                        apply: (item) => {
                            resetItemClasses(item);
                            item.classList.add('animate-zoom-pan');
                            item.classList.add('animate-zoom-pan-left');

                            // Get the image element
                            const img = item.querySelector('img');
                            if (img) {
                                // Make sure animation is running
                                img.style.animationPlayState = 'running';
                            }
                        }
                    },
                    {
                        name: 'zoom-pan-right',
                        apply: (item) => {
                            resetItemClasses(item);
                            item.classList.add('animate-zoom-pan');
                            item.classList.add('animate-zoom-pan-right');

                            // Get the image element
                            const img = item.querySelector('img');
                            if (img) {
                                // Make sure animation is running
                                img.style.animationPlayState = 'running';
                            }
                        }
                    },
                    {
                        name: 'fade-scale-center',
                        apply: (item) => {
                            resetItemClasses(item);
                            item.classList.add('animate-fade-scale');

                            // Create container for fade-scale animation
                            const container = document.createElement('div');
                            container.className = 'fade-container';

                            // Create the scaled image that will fade in
                            const fadeImage = document.createElement('div');
                            fadeImage.className = 'fade-image';
                            fadeImage.style.backgroundImage = `url(${item.dataset.imagePath})`;

                            // Add to container
                            container.appendChild(fadeImage);
                            item.appendChild(container);

                            // Trigger the fade-in animation after a small delay
                            setTimeout(() => {
                                fadeImage.classList.add('active');
                            }, 50);
                        }
                    },
                    {
                        name: 'fade-scale-top',
                        apply: (item) => {
                            resetItemClasses(item);
                            item.classList.add('animate-fade-scale');

                            // Create container for fade-scale animation
                            const container = document.createElement('div');
                            container.className = 'fade-container';

                            // Create the scaled image that will fade in
                            const fadeImage = document.createElement('div');
                            fadeImage.className = 'fade-image';
                            fadeImage.style.backgroundImage = `url(${item.dataset.imagePath})`;
                            fadeImage.style.backgroundPosition = 'center top';

                            // Add to container
                            container.appendChild(fadeImage);
                            item.appendChild(container);

                            // Trigger the fade-in animation after a small delay
                            setTimeout(() => {
                                fadeImage.classList.add('active');
                            }, 50);
                        }
                    },
                    {
                        name: 'fade-scale-bottom',
                        apply: (item) => {
                            resetItemClasses(item);
                            item.classList.add('animate-fade-scale');

                            // Create container for fade-scale animation
                            const container = document.createElement('div');
                            container.className = 'fade-container';

                            // Create the scaled image that will fade in
                            const fadeImage = document.createElement('div');
                            fadeImage.className = 'fade-image';
                            fadeImage.style.backgroundImage = `url(${item.dataset.imagePath})`;
                            fadeImage.style.backgroundPosition = 'center bottom';

                            // Add to container
                            container.appendChild(fadeImage);
                            item.appendChild(container);

                            // Trigger the fade-in animation after a small delay
                            setTimeout(() => {
                                fadeImage.classList.add('active');
                            }, 50);
                        }
                    },
                    {
                        name: 'slot-scroll-horizontal',
                        apply: (item) => {
                            resetItemClasses(item);
                            item.classList.add('animate-slot-scroll');
                            item.classList.add('animate-slot-scroll-horizontal');

                            // Create container for variable speed animation
                            const container = document.createElement('div');
                            container.className = 'scroll-container';

                            // Create 10 copies of the image for seamless scrolling
                            for (let i = 0; i < 10; i++) {
                                const img = document.createElement('div');
                                img.className = i === 0 ? 'scroll-image' : 'scroll-image-duplicate';
                                img.style.backgroundImage = `url(${item.dataset.imagePath})`;
                                container.appendChild(img);
                            }

                            item.appendChild(container);
                        }
                    },
                    {
                        name: 'slot-scroll-vertical',
                        apply: (item) => {
                            resetItemClasses(item);
                            item.classList.add('animate-slot-scroll');
                            item.classList.add('animate-slot-scroll-vertical');

                            // Create container for variable speed animation
                            const container = document.createElement('div');
                            container.className = 'scroll-container-vertical';

                            // Create 10 copies of the image for seamless scrolling
                            for (let i = 0; i < 10; i++) {
                                const img = document.createElement('div');
                                img.className = i === 0 ? 'scroll-image' : 'scroll-image-duplicate';
                                img.style.backgroundImage = `url(${item.dataset.imagePath})`;
                                container.appendChild(img);
                            }

                            item.appendChild(container);
                        }
                    },
                    {
                        name: 'slot-scroll-horizontal-slow',
                        apply: (item) => {
                            resetItemClasses(item);
                            item.classList.add('animate-slot-scroll');

                            // Create container for constant speed animation
                            const container = document.createElement('div');
                            container.className = 'constant-container';

                            // Create two reels for seamless scrolling
                            const reel1 = document.createElement('div');
                            reel1.className = 'horizontal-reel';
                            reel1.id = `horizontal-constant-reel1-${Date.now()}`;
                            reel1.style.left = '0';

                            const reel2 = document.createElement('div');
                            reel2.className = 'horizontal-reel';
                            reel2.id = `horizontal-constant-reel2-${Date.now()}`;
                            reel2.style.left = '-100%';

                            // Create 10 symbols for each reel
                            for (let i = 0; i < 10; i++) {
                                const symbol1 = document.createElement('div');
                                symbol1.className = 'horizontal-symbol';
                                symbol1.style.backgroundImage = `url(${item.dataset.imagePath})`;
                                reel1.appendChild(symbol1);

                                const symbol2 = document.createElement('div');
                                symbol2.className = 'horizontal-symbol';
                                symbol2.style.backgroundImage = `url(${item.dataset.imagePath})`;
                                reel2.appendChild(symbol2);
                            }

                            // Add reels to container
                            container.appendChild(reel1);
                            container.appendChild(reel2);

                            // Add container to item
                            item.appendChild(container);

                            // Start constant speed animation
                            animateHorizontalConstant(reel1.id, reel2.id);
                        }
                    },
                    {
                        name: 'slot-scroll-vertical-slow',
                        apply: (item) => {
                            resetItemClasses(item);
                            item.classList.add('animate-slot-scroll');

                            // Create container for constant speed animation
                            const container = document.createElement('div');
                            container.className = 'constant-container';

                            // Create two reels for seamless scrolling
                            const reel1 = document.createElement('div');
                            reel1.className = 'vertical-reel';
                            reel1.id = `vertical-constant-reel1-${Date.now()}`;
                            reel1.style.top = '0';

                            const reel2 = document.createElement('div');
                            reel2.className = 'vertical-reel';
                            reel2.id = `vertical-constant-reel2-${Date.now()}`;
                            reel2.style.top = '-100%';

                            // Create 10 symbols for each reel
                            for (let i = 0; i < 10; i++) {
                                const symbol1 = document.createElement('div');
                                symbol1.className = 'vertical-symbol';
                                symbol1.style.backgroundImage = `url(${item.dataset.imagePath})`;
                                reel1.appendChild(symbol1);

                                const symbol2 = document.createElement('div');
                                symbol2.className = 'vertical-symbol';
                                symbol2.style.backgroundImage = `url(${item.dataset.imagePath})`;
                                reel2.appendChild(symbol2);
                            }

                            // Add reels to container
                            container.appendChild(reel1);
                            container.appendChild(reel2);

                            // Add container to item
                            item.appendChild(container);

                            // Start constant speed animation
                            animateVerticalConstant(reel1.id, reel2.id);
                        }
                    }
                ];

                // Store animation IDs for cleanup (globally accessible for visibility change handler)
                window.animationIds = window.animationIds || {};

                // Animate horizontal constant speed
                function animateHorizontalConstant(reel1Id, reel2Id) {
                    const reel1 = document.getElementById(reel1Id);
                    const reel2 = document.getElementById(reel2Id);

                    if (!reel1 || !reel2) return;

                    let position1 = 0;
                    let position2 = -100;
                    const speed = 0.5; // Adjust speed as needed

                    function animate() {
                        // Move both reels rightward
                        position1 += speed;
                        position2 += speed;

                        // Reset positions when a reel moves completely out of view
                        if (position1 >= 100) {
                            position1 = -100;
                        }
                        if (position2 >= 100) {
                            position2 = -100;
                        }

                        // Apply positions
                        if (reel1 && reel1.parentNode) reel1.style.left = position1 + '%';
                        if (reel2 && reel2.parentNode) reel2.style.left = position2 + '%';

                        // Continue animation if elements still exist
                        if (reel1 && reel1.parentNode && reel2 && reel2.parentNode) {
                            window.animationIds[reel1Id] = requestAnimationFrame(animate);
                        } else {
                            // Clean up if elements are gone
                            delete window.animationIds[reel1Id];
                        }
                    }

                    // Start animation
                    window.animationIds[reel1Id] = requestAnimationFrame(animate);
                }

                // Animate vertical constant speed
                function animateVerticalConstant(reel1Id, reel2Id) {
                    const reel1 = document.getElementById(reel1Id);
                    const reel2 = document.getElementById(reel2Id);

                    if (!reel1 || !reel2) return;

                    let position1 = 0;
                    let position2 = -100;
                    const speed = 0.5; // Adjust speed as needed

                    function animate() {
                        // Move both reels downward
                        position1 += speed;
                        position2 += speed;

                        // Reset positions when a reel moves completely out of view
                        if (position1 >= 100) {
                            position1 = -100;
                        }
                        if (position2 >= 100) {
                            position2 = -100;
                        }

                        // Apply positions
                        if (reel1 && reel1.parentNode) reel1.style.top = position1 + '%';
                        if (reel2 && reel2.parentNode) reel2.style.top = position2 + '%';

                        // Continue animation if elements still exist
                        if (reel1 && reel1.parentNode && reel2 && reel2.parentNode) {
                            window.animationIds[reel1Id] = requestAnimationFrame(animate);
                        } else {
                            // Clean up if elements are gone
                            delete window.animationIds[reel1Id];
                        }
                    }

                    // Start animation
                    window.animationIds[reel1Id] = requestAnimationFrame(animate);
                }

                // Function to reset item classes
                function resetItemClasses(item) {
                    // Remove all animation classes
                    item.classList.remove(
                        'animate-slot-scroll',
                        'animate-slot-scroll-horizontal',
                        'animate-slot-scroll-vertical',
                        'animate-zoom-pan',
                        'animate-zoom-pan-circular',
                        'animate-zoom-pan-left',
                        'animate-zoom-pan-right',
                        'animate-fade-scale'
                    );

                    // Cancel any running animations
                    const reels = item.querySelectorAll('[id^="horizontal-constant-reel"], [id^="vertical-constant-reel"]');
                    reels.forEach(reel => {
                        if (reel.id && window.animationIds && window.animationIds[reel.id]) {
                            cancelAnimationFrame(window.animationIds[reel.id]);
                            delete window.animationIds[reel.id];
                        }
                    });

                    // Remove any containers
                    const containers = item.querySelectorAll('.scroll-container, .scroll-container-vertical, .constant-container, .fade-container');
                    containers.forEach(container => {
                        container.remove();
                    });

                    // Reset any animation state on the image
                    const img = item.querySelector('img');
                    if (img) {
                        img.style.animation = 'none';
                        img.offsetHeight; // Trigger reflow
                        img.style.animation = '';
                        img.style.opacity = '1'; // Make sure the image is visible
                        img.style.animationPlayState = 'paused';
                    }
                }

                // Function to apply a specific animation to an item
                function applyAnimation(item, animationIndex) {
                    // Get the animation type at the specified index
                    const animationType = animationTypes[animationIndex];

                    // Apply the animation
                    animationType.apply(item);

                    // Set a timeout for the animation duration (4 seconds for all animations)
                    const duration = 4000;

                    // After the animation completes, move to the next animation in sequence
                    setTimeout(() => {
                        // Reset the item
                        resetItemClasses(item);

                        // Calculate the next animation index in the sequence
                        const nextAnimIndex = (animationIndex + 1) % animationTypes.length;

                        // Wait 4 seconds before starting the next animation
                        setTimeout(() => {
                            applyAnimation(item, nextAnimIndex);
                        }, 4000); // 4 second pause between animations
                    }, duration);
                }

                // Start the animation sequence for all gallery items
                galleryItems.forEach((item, index) => {
                    // Calculate a starting point in the animation sequence based on the item's index
                    // This ensures each square starts at a different point in the sequence
                    const startingAnimIndex = (index * 3) % animationTypes.length;

                    // Add a staggered delay based on the item's position
                    const startDelay = index * 200; // 200ms delay between each item's start

                    // Start the animation sequence after the delay
                    setTimeout(() => {
                        applyAnimation(item, startingAnimIndex);
                    }, startDelay);
                });
            }
        } catch (error) {
            console.error('Error loading gallery images:', error);
        }
    }

    // Function to open modal with expand animation
    function openModal(index) {
        // No longer adding selected class to avoid unwanted highlighting
        currentIndex = index;

        // Set the image source but don't animate yet
        modalImage.src = galleryImages[index];
        modalImage.classList.remove('active');

        // Display the modal first without opacity
        modal.style.display = 'flex';

        // Trigger animations after a tiny delay to ensure display:flex is applied
        setTimeout(() => {
            // Fade in the modal background
            modal.classList.add('active');

            // After background starts fading in, expand the image
            setTimeout(() => {
                modalImage.classList.add('active');
            }, 100);
        }, 10);

        document.body.style.overflow = 'hidden'; // Prevent scrolling
    }

    // Function to close modal with animation
    function closeModal() {
        // First shrink the image
        modalImage.classList.remove('active');

        // Then fade out the modal background
        setTimeout(() => {
            modal.classList.remove('active');

            // After fade out completes, hide the modal
            setTimeout(() => {
                modal.style.display = 'none';
                document.body.style.overflow = ''; // Re-enable scrolling
            }, 300); // Match the transition duration
        }, 100);

        // No longer using selected class
    }

    // Function to navigate to previous image with animation
    function prevImage() {
        // First fade out the current image
        modalImage.classList.remove('active');

        setTimeout(() => {
            // Update current index
            currentIndex = (currentIndex - 1 + galleryImages.length) % galleryImages.length;

            // Update the image source
            modalImage.src = galleryImages[currentIndex];

            // After a tiny delay to ensure the new image is loaded, fade it in
            setTimeout(() => {
                modalImage.classList.add('active');
            }, 50);

            // Scroll the item into view if needed
            const selectedItem = document.querySelector(`.gallery-item[data-index="${currentIndex}"]`);
            if (selectedItem && !isElementInViewport(selectedItem)) {
                selectedItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }, 200); // Short delay to allow fade out
    }

    // Function to navigate to next image with animation
    function nextImage() {
        // First fade out the current image
        modalImage.classList.remove('active');

        setTimeout(() => {
            // Update current index
            currentIndex = (currentIndex + 1) % galleryImages.length;

            // Update the image source
            modalImage.src = galleryImages[currentIndex];

            // After a tiny delay to ensure the new image is loaded, fade it in
            setTimeout(() => {
                modalImage.classList.add('active');
            }, 50);

            // Scroll the item into view if needed
            const selectedItem = document.querySelector(`.gallery-item[data-index="${currentIndex}"]`);
            if (selectedItem && !isElementInViewport(selectedItem)) {
                selectedItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }, 200); // Short delay to allow fade out
    }

    // Helper function to check if an element is in the viewport
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
    
    // Make startGalleryAnimations available globally if not already defined
    if (!window.startGalleryAnimations) {
        window.startGalleryAnimations = function() {
            // This will be defined by the rest of the gallery code
        };
    }
});

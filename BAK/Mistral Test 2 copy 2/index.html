<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">

    <!-- SEO Meta Tags -->
    <title>Artchop!</title>
    <meta name="description" content="Artchop is a scalable, full-stack creative studio for games. We provide high-fidelity game art, animation, code, and content for developers, publishers, and platforms.">
    <meta name="keywords" content="game art outsourcing, game art studio, external development for games, character modeling, animation services for games, UI/UX for games, 3D game art, digital doubles, scalable creative for games">
    <meta name="author" content="Artchop">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://www.artchop.net">

    <!-- Mobile Meta Tags -->
    <meta name="theme-color" content="#8781bd">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">

    <!-- Favicon -->
    <link rel="icon" href="images/flavi.png" type="image/png">
    <link rel="apple-touch-icon" href="images/flavi.png">

    <!-- Social Media Preview -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Artchop!">
    <meta property="og:description" content="Scalable creative strike force for games—trusted by top studios for characters, worlds, pipelines, and content built to ship.">
    <meta property="og:image" content="images/artchoop-social.png">
    <meta property="og:url" content="https://www.artchop.net">
    <meta property="og:site_name" content="Artchop Studio">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Artchop!">
    <meta name="twitter:description" content="Scalable creative strike force for games—trusted by top studios for characters, worlds, pipelines, and content built to ship.">
    <meta name="twitter:image" content="images/artchoop-social.png">
    <link rel="stylesheet" href="fixed-style.css">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="gallery.css">
    <link rel="stylesheet" href="about-widgets.css">
    <link rel="stylesheet" href="action-buttons.css">
    <link rel="stylesheet" href="contact.css">
    <!-- Google Fonts with display=swap for better performance -->
    <link href="https://fonts.googleapis.com/css2?family=Hanken+Grotesk:wght@400;500;700&family=Syncopate:wght@400;700&display=swap" rel="stylesheet">

    <!-- Preload critical fonts for better performance -->
    <link rel="preload" href="https://fonts.gstatic.com/s/hankengrotesk/v8/ieVq2YZDLWuGJpnzaiwFXS9tYtpY_Q.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://fonts.gstatic.com/s/syncopate/v19/pe0sMIuPIYBCpEV5eFdCBfe5.woff2" as="font" type="font/woff2" crossorigin>
    <!-- GSAP and ScrollTrigger CDN links -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollToPlugin.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
    <!-- Animation scripts -->
    <script src="script.js" defer></script>
    <script src="gallery.js" defer></script>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-container">
            <a href="#hero" class="site-logo">
                <div class="logo-container">
                    <img src="images/artchop-logo-1.png" class="logo-image logo-1">
                    <img src="images/artchop-logo-2.png" class="logo-image logo-2">
                </div>
            </a>
            <button class="hamburger-icon" aria-label="Toggle navigation menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="nav-links" id="mobile-menu">
                <a href="#about">ABOUT</a>
                <a href="#about-2">GALLERY</a>
                <a href="#contact">CONTACT</a>
            </div>
        </div>
    </nav>
    <div class="sticky-navbar-spacer"></div>

    <!-- Hero Section -->
    <section id="hero" class="hero-section section">
        <div class="hero-overlay"></div>
        <div class="hero-carousel">
            <div class="hero-slide active">
                <img src="images/Hero1.png" alt="Hero 1" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero2.png" alt="Hero 2" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero3.png" alt="Hero 3" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero4.png" alt="Hero 4" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero5.png" alt="Hero 5" class="hero-background-image">
            </div>
        </div>
        <div class="hero-content-container">
            <div class="hero-content">
                <h1>IDEAL CREATIVE PARTNERS FOR GAMES</h1>
                <div class="subheadline">
                    <p>FULL-STACK CREATIVE</p>
                    <p>CHARACTERS</p>
                    <p>WORLDS</p>
                    <p>PIPELINES</p>
                </div>
                <a href="#about" class="cta-button">GET TO KNOW US</a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section section fullscreen-section">
        <div class="about-content">
            <h2>WHO WE ARE</h2>

            <div class="widget-grid">
                <!-- ARTCHOP! Widget - FIRST -->
                <div class="widget mission-widget dominant">
                    <div class="widget-header">
                        <div class="widget-icon">🎯</div>
                        <div class="widget-title">ARTCHOP!</div>
                    </div>
                    <div class="widget-content">
                        <p><span style="font-weight: 700;">WE'RE A MODERN CREATIVE STUDIO BUILT FOR COLLABORATION.</span></p>
                        <br>
                        <p>Backed by deep development experience, creative instincts, and strong technical know-how, Artchop is structured to reduce friction and de-risk outsourcing. Our US-based front office handles communication and coordination, while our global senior team plugs in fast and gets to work.</p>
                        <br>
                        <p>For our clients, we fill gaps, support overflow, or take ownership of entire initiatives. We integrate quickly, align easily, and deliver consistently. We've contributed to award-winning games, long-term partnerships, and high-stakes projects that needed reliable hands when it mattered most.</p>
                    </div>
                </div>

                <!-- Team Breakdown Pie Chart -->
                <div class="widget projects-widget large">
                    <div class="widget-header">
                        <div class="widget-icon">📊</div>
                        <div class="widget-title">Team Breakdown</div>
                    </div>
                    <div class="widget-content">
                        <div class="team-breakdown-layout">
                            <div class="pie-legend">
                                <div class="legend-item"><span class="legend-color" style="background: #e74c3c;"></span>3D MODELERS (10)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #f39c12;"></span>PROGRAMMERS (8)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #2ecc71;"></span>DD SPECIALISTS (6)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #3498db;"></span>CONCEPT ARTISTS (5)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #9b59b6;"></span>ANIMATORS (3)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #f1c40f;"></span>ART MANAGERS (3)</div>
                                <div class="legend-item"><span class="legend-color" style="background: #ff69b4;"></span>TECHNICAL ARTISTS (2)</div>
                            </div>
                            <div class="pie-chart">
                                <svg viewBox="0 0 100 100" class="pie-svg">
                                    <!-- Background circle -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="20"/>

                                    <!-- 3D Modelers: 10/37 = 27.0% of 251.2 circumference = 67.8 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#e74c3c" stroke-width="20"
                                            stroke-dashoffset="62.8" transform="rotate(-90 50 50)" style="--final-dash-array: 67.8 183.4"/>
                                    <!-- Programmers: 8/37 = 21.6% = 54.3 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#f39c12" stroke-width="20"
                                            stroke-dashoffset="-5.0" transform="rotate(-90 50 50)" style="--final-dash-array: 54.3 196.9"/>
                                    <!-- DD Specialists: 6/37 = 16.2% = 40.7 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#2ecc71" stroke-width="20"
                                            stroke-dashoffset="-59.3" transform="rotate(-90 50 50)" style="--final-dash-array: 40.7 210.5"/>
                                    <!-- Concept Artists: 5/37 = 13.5% = 33.9 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#3498db" stroke-width="20"
                                            stroke-dashoffset="-100.0" transform="rotate(-90 50 50)" style="--final-dash-array: 33.9 217.3"/>
                                    <!-- Animators: 3/37 = 8.1% = 20.3 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#9b59b6" stroke-width="20"
                                            stroke-dashoffset="-133.9" transform="rotate(-90 50 50)" style="--final-dash-array: 20.3 230.9"/>
                                    <!-- Art Managers: 3/37 = 8.1% = 20.3 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#f1c40f" stroke-width="20"
                                            stroke-dashoffset="-154.2" transform="rotate(-90 50 50)" style="--final-dash-array: 20.3 230.9"/>
                                    <!-- Technical Artists: 2/37 = 5.4% = 13.6 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#ff69b4" stroke-width="20"
                                            stroke-dashoffset="-174.5" transform="rotate(-90 50 50)" style="--final-dash-array: 13.6 237.6"/>
                                </svg>
                                <div class="pie-center">37</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Technologies Widget -->
                <div class="widget vision-widget tall">
                    <div class="widget-header">
                        <div class="widget-icon">💻</div>
                        <div class="widget-title">Technologies</div>
                    </div>
                    <div class="widget-content">
                        <div class="tech-logos">
                            <div class="tech-item">
                                <div class="tech-logo unity-logo">U</div>
                                <div class="tech-name">UNITY</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo unreal-logo">UE</div>
                                <div class="tech-name">UNREAL</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo shader-logo">SG</div>
                                <div class="tech-name">SHADER GRAPH</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo substance-painter-logo">SP</div>
                                <div class="tech-name">SUBSTANCE 3D PAINTER</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo substance-designer-logo">SD</div>
                                <div class="tech-name">SUBSTANCE 3D DESIGNER</div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-logo marvelous-logo">MD</div>
                                <div class="tech-name">MARVELOUS DESIGNER</div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- Expertise Widget (Wide) -->
                <div class="widget skills-widget wide">
                    <div class="widget-header">
                        <div class="widget-icon">⚡</div>
                        <div class="widget-title">Expertise</div>
                    </div>
                    <div class="widget-content">
                        <div class="expertise-grid">
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">CONCEPT, KEY & PRODUCTION ART</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">HARD SURFACE MODELS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">MODULAR GAME ASSETS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">CHARACTER MODELS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">TEXTURE & MATERIALS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">HD DIGITAL DOUBLES</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">ENVIRONMENTS</div>
                            </div>
                            <div class="expertise-item">
                                <div class="expertise-checkbox">✓</div>
                                <div class="expertise-text">RIGGING</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vibe Check Widget -->
                <div class="widget vibe-widget">
                    <div class="widget-header">
                        <div class="widget-icon">✨</div>
                        <div class="widget-title">Vibe Check</div>
                    </div>
                    <div class="widget-content">
                        <div class="vibe-simple">
                            <div class="widget-label">CURRENT STATUS</div>
                            <div class="vibe-status-row">
                                <div class="status-options">
                                    <span class="status-option">OFF</span>
                                    <span class="status-option active">ON</span>
                                </div>
                                <div class="status-indicator-large on"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Aura Meter Widget -->
                <div class="widget aura-widget">
                    <div class="widget-header">
                        <div class="widget-icon">🌟</div>
                        <div class="widget-title">Aura Meter</div>
                    </div>
                    <div class="widget-content">
                        <div class="aura-meter">
                            <div class="aura-bar">
                                <div class="aura-fill"></div>
                            </div>
                            <div class="aura-percentage">100%</div>
                        </div>
                        <div class="widget-label" style="text-align: center;">MAXIMUM AURA</div>
                    </div>
                </div>

                <!-- World Map Widget -->
                <div class="widget map-widget wide">
                    <div class="widget-header">
                        <div class="widget-icon">🗺️</div>
                        <div class="widget-title">Locations</div>
                    </div>
                    <div class="widget-content">
                        <div class="world-map">
                            <!-- Time Zone Clocks -->
                            <div class="timezone-clocks">
                                <div class="clock-item">
                                    <div class="clock-face" id="sf-clock">
                                        <div class="clock-ticks">
                                            <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                            <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                        </div>
                                        <div class="clock-hand hour-hand" id="sf-hour"></div>
                                        <div class="clock-hand minute-hand" id="sf-minute"></div>
                                    </div>
                                    <div class="clock-label">
                                        <div class="city-name">SAN FRANCISCO</div>
                                        <div class="time-display" id="sf-time">--:--</div>
                                    </div>
                                </div>

                                <div class="clock-item">
                                    <div class="clock-face" id="saitama-clock">
                                        <div class="clock-ticks">
                                            <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                            <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                        </div>
                                        <div class="clock-hand hour-hand" id="saitama-hour"></div>
                                        <div class="clock-hand minute-hand" id="saitama-minute"></div>
                                    </div>
                                    <div class="clock-label">
                                        <div class="city-name">SAITAMA</div>
                                        <div class="time-display" id="saitama-time">--:--</div>
                                    </div>
                                </div>

                                <div class="clock-item">
                                    <div class="clock-face" id="manila-clock">
                                        <div class="clock-ticks">
                                            <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                            <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                        </div>
                                        <div class="clock-hand hour-hand" id="manila-hour"></div>
                                        <div class="clock-hand minute-hand" id="manila-minute"></div>
                                    </div>
                                    <div class="clock-label">
                                        <div class="city-name">MANILA</div>
                                        <div class="time-display" id="manila-time">--:--</div>
                                    </div>
                                </div>

                                <div class="clock-item">
                                    <div class="clock-face" id="novi-sad-clock">
                                        <div class="clock-ticks">
                                            <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                            <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                            <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                        </div>
                                        <div class="clock-hand hour-hand" id="novi-sad-hour"></div>
                                        <div class="clock-hand minute-hand" id="novi-sad-minute"></div>
                                    </div>
                                    <div class="clock-label">
                                        <div class="city-name">NOVI SAD</div>
                                        <div class="time-display" id="novi-sad-time">--:--</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons Widget -->
                <div class="widget action-widget wide">
                    <div class="widget-header">
                        <div class="widget-icon">🚀</div>
                        <div class="widget-title">Take Action</div>
                    </div>
                    <div class="widget-content">
                        <div class="action-buttons">
                            <button class="action-btn action-pink" onclick="const targetId1 = 'about-2'; const target1 = document.getElementById(targetId1); if (target1) { resetAndReanimateSection('#' + targetId1); const targetPosition1 = target1.getBoundingClientRect().top + window.scrollY; window.scrollTo({ top: targetPosition1, behavior: 'smooth' }); }">
                                VIEW OUR WORK
                            </button>
                            <button class="action-btn action-blue" onclick="const target2 = document.getElementById('contact'); if (target2) { resetAndReanimateSection('#' + target2.id); const targetPosition2 = target2.getBoundingClientRect().top + window.scrollY; window.scrollTo({ top: targetPosition2, behavior: 'smooth' }); }">
                                REACH OUT TO US
                            </button>
                        </div>
                    </div>
                </div>
        </div>
    </section>

    <!-- Our Work Section -->
    <section id="about-2" class="about-section section fullscreen-section" style="background-color: #000000;">
        <div class="about-content">
            <h2>OUR WORK</h2>
            <div class="widget-grid">
                <!-- Widgets will go here -->
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section section fullscreen-section">
        <div class="about-content">
            <h2>LET'S GET TO WORK!</h2>
            <div class="widget-grid">
                <!-- Contact Form Widget -->
                <div class="widget contact-widget wide">
                    <div class="contact-form-container">
                        <p class="contact-form-subheadline">Let's talk about how we can support your next project and deliver tangible results.</p>
                        
                        <form id="contact-form" class="contact-form" action="https://formsubmit.co/<EMAIL>" method="POST">
                            <!-- Prevent spam -->
                            <input type="text" name="_honey" style="display:none">
                            <!-- Disable Captcha -->
                            <input type="hidden" name="_captcha" value="false">
                            <!-- Disable auto response -->
                            <input type="hidden" name="_autoresponse" value="">
                            <!-- Template -->
                            <input type="hidden" name="_template" value="table">
                            <div class="form-group">
                                <input type="text" id="name" name="name" required>
                                <label for="name">Your Name</label>
                            </div>
                            <div class="form-group">
                                <input type="email" id="email" name="email" required>
                                <label for="email">Your Email</label>
                            </div>
                            <div class="form-group">
                                <textarea id="message" name="message" rows="4" required></textarea>
                                <label for="message">Your Message</label>
                            </div>
                            <div class="form-group terms-group">
                                <div style="display: flex; align-items: center; flex-wrap: wrap; gap: 4px;">
                                    <input type="checkbox" id="terms" name="terms" required>
                                    <label for="terms" style="margin: 0; padding: 0; display: inline; position: static; transform: none; pointer-events: auto;">
                                        By continuing you agree to our
                                    </label>
                                    <a href="#" onclick="showModal('termsModal'); return false;" style="color: #FF42A1; font-weight: bold; text-decoration: none; margin: 0 4px;">
                                        Terms of Service
                                    </a>
                                    <span style="color: #FF42A1;">and</span>
                                    <a href="#" onclick="showModal('privacyModal'); return false;" style="color: #FF42A1; font-weight: bold; text-decoration: none; margin: 0 4px;">
                                        Privacy Policy
                                    </a>
                                </div>
                            </div>
                            <button type="submit" class="cta-button">Send Message</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Artchop Studio",
        "url": "https://www.artchop.net",
        "logo": "https://www.artchop.net/images/flavi.png",
        "description": "Artchop is a scalable, full-stack creative studio for games. We provide high-fidelity game art, animation, code, and content for developers, publishers, and platforms.",
        "slogan": "Scalable creative strike force for games"
    }
    </script>

    <!-- Main JavaScript -->
    <script src="gallery.js"></script>
    <script>
        // Initialize gallery when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof initGallery === 'function') {
                initGallery();
            }
        });
    </script>
    <!-- Terms of Service Modal -->
    <div id="termsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Artchop.net Terms of Service</h2>
                <div style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
                    <a href="#" onclick="hideModal('termsModal'); return false;" style="font-size: 32px; color: #333; text-decoration: none; display: block; width: 40px; height: 40px; text-align: center; line-height: 40px;">&times;</a>
                </div>
            </div>
            <div class="modal-body">
                <h3>1. Introduction</h3>
                <p>Welcome to Artchop. By accessing this website or using our services, you agree to be bound by these Terms of Service. If you do not agree with any part of these terms, please do not use the site.</p>

                <h3>2. Use of Services</h3>
                <p>Artchop provides creative services for game development and related industries. All services are provided "as is." We are not responsible for delays, data loss, or communication errors, and we make no guarantees regarding uptime or availability.</p>

                <h3>3. User Conduct</h3>
                <p>You are responsible for your use of the site and any communications submitted. Fraudulent, abusive, or unlawful behavior may result in termination of access.</p>

                <h3>4. Intellectual Property</h3>
                <p>All content on this site—including text, graphics, logos, images, and layouts—is the property of Artchop or its licensors and is protected by intellectual property laws. You may not copy, reproduce, or distribute any content without permission.</p>

                <h3>5. Disclaimer of Warranties</h3>
                <p>We provide this website and our services "as is" without warranties of any kind, express or implied, including but not limited to merchantability or fitness for a particular purpose.</p>

                <h3>6. Limitation of Liability</h3>
                <p>Artchop is not liable for any indirect, incidental, special, consequential, or punitive damages arising from your use of this site or services, even if we've been advised of the possibility of such damages.</p>

                <h3>7. Changes to These Terms</h3>
                <p>We may update these Terms at any time. Continued use of the site after changes are posted constitutes your acceptance of those changes.</p>

                <h3>8. Governing Law</h3>
                <p>These Terms are governed by the laws of the State of California, USA, without regard to its conflict of law rules.</p>

                <p class="last-updated">Last Updated: November 8, 2023</p>
            </div>
        </div>
    </div>

    <!-- Privacy Policy Modal -->
    <div id="privacyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Artchop.net Privacy Policy</h2>
                <div style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
                    <a href="#" onclick="hideModal('privacyModal'); return false;" style="font-size: 32px; color: #333; text-decoration: none; display: block; width: 40px; height: 40px; text-align: center; line-height: 40px;">&times;</a>
                </div>
            </div>
            <div class="modal-body">
                <h3>1. Introduction</h3>
                <p>At Artchop, we take your privacy seriously. This Privacy Policy explains what personal information we collect, how we use it, and the steps we take to protect it.</p>

                <h3>2. Information We Collect</h3>
                <p>We may collect personal information when you fill out a form, contact us, subscribe to our newsletter, or communicate with us directly. This may include your name, email address, and any other details you choose to provide.</p>

                <h3>3. How We Use Your Information</h3>
                <p>We use the information we collect to:</p>
                <ul>
                    <li>Respond to inquiries or requests</li>
                    <li>Improve our website and services</li>
                    <li>Send occasional updates or newsletters (only if you've opted in)</li>
                    <li>Maintain internal records</li>
                </ul>

                <h3>4. How We Protect Your Information</h3>
                <p>We implement industry-standard security measures to protect your information from unauthorized access, alteration, or disclosure.</p>

                <h3>5. Cookies</h3>
                <p>Our website uses cookies to enhance user experience and collect limited analytics data. You can choose to disable cookies in your browser settings.</p>

                <h3>6. Information Sharing</h3>
                <p>We do not sell, trade, or share your personal information with outside parties, except when required by law or to provide services you've requested.</p>

                <h3>7. Third-Party Links</h3>
                <p>This website may contain links to third-party services. These sites have separate and independent privacy policies, and we are not responsible for their content or practices.</p>

                <h3>8. Your Consent</h3>
                <p>By using our site, you consent to the terms of this Privacy Policy.</p>

                <h3>9. Changes to This Policy</h3>
                <p>We may update this Privacy Policy occasionally. Changes will be reflected on this page with a revised modification date.</p>

                <p class="last-updated">Last Updated: November 8, 2023</p>

                <h3>Contact Us</h3>
                <p>If you have questions about this policy, please contact us at:<br>
                <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
        </div>
    </div>
    
    <script>
        // Show modal function
        function showModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden'; // Prevent scrolling
            }
        }


        // Hide modal function
        function hideModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = 'auto'; // Re-enable scrolling
            }
            return false;
        }


        // Close modal when clicking outside content
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.classList.remove('active');
                document.body.style.overflow = 'auto'; // Re-enable scrolling
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const activeModal = document.querySelector('.modal.active');
                if (activeModal) {
                    hideModal(activeModal.id);
                }
            }
        });

        // Contact Form Animation and Functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Animate contact form when in view
            function resetAnimations() {
                const contactWidget = document.querySelector('.contact-widget');
                const formContainer = document.querySelector('.contact-form-container');
                const contactForm = document.querySelector('.contact-form');
                const subheadline = document.querySelector('.contact-form-subheadline');
                
                if (contactWidget) contactWidget.style.opacity = '0';
                if (formContainer) formContainer.style.opacity = '0';
                if (contactForm) contactForm.style.opacity = '0';
                if (subheadline) {
                    subheadline.style.opacity = '0';
                    subheadline.style.transform = 'scale(0.98)';
                    subheadline.style.animation = 'none';
                    // Force reflow
                    void subheadline.offsetHeight;
                }
            }
            
            function animateContactForm() {
                const contactSection = document.querySelector('.contact-section');
                const contactWidget = document.querySelector('.contact-widget');
                const formContainer = document.querySelector('.contact-form-container');
                const contactForm = document.querySelector('.contact-form');
                const subheadline = document.querySelector('.contact-form-subheadline');
                
                if (!contactSection || !contactWidget || !formContainer || !contactForm || !subheadline) return;
                
                // Reset animations when section comes into view
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        // Only animate when at least 10% of the section is visible
                        if (entry.isIntersecting && entry.intersectionRatio >= 0.1) {
                            // Reset animations first
                            resetAnimations();
                            
                            // Force reflow to ensure reset is applied
                            void contactSection.offsetHeight;
                            
                            // Animate the widget container (fade in)
                            requestAnimationFrame(() => {
                                contactWidget.style.transition = 'opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                                contactWidget.style.opacity = '1';
                                
                                // Animate form container (scale up and fade in)
                                setTimeout(() => {
                                    formContainer.style.transition = 'opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                                    formContainer.style.opacity = '1';
                                    formContainer.style.transform = 'scale(1)';
                                    
                                    // Animate form (slide up and fade in)
                                    setTimeout(() => {
                                        contactForm.style.transition = 'opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1), transform 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                                        contactForm.style.opacity = '1';
                                        contactForm.style.transform = 'translateY(0)';
                                        
                                        // Animate subheadline (fade in and pulse)
                                        setTimeout(() => {
                                            subheadline.style.transition = 'opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1), transform 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                                            subheadline.style.opacity = '1';
                                            subheadline.style.transform = 'scale(1)';
                                            // Start the pulsing animation after the initial scale
                                            setTimeout(() => {
                                                subheadline.style.animation = 'pulseScale 3s infinite ease-in-out';
                                            }, 600);
                                        }, 150);
                                    }, 150);
                                }, 100);
                            });
                            
                            // Keep observing for when it goes out of view
                            // Don't unobserve so animations trigger every time
                        } else {
                            // When section goes out of view, reset animations
                            resetAnimations();
                        }
                    });
                }, { 
                    threshold: [0, 0.1, 0.5, 1], // Multiple thresholds for better detection
                    rootMargin: '0px 0px -20% 0px' // Trigger when 20% of section is from bottom of viewport
                });
                
                // Initial reset
                resetAnimations();
                
                // Start observing
                observer.observe(contactSection);
                
                // Also observe when section leaves the viewport
                return observer;
            }
            
            // Initialize contact form animation
            animateContactForm();
            
            // Success Modal
            const modal = document.createElement('div');
            modal.className = 'success-modal';
            modal.style.display = 'none';
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.backgroundColor = 'rgba(0,0,0,0.8)';
            modal.style.zIndex = '9999';
            modal.style.display = 'flex';
            modal.style.justifyContent = 'center';
            modal.style.alignItems = 'center';
            modal.style.opacity = '0';
            modal.style.transition = 'opacity 0.3s ease-in-out';
            
            const modalContent = document.createElement('div');
            modalContent.style.backgroundColor = '#fff';
            modalContent.style.padding = '40px';
            modalContent.style.borderRadius = '8px';
            modalContent.style.textAlign = 'center';
            modalContent.style.maxWidth = '500px';
            modalContent.style.width = '90%';
            modalContent.style.position = 'relative';
            
            const closeBtn = document.createElement('button');
            closeBtn.textContent = '✕';
            closeBtn.style.position = 'absolute';
            closeBtn.style.top = '10px';
            closeBtn.style.right = '10px';
            closeBtn.style.background = 'none';
            closeBtn.style.border = 'none';
            closeBtn.style.fontSize = '24px';
            closeBtn.style.cursor = 'pointer';
            closeBtn.style.padding = '0 10px';
            closeBtn.style.color = '#666';
            
            const successIcon = document.createElement('div');
            successIcon.innerHTML = '✓';
            successIcon.style.fontSize = '60px';
            successIcon.style.color = '#4CAF50';
            successIcon.style.marginBottom = '20px';
            
            const successTitle = document.createElement('h2');
            successTitle.textContent = 'Message Sent!';
            successTitle.style.marginBottom = '15px';
            successTitle.style.color = '#333';
            
            const successText = document.createElement('p');
            successText.textContent = 'Thank you for reaching out. We\'ll get back to you soon!';
            successText.style.color = '#666';
            successText.style.lineHeight = '1.6';
            
            modalContent.appendChild(closeBtn);
            modalContent.appendChild(successIcon);
            modalContent.appendChild(successTitle);
            modalContent.appendChild(successText);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);
            
            // Close modal function
            function closeModal() {
                modal.style.opacity = '0';
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 300);
            }
            
            closeBtn.addEventListener('click', closeModal);
            modal.addEventListener('click', (e) => {
                if (e.target === modal) closeModal();
            });
            
            // Form validation function
            function validateForm(form) {
                let isValid = true;
                let firstError = null;
                
                // Reset all error states
                const errorElements = form.querySelectorAll('.error-message');
                errorElements.forEach(el => el.remove());
                
                const formGroups = form.querySelectorAll('.form-group');
                formGroups.forEach(group => group.classList.remove('error'));
                
                // Validate name
                const nameInput = form.querySelector('input[name="name"]');
                if (!nameInput.value.trim()) {
                    showError(nameInput, 'Please enter your name');
                    if (!firstError) firstError = nameInput;
                    isValid = false;
                }
                
                // Validate email
                const emailInput = form.querySelector('input[name="email"]');
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailInput.value.trim()) {
                    showError(emailInput, 'Please enter your email');
                    if (!firstError) firstError = emailInput;
                    isValid = false;
                } else if (!emailRegex.test(emailInput.value.trim())) {
                    showError(emailInput, 'Please enter a valid email address');
                    if (!firstError) firstError = emailInput;
                    isValid = false;
                }
                
                // Validate message
                const messageInput = form.querySelector('textarea[name="message"]');
                if (!messageInput.value.trim()) {
                    showError(messageInput, 'Please enter your message');
                    if (!firstError) firstError = messageInput;
                    isValid = false;
                }
                
                // Validate terms checkbox
                const termsCheckbox = form.querySelector('input[name="terms"]');
                const termsGroup = termsCheckbox.closest('.form-group');
                if (!termsCheckbox.checked) {
                    const error = document.createElement('div');
                    error.className = 'error-message';
                    error.textContent = 'You must accept the terms';
                    termsGroup.appendChild(error);
                    termsGroup.classList.add('error');
                    if (!firstError) firstError = termsGroup;
                    isValid = false;
                }
                
                // Scroll to first error if any
                if (!isValid && firstError) {
                    // Small delay to ensure the error messages are rendered
                    setTimeout(() => {
                        firstError.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });
                    }, 50);
                }
                
                return isValid;
            }
            
            // Show error message for a field
            function showError(input, message) {
                const formGroup = input.closest('.form-group');
                const error = document.createElement('div');
                error.className = 'error-message';
                error.textContent = message;
                formGroup.appendChild(error);
                formGroup.classList.add('error');
            }
            
            // Handle form submission
            const contactForm = document.getElementById('contact-form');
            if (contactForm) {
                contactForm.addEventListener('submit', async function(e) {
                    // Prevent default form submission
                    e.preventDefault();
                    
                    // Reset any previous error states
                    const errorMessages = contactForm.querySelectorAll('.error-message');
                    errorMessages.forEach(el => el.remove());
                    
                    // Clear any previous error classes
                    const formGroups = contactForm.querySelectorAll('.form-group');
                    formGroups.forEach(group => group.classList.remove('error'));
                    
                    // Validate form
                    if (!validateForm(contactForm)) {
                        return false; // Stop if validation fails
                    }
                    
                    const formData = new FormData(contactForm);
                    const submitBtn = contactForm.querySelector('button[type="submit"]');
                    const originalBtnText = submitBtn.textContent;
                    
                    try {
                        // Show loading state
                        submitBtn.disabled = true;
                        submitBtn.textContent = 'Sending...';
                        
                        // Submit form data
                        const response = await fetch(contactForm.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'Accept': 'application/json'
                            }
                        });

                        if (response.ok) {
                            // Show success modal
                            modal.style.display = 'flex';
                            setTimeout(() => {
                                modal.style.opacity = '1';
                            }, 10);
                            
                            // Reset form
                            contactForm.reset();
                        } else {
                            throw new Error('Network response was not ok');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        alert('There was a problem sending your message. Please try again later.');
                    } finally {
                        // Reset button state
                        submitBtn.textContent = originalBtnText;
                        submitBtn.disabled = false;
                    }
                });
            }
        });
    </script>

</body>
</html>

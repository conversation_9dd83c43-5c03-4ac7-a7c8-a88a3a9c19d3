/* Gallery Styles */

/* Gallery header */
.gallery-header {
    text-align: left; /* Change to left-aligned */
    margin-bottom: 30px;
    width: 100%;
    max-width: 800px;
}

.gallery-header h2 {
    margin-bottom: 20px;
    text-align: left; /* Ensure heading is left-aligned */
}

.gallery-header p {
    margin-bottom: 0;
    text-align: left; /* Ensure paragraph is left-aligned */
}

/* Gallery container */
.gallery-container {
    max-width: 1200px;
    width: 100%;
    margin: 20px auto;
    padding: 0;
}

/* Gallery container is responsive by default */

/* Gallery grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    width: 100%;
    gap: 2px;
    background-color: #000; /* Change to black */
}

/* Gallery item */
.gallery-item {
    position: relative;
    overflow: hidden;
    aspect-ratio: 16/9; /* Rectangular aspect ratio (16:9) */
    cursor: pointer;
    border: 1px solid #000; /* Change to black */
    background-color: #000; /* Add black background */
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    opacity: 0;
    transition: opacity 0.2s ease-in;
    transform-origin: center;
}

.gallery-item.revealed img {
    opacity: 1;
}

/* Ensure the animation stays within the container */
.gallery-item {
    overflow: hidden;
}

/* Hover effect - magnifying glass */
.gallery-item .magnify-icon {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 32px;
    height: 32px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/><path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 4;
    pointer-events: none;
}

/* Hover overlay */
.gallery-item .hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #8781bd;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
}

.gallery-item:hover .hover-overlay {
    opacity: 0.5;
}

.gallery-item:hover .magnify-icon {
    opacity: 1;
}

/* Selection overlay */
.gallery-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    border: 3px solid #8781bd; /* Change to purple instead of white */
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
    pointer-events: none;
}

.gallery-item.selected::after {
    opacity: 1;
}

/* Modal styles */
.gallery-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw; /* Use viewport width */
    height: 100vh; /* Use viewport height */
    background-color: rgba(0, 0, 0, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999; /* Higher z-index to ensure it's above everything */
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.gallery-modal.active {
    opacity: 1;
}

.modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-image {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    transform: scale(0.8);
    opacity: 0;
    transition: transform 0.4s ease-out, opacity 0.4s ease-out;
}

.modal-image.active {
    transform: scale(1);
    opacity: 1;
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1001;
}

.modal-close::before,
.modal-close::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background-color: white;
}

.modal-close::before {
    transform: rotate(45deg);
}

.modal-close::after {
    transform: rotate(-45deg);
}

.modal-nav {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    pointer-events: none;
}

.modal-prev,
.modal-next {
    width: 50px;
    height: 50px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    pointer-events: auto;
}

.modal-prev::before,
.modal-next::before {
    content: '';
    width: 10px;
    height: 10px;
    border-style: solid;
    border-color: white;
    border-width: 0 0 2px 2px;
    display: inline-block;
}

.modal-prev::before {
    transform: rotate(45deg);
    margin-left: 5px;
}

.modal-next::before {
    transform: rotate(-135deg);
    margin-right: 5px;
}

/* Animation styles */
/* Base class for zoom-pan animations */
.animate-zoom-pan {
    position: relative;
    overflow: hidden;
}

/* Animation for circular pan */
.animate-zoom-pan-circular img {
    animation: zoomPanCircular 5.5s ease-in-out;
    transform-origin: center;
    will-change: transform;
}

/* Animation for left-side pan */
.animate-zoom-pan-left img {
    animation: zoomPanLeft 5.5s ease-in-out;
    transform-origin: center;
    will-change: transform;
}

/* Animation for right-side pan */
.animate-zoom-pan-right img {
    animation: zoomPanRight 5.5s ease-in-out;
    transform-origin: center;
    will-change: transform;
}

/* Fade-in scaled animations */
.animate-fade-scale {
    position: relative;
    overflow: hidden;
}

/* Fade-in scaled animation container */
.animate-fade-scale .fade-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}

/* Fade-in scaled image */
.animate-fade-scale .fade-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0;
    transform: scale(1.5);
    transition: opacity 1s ease-in-out, transform 1.5s ease-in-out;
}

/* Active state for fade-in scaled image */
.animate-fade-scale .fade-image.active {
    opacity: 1;
    transform: scale(1);
}

/* Original circular pan animation */
@keyframes zoomPanCircular {
    /* Initial state */
    0% { transform: scale(1) translate(0, 0); }

    /* Zoom up quickly (0.25s) */
    3.23% { transform: scale(3) translate(0, 0); }

    /* Pan in a circle using sine/cosine pattern (5s) */
    /* We use a mathematical pattern with just a few keyframes */
    19.35% { transform: scale(3) translate(5%, 0); }     /* Right */
    35.48% { transform: scale(3) translate(0, 5%); }     /* Bottom */
    51.61% { transform: scale(3) translate(-5%, 0); }    /* Left */
    67.74% { transform: scale(3) translate(0, -5%); }    /* Top */

    /* Zoom back down (0.5s) */
    74.19% { transform: scale(1) translate(0, 0); }

    /* Rest before repeating */
    100% { transform: scale(1) translate(0, 0); }
}

/* Left-side pan animation */
@keyframes zoomPanLeft {
    /* Initial state */
    0% { transform: scale(1) translate(0, 0); }

    /* Zoom up quickly (0.25s) */
    3.23% { transform: scale(3) translate(15%, 0); }

    /* Pan across left side (5s) */
    19.35% { transform: scale(3) translate(15%, 10%); }
    35.48% { transform: scale(3) translate(15%, 0); }
    51.61% { transform: scale(3) translate(15%, -10%); }
    67.74% { transform: scale(3) translate(15%, 0); }

    /* Zoom back down (0.5s) */
    74.19% { transform: scale(1) translate(0, 0); }

    /* Rest before repeating */
    100% { transform: scale(1) translate(0, 0); }
}

/* Right-side pan animation */
@keyframes zoomPanRight {
    /* Initial state */
    0% { transform: scale(1) translate(0, 0); }

    /* Zoom up quickly (0.25s) */
    3.23% { transform: scale(3) translate(-15%, 0); }

    /* Pan across right side (5s) */
    19.35% { transform: scale(3) translate(-15%, 10%); }
    35.48% { transform: scale(3) translate(-15%, 0); }
    51.61% { transform: scale(3) translate(-15%, -10%); }
    67.74% { transform: scale(3) translate(-15%, 0); }

    /* Zoom back down (0.5s) */
    74.19% { transform: scale(1) translate(0, 0); }

    /* Rest before repeating */
    100% { transform: scale(1) translate(0, 0); }
}

/* Slot scroll animation base styles */
.animate-slot-scroll {
    position: relative;
    overflow: hidden;
}

/* Hide the original image during slot scroll animations */
.animate-slot-scroll img {
    opacity: 0 !important;
}

/* VARIABLE SPEED ANIMATIONS - CSS-based */

/* Container for horizontal variable speed */
.animate-slot-scroll .scroll-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 1000%; /* Multiple copies for seamless scrolling */
    height: 100%;
    display: flex;
}

/* Container for vertical variable speed */
.animate-slot-scroll .scroll-container-vertical {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1000%; /* Multiple copies for seamless scrolling */
    display: flex;
    flex-direction: column;
}

/* Style for the images in variable speed animations */
.animate-slot-scroll .scroll-image,
.animate-slot-scroll .scroll-image-duplicate {
    width: 100%;
    height: 100%;
    flex: 0 0 10%; /* Each image takes up 10% of the container */
    background-size: cover;
    background-position: center;
}

/* Horizontal slot scroll with variable speed (speeds up, 3 scrolls, slows down) */
@keyframes slotScrollHorizontal {
    0% { transform: translateX(0); }
    100% { transform: translateX(-90%); } /* Move by exactly 9 symbols */
}

/* Vertical slot scroll with variable speed (speeds up, 3 scrolls, slows down) */
@keyframes slotScrollVertical {
    0% { transform: translateY(0); }
    100% { transform: translateY(-90%); } /* Move by exactly 9 symbols */
}

/* Apply variable speed animations to containers */
.animate-slot-scroll-horizontal .scroll-container {
    animation: slotScrollHorizontal 4s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
}

.animate-slot-scroll-vertical .scroll-container-vertical {
    animation: slotScrollVertical 4s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
}

/* CONSTANT SPEED ANIMATIONS - Special containers */

/* Container for constant speed animations */
.animate-slot-scroll .constant-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Reels for constant speed horizontal animation */
.animate-slot-scroll .horizontal-reel {
    position: absolute;
    width: 1000%;
    height: 100%;
    top: 0;
    left: 0;
    display: flex;
}

/* Reels for constant speed vertical animation */
.animate-slot-scroll .vertical-reel {
    position: absolute;
    width: 100%;
    height: 1000%;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
}

/* Symbols for constant speed animations */
.animate-slot-scroll .horizontal-symbol {
    width: 10%;
    height: 100%;
    flex-shrink: 0;
    background-size: cover;
    background-position: center;
}

.animate-slot-scroll .vertical-symbol {
    width: 100%;
    height: 10%;
    flex-shrink: 0;
    background-size: cover;
    background-position: center;
}

/* Random animation delays */
.gallery-item.delay-0 img { animation-delay: 0s; }
.gallery-item.delay-1 img { animation-delay: 1s; }
.gallery-item.delay-2 img { animation-delay: 2s; }
.gallery-item.delay-3 img { animation-delay: 3s; }
.gallery-item.delay-4 img { animation-delay: 4s; }
.gallery-item.delay-5 img { animation-delay: 5s; }

/* Responsive styles */
@media (max-width: 992px) {
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .gallery-grid {
        grid-template-columns: 1fr;
    }
}

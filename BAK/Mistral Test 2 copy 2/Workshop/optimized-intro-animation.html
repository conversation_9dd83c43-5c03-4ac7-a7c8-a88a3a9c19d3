<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optimized Intro Animation</title>
    <link href="https://fonts.googleapis.com/css2?family=Syncopate:wght@400;700&family=Hanken+Grotesk:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            font-family: 'Hanken Grotesk', sans-serif;
            font-weight: 400;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
            line-height: 1.4;
        }

        .container {
            margin-bottom: 50px;
            text-align: center;
            max-width: 800px;
        }

        h1 {
            font-family: 'Syncopate', sans-serif;
            font-weight: 700;
            text-transform: uppercase;
            font-size: clamp(2.5rem, 7vw, 5.5rem);
            letter-spacing: -0.05em;
            line-height: 0.9;
            margin-bottom: 30px;
        }

        .subheadline {
            font-family: 'Hanken Grotesk', sans-serif;
            font-size: 1.2rem;
            line-height: 1.4;
            font-weight: 400;
            margin-bottom: 30px;
            letter-spacing: 0.05em; /* Increased for all-caps */
            text-transform: uppercase; /* Ensure all-caps */
        }

        .cta-button {
            display: inline-block;
            background-color: #ffffff;
            color: #1a1a1a;
            padding: 15px 30px;
            text-decoration: none;
            font-size: 1.1em;
            font-weight: 700;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s ease;
            letter-spacing: 0.02em;
            font-family: 'Hanken Grotesk', sans-serif;
        }

        .cta-button:hover {
            background-color: #eeeeee;
        }

        /* Slot animation styles - optimized for capital letters */
        .slot-mask {
            display: inline-block;
            overflow: hidden;
            vertical-align: bottom;
            height: 1em; /* Reduced height since we only use capital letters */
        }

        /* For headline */
        h1 .slot-mask {
            height: 1em;
        }

        /* For subheadline */
        .subheadline .slot-mask {
            height: 1em;
        }

        /* For button */
        .button-mask {
            display: inline-block;
            overflow: hidden;
            vertical-align: bottom;
            height: 54px; /* Match button height */
            margin-top: 0;
            margin-bottom: 0;
        }

        /* Ensure button has no margins inside the mask */
        .button-mask .cta-button {
            margin: 0;
        }

        .slot-reel {
            display: block;
            transform: translateY(700%);
        }

        /* Initial slot animation */
        @keyframes slotSpin {
            0% { transform: translateY(700%); }
            15% { transform: translateY(500%); }
            30% { transform: translateY(300%); }
            45% { transform: translateY(100%); }
            60% { transform: translateY(-50%); }
            75% { transform: translateY(-20%); }
            85% { transform: translateY(-10%); }
            100% { transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 id="headline">CREATIVE PARTNER FOR GAMES</h1>
        <p id="subheadline" class="subheadline">CATCHY SUB-HEADLINE OR DESCRIPTION</p>
        <div id="button-container">
            <!-- Button will be created by JavaScript -->
        </div>
    </div>

    <script>
        // Number of items to show in each reel
        const REEL_ITEMS = 7;

        document.addEventListener('DOMContentLoaded', () => {
            // Process the headline
            const headline = document.getElementById('headline');
            processElement(headline);

            // Process the subheadline
            const subheadline = document.getElementById('subheadline');
            processElement(subheadline);

            // Process the button separately
            processButton();
        });

        function processElement(element) {
            // Get the original text
            const originalText = element.textContent;

            // Split into words
            const words = originalText.split(/\s+/);

            // Clear the element
            element.innerHTML = '';

            // Process each word
            words.forEach((word, wordIndex) => {
                // Create slot mask
                const slotMask = document.createElement('span');
                slotMask.className = 'slot-mask';

                // Add specific class for button if needed
                if (element.classList.contains('cta-button')) {
                    slotMask.className = 'button-mask';
                }

                // Create slot reel
                const slotReel = document.createElement('div');
                slotReel.className = 'slot-reel';

                // Generate items for the reel - all showing the actual word
                for (let i = 0; i < REEL_ITEMS; i++) {
                    const slotItem = document.createElement('div');
                    slotItem.style.height = '1em'; // Reduced for capital letters
                    slotItem.style.lineHeight = '1'; // Set to 1 for capital letters
                    slotItem.textContent = word;
                    slotReel.appendChild(slotItem);
                }

                // Create the final word
                const finalItem = document.createElement('div');
                finalItem.style.height = '1em'; // Reduced for capital letters
                finalItem.style.lineHeight = '1'; // Set to 1 for capital letters
                finalItem.textContent = word;

                slotReel.appendChild(finalItem);

                // Set animation delay - staggered sequence
                const delay = 0.05 + (wordIndex * 0.08);
                const duration = 0.8;

                // Apply the animation
                slotReel.style.animation = `slotSpin ${duration}s cubic-bezier(0.19, 1, 0.22, 1) forwards`;
                slotReel.style.animationDelay = `${delay}s`;

                // Assemble and add to element
                slotMask.appendChild(slotReel);
                element.appendChild(slotMask);

                // Add space after word (except last word)
                if (wordIndex < words.length - 1) {
                    element.appendChild(document.createTextNode(' '));
                }
            });
        }

        // Function to handle button animation as a single unit
        function processButton() {
            // Get the button container
            const buttonContainer = document.getElementById('button-container');

            // Create button mask
            const buttonMask = document.createElement('div');
            buttonMask.className = 'button-mask';

            // Create slot reel for button
            const buttonReel = document.createElement('div');
            buttonReel.className = 'slot-reel';

            // Create the button
            const button = document.createElement('a');
            button.href = '#';
            button.className = 'cta-button';
            button.textContent = 'GET IN TOUCH';

            // Add button to reel
            buttonReel.appendChild(button);

            // Set animation delay - after headline and subheadline
            const delay = 0.5; // Delay after headline and subheadline
            const duration = 0.8;

            // Apply the animation to the entire button
            buttonReel.style.animation = `slotSpin ${duration}s cubic-bezier(0.19, 1, 0.22, 1) forwards`;
            buttonReel.style.animationDelay = `${delay}s`;

            // Assemble and add to container
            buttonMask.appendChild(buttonReel);
            buttonContainer.appendChild(buttonMask);
        }
    </script>
</body>
</html>

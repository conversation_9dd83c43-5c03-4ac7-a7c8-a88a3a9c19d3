<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us - Widget Collection</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hanken+Grotesk:wght@400;500;700&family=Syncopate:wght@400;700&family=Inter:wght@400;700&family=Space+Mono:wght@400;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Hanken Grotesk', sans-serif;
            background: linear-gradient(to bottom, #00A2FF 0%, #000000 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 100px 40px;
        }

        /* Typography */
        h2 {
            font-family: 'Syncopate', sans-serif;
            font-weight: 700;
            text-transform: uppercase;
            font-size: clamp(1.8rem, 5vw, 4rem);
            letter-spacing: -0.05em;
            line-height: 0.9;
            margin-bottom: 40px;
            text-align: left;
        }

        .about-container {
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }

        /* Widget Grid */
        .widget-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 12px;
            grid-auto-rows: minmax(140px, auto);
            width: 100%;
            max-width: 1200px; /* Ensure grid doesn't exceed container */
        }

        /* Widget Base Styles */
        .widget {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 16px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .widget:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        /* Widget Sizes */
        .widget.large {
            grid-column: span 2;
            grid-row: span 2;
        }

        .widget.dominant {
            grid-column: span 3;
            grid-row: span 2;
        }

        .widget.extra-wide {
            grid-column: span 4;
            grid-row: span 2;
        }

        .widget.wide {
            grid-column: span 2;
        }

        .widget.tall {
            grid-row: span 2;
        }

        /* Widget Content */
        .widget-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .widget-icon {
            display: none;
        }

        .widget-title {
            font-family: 'Inter', sans-serif;
            font-size: 1.2rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: normal;
            background: rgba(255, 255, 255, 0.15);
            padding: 8px 12px;
            border-radius: 8px;
            display: inline-block;
        }

        .widget-content {
            font-family: 'Inter', sans-serif;
            font-size: 0.75rem;
            font-weight: 400;
            line-height: 1.6;
            letter-spacing: 0.02em;
            opacity: 0.9;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .widget-number {
            font-family: 'Inter', sans-serif;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 4px;
            background: linear-gradient(45deg, #ffffff, #e3f2fd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
        }

        .widget-label {
            font-family: 'Inter', sans-serif;
            font-size: 0.65rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            opacity: 0.8;
            line-height: 1.2;
        }

        /* Specific Widget Styles */
        .team-widget {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.3), rgba(41, 128, 185, 0.3));
        }

        .experience-widget {
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.3), rgba(39, 174, 96, 0.3));
        }

        .projects-widget {
            background: linear-gradient(135deg, rgba(155, 89, 182, 0.3), rgba(142, 68, 173, 0.3));
        }

        .mission-widget {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #ffffff;
        }

        .mission-widget .widget-title {
            color: #ffffff;
            background: rgba(255, 255, 255, 0.1);
        }

        .mission-widget .widget-content {
            color: #ffffff;
        }

        /* Section Intro Animations */
        .widget {
            opacity: 0;
            transform: scale(0.8);
        }

        .widget:nth-child(1) { animation: scaleInFade 0.7s ease-out 0.3s forwards; }
        .widget:nth-child(2) { animation: scaleInFade 0.7s ease-out 0.5s forwards; }
        .widget:nth-child(3) { animation: scaleInFade 0.7s ease-out 0.7s forwards; }
        .widget:nth-child(4) { animation: scaleInFade 0.7s ease-out 0.9s forwards; }
        .widget:nth-child(5) { animation: scaleInFade 0.7s ease-out 1.1s forwards; }
        .widget:nth-child(6) { animation: scaleInFade 0.7s ease-out 1.3s forwards; }
        .widget:nth-child(7) { animation: scaleInFade 0.7s ease-out 1.5s forwards; }

        @keyframes scaleInFade {
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .skills-widget {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.3), rgba(192, 57, 43, 0.3));
        }

        .vision-widget {
            background: linear-gradient(135deg, rgba(52, 73, 94, 0.3), rgba(44, 62, 80, 0.3));
        }

        .vibe-widget {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 152, 0, 0.3));
        }

        .aura-widget {
            background: linear-gradient(135deg, rgba(156, 39, 176, 0.3), rgba(123, 31, 162, 0.3));
        }

        .map-widget {
            background: linear-gradient(135deg, rgba(26, 188, 156, 0.3), rgba(22, 160, 133, 0.3));
        }

        /* Skill Tags */
        .skill-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .skill-tag {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 12px;
            font-family: 'Inter', sans-serif;
            font-size: 0.65rem;
            font-weight: 700;
        }

        /* Progress Bars */
        .progress-item {
            margin-bottom: 8px;
        }

        .progress-label {
            font-family: 'Inter', sans-serif;
            font-size: 0.65rem;
            font-weight: 700;
            margin-bottom: 3px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ffffff, rgba(255, 255, 255, 0.8));
            border-radius: 3px;
            transition: width 1s ease;
        }

        /* Vibe Check Styles */
        .vibe-simple {
            text-align: center;
        }

        .vibe-simple .widget-label {
            margin-bottom: 12px;
        }

        .vibe-status-row {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 16px;
        }

        .status-options {
            display: flex;
            gap: 12px;
        }

        .status-option {
            font-family: 'Inter', sans-serif;
            font-size: 1.1rem;
            font-weight: 700;
            padding: 4px 8px;
            border-radius: 4px;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .status-option.active {
            opacity: 1;
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
            text-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
        }

        .status-indicator-large {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ff4444;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .status-indicator-large.on {
            background: #00ff88;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
            animation: sineWave 4s infinite ease-in-out !important;
            position: relative;
            overflow: hidden;
        }

        .status-indicator-large.on::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: sineWaveFlow 6s infinite;
        }

        @keyframes sineWave {
            0% {
                transform: scale(1) translateY(0px);
                box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
            }
            25% {
                transform: scale(1.1) translateY(-1px);
                box-shadow: 0 0 25px rgba(0, 255, 136, 0.8);
            }
            50% {
                transform: scale(1) translateY(0px);
                box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
            }
            75% {
                transform: scale(0.9) translateY(1px);
                box-shadow: 0 0 15px rgba(0, 255, 136, 0.4);
            }
            100% {
                transform: scale(1) translateY(0px);
                box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
            }
        }

        @keyframes sineWaveFlow {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .status-text {
            font-family: 'Inter', sans-serif;
            font-size: 1.1rem;
            font-weight: 700;
            color: #00ff88;
            text-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
        }

        /* Aura Meter Styles */
        .aura-meter {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 16px;
        }

        .aura-bar {
            width: 100%;
            height: 14px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 7px;
            overflow: hidden;
            position: relative;
        }

        .aura-fill {
            height: 100%;
            width: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7);
            border-radius: 10px;
            position: relative;
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .aura-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shine 2s infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .aura-percentage {
            font-family: 'Inter', sans-serif;
            font-size: 1.3rem;
            font-weight: 700;
            text-align: center;
            background: linear-gradient(45deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: rainbow 3s infinite;
            line-height: 1;
        }

        @keyframes rainbow {
            0%, 100% { filter: hue-rotate(0deg); }
            50% { filter: hue-rotate(180deg); }
        }

        /* World Map Styles */
        .world-map {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .map-svg {
            width: 100%;
            height: 120px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 8px;
        }

        .location-marker {
            cursor: pointer;
            filter: drop-shadow(0 0 6px currentColor);
        }

        .location-marker:hover {
            filter: drop-shadow(0 0 12px currentColor);
        }

        /* Timezone Clocks */
        .timezone-clocks {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
        }

        .clock-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .team-count {
            font-family: 'Inter', sans-serif;
            font-size: 1.2rem;
            font-weight: 700;
            color: #ffffff;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 4px;
        }

        .clock-face {
            width: 60px;
            height: 60px;
            border: 2px solid rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            position: relative;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .clock-face::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 4px;
            height: 4px;
            background: #ffffff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 3;
            box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
        }

        .clock-ticks {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .clock-tick {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
        }

        .clock-face.nighttime {
            background: rgba(0, 0, 0, 0.4);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .clock-face.nighttime .clock-tick {
            background: rgba(255, 255, 255, 0.4);
        }

        .clock-face.nighttime .clock-hand {
            background: rgba(255, 255, 255, 0.8);
        }

        .clock-hand {
            position: absolute;
            background: #ffffff;
            transform-origin: bottom center;
            border-radius: 2px;
            box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
        }

        .hour-hand {
            width: 2px;
            height: 18px;
            top: 12px;
            left: 50%;
            margin-left: -1px;
            z-index: 2;
        }

        .minute-hand {
            width: 1px;
            height: 24px;
            top: 6px;
            left: 50%;
            margin-left: -0.5px;
            z-index: 1;
        }

        .clock-label {
            text-align: center;
        }

        .city-name {
            font-family: 'Inter', sans-serif;
            font-size: 0.6rem;
            font-weight: 700;
            margin-bottom: 1px;
        }

        .time-display {
            font-family: 'Inter', sans-serif;
            font-size: 0.55rem;
            font-weight: 700;
            opacity: 0.9;
        }

        /* Pie Chart Styles */
        .pie-chart {
            position: relative;
            width: 200px;
            height: 200px;
            flex-shrink: 0;
        }

        .pie-chart-inner {
            position: relative;
            width: 200px;
            height: 200px;
        }

        .pie-svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .pie-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-family: 'Inter', sans-serif;
            font-size: 2.8rem;
            font-weight: 700;
        }

        /* Team Breakdown Layout */
        .team-breakdown-layout {
            display: flex;
            gap: 16px;
            align-items: center;
            justify-content: center;
        }

        .pie-legend {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 8px;
            font-family: 'Inter', sans-serif;
            font-size: 0.75rem;
            font-weight: 700;
            flex: 1;
            text-align: left;
        }

        .pie-chart {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 3px;
            flex-shrink: 0;
        }

        /* Bar Chart Styles */
        .bar-chart {
            display: flex;
            align-items: end;
            gap: 6px;
            height: 120px;
            padding: 8px 0;
        }

        .bar-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
        }

        .bar-container {
            width: 100%;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            display: flex;
            align-items: end;
            margin: 4px 0;
        }

        .bar-fill {
            width: 100%;
            border-radius: 2px;
            transition: height 1s ease;
            animation: barGrow 1s ease;
        }

        @keyframes barGrow {
            from { height: 0%; }
            to { height: var(--final-height); }
        }

        .bar-label {
            font-family: 'Inter', sans-serif;
            font-size: 0.5rem;
            font-weight: 700;
            text-align: center;
            writing-mode: vertical-rl;
            text-orientation: mixed;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .bar-value {
            font-family: 'Inter', sans-serif;
            font-size: 0.6rem;
            font-weight: 700;
            margin-top: 2px;
        }

        /* Expertise Grid */
        .expertise-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .expertise-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .expertise-checkbox {
            width: 16px;
            height: 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 700;
            color: #00ff88;
            flex-shrink: 0;
        }

        .expertise-text {
            font-family: 'Inter', sans-serif;
            font-size: 0.75rem;
            font-weight: 700;
            line-height: 1.2;
        }

        /* ARTCHOP Widget Layout */
        .mission-widget .widget-content {
            font-size: 1.05rem;
        }

        .artchop-content {
            display: flex;
            gap: 24px;
            align-items: flex-start;
        }

        .artchop-text {
            flex: 2;
            min-width: 0;
        }

        .artchop-visual {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
            min-width: 200px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-family: 'Inter', sans-serif;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
            background: linear-gradient(45deg, #ffffff, #e3f2fd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-family: 'Inter', sans-serif;
            font-size: 0.65rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            opacity: 0.8;
        }

        .artchop-logo {
            background: rgba(255, 255, 255, 0.1);
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }

        .logo-text {
            font-family: 'Inter', sans-serif;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 4px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-tagline {
            font-family: 'Inter', sans-serif;
            font-size: 0.7rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            opacity: 0.8;
        }

        /* Technology Logos */
        .tech-logos {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 8px;
        }

        .tech-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .tech-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .tech-logo {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
            font-size: 0.8rem;
            font-weight: 700;
            flex-shrink: 0;
        }

        .tech-name {
            font-family: 'Inter', sans-serif;
            font-size: 0.65rem;
            font-weight: 700;
            line-height: 1;
        }

        /* Individual Logo Colors */
        .unity-logo { background: #000000; color: #ffffff; }
        .unreal-logo { background: #0e1128; color: #ffffff; }
        .shader-logo { background: #4a90e2; color: #ffffff; }
        .substance-painter-logo { background: #f57c00; color: #ffffff; }
        .substance-designer-logo { background: #e65100; color: #ffffff; }
        .marvelous-logo { background: #8e24aa; color: #ffffff; }
        .zbrush-logo { background: #d32f2f; color: #ffffff; }
        .maya-logo { background: #37a5cc; color: #ffffff; }

        /* Responsive for ARTCHOP widget */
        @media (max-width: 1024px) {
            .artchop-content {
                flex-direction: column;
                gap: 16px;
            }

            .artchop-visual {
                min-width: auto;
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .widget-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .widget.large,
            .widget.wide,
            .widget.dominant,
            .widget.extra-wide {
                grid-column: span 1;
            }

            .widget.large,
            .widget.dominant,
            .widget.extra-wide {
                grid-row: span 1;
            }

            body {
                padding: 60px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="about-container">
        <h2>THE GET-IT-DONE CREW</h2>

        <div class="widget-grid">
            <!-- ARTCHOP! Widget - FIRST -->
            <div class="widget mission-widget dominant">
                <div class="widget-header">
                    <div class="widget-icon">🎯</div>
                    <div class="widget-title">ARTCHOP!</div>
                </div>
                <div class="widget-content">
                    <p><span style="font-weight: 700;">WE'RE A MODERN CREATIVE STUDIO BUILT FOR COLLABORATION.</span></p>
                    <br>
                    <p>Backed by deep development experience, creative instincts, and strong technical know-how, Artchop is structured to reduce friction and de-risk outsourcing. Our US-based front office handles communication and coordination, while our global senior team plugs in fast and gets to work.</p>
                    <br>
                    <p>For our clients, we fill gaps, support overflow, or take ownership of entire initiatives. We integrate quickly, align easily, and deliver consistently. We've contributed to award-winning games, long-term partnerships, and high-stakes projects that needed reliable hands when it mattered most.</p>
                </div>
            </div>

            <!-- Team Breakdown Pie Chart -->
            <div class="widget projects-widget large">
                <div class="widget-header">
                    <div class="widget-icon">📊</div>
                    <div class="widget-title">Team Breakdown</div>
                </div>
                <div class="widget-content">
                    <div class="team-breakdown-layout">
                        <div class="pie-legend">
                            <div class="legend-item"><span class="legend-color" style="background: #e74c3c;"></span>3D MODELERS (10)</div>
                            <div class="legend-item"><span class="legend-color" style="background: #f39c12;"></span>PROGRAMMERS (8)</div>
                            <div class="legend-item"><span class="legend-color" style="background: #2ecc71;"></span>DD SPECIALISTS (6)</div>
                            <div class="legend-item"><span class="legend-color" style="background: #3498db;"></span>CONCEPT ARTISTS (5)</div>
                            <div class="legend-item"><span class="legend-color" style="background: #9b59b6;"></span>ANIMATORS (3)</div>
                            <div class="legend-item"><span class="legend-color" style="background: #f1c40f;"></span>ART MANAGERS (3)</div>
                            <div class="legend-item"><span class="legend-color" style="background: #ff69b4;"></span>TECHNICAL ARTISTS (2)</div>
                        </div>
                        <div class="pie-chart">
                            <svg viewBox="0 0 100 100" class="pie-svg">
                                <!-- Background circle -->
                                <circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="20"/>

                                <!-- 3D Modelers: 10/37 = 27.0% of 251.2 circumference = 67.8 -->
                                <circle cx="50" cy="50" r="40" fill="none" stroke="#e74c3c" stroke-width="20"
                                        stroke-dasharray="67.8 183.4" stroke-dashoffset="62.8" transform="rotate(-90 50 50)"/>
                                <!-- Programmers: 8/37 = 21.6% = 54.3 -->
                                <circle cx="50" cy="50" r="40" fill="none" stroke="#f39c12" stroke-width="20"
                                        stroke-dasharray="54.3 196.9" stroke-dashoffset="-5.0" transform="rotate(-90 50 50)"/>
                                <!-- DD Specialists: 6/37 = 16.2% = 40.7 -->
                                <circle cx="50" cy="50" r="40" fill="none" stroke="#2ecc71" stroke-width="20"
                                        stroke-dasharray="40.7 210.5" stroke-dashoffset="-59.3" transform="rotate(-90 50 50)"/>
                                <!-- Concept Artists: 5/37 = 13.5% = 33.9 -->
                                <circle cx="50" cy="50" r="40" fill="none" stroke="#3498db" stroke-width="20"
                                        stroke-dasharray="33.9 217.3" stroke-dashoffset="-100.0" transform="rotate(-90 50 50)"/>
                                <!-- Animators: 3/37 = 8.1% = 20.3 -->
                                <circle cx="50" cy="50" r="40" fill="none" stroke="#9b59b6" stroke-width="20"
                                        stroke-dasharray="20.3 230.9" stroke-dashoffset="-133.9" transform="rotate(-90 50 50)"/>
                                <!-- Art Managers: 3/37 = 8.1% = 20.3 -->
                                <circle cx="50" cy="50" r="40" fill="none" stroke="#f1c40f" stroke-width="20"
                                        stroke-dasharray="20.3 230.9" stroke-dashoffset="-154.2" transform="rotate(-90 50 50)"/>
                                <!-- Technical Artists: 2/37 = 5.4% = 13.6 -->
                                <circle cx="50" cy="50" r="40" fill="none" stroke="#ff69b4" stroke-width="20"
                                        stroke-dasharray="13.6 237.6" stroke-dashoffset="-174.5" transform="rotate(-90 50 50)"/>
                            </svg>
                            <div class="pie-center">37</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technologies Widget -->
            <div class="widget vision-widget tall">
                <div class="widget-header">
                    <div class="widget-icon">💻</div>
                    <div class="widget-title">Technologies</div>
                </div>
                <div class="widget-content">
                    <div class="tech-logos">
                        <div class="tech-item">
                            <div class="tech-logo unity-logo">U</div>
                            <div class="tech-name">UNITY</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-logo unreal-logo">UE</div>
                            <div class="tech-name">UNREAL</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-logo shader-logo">SG</div>
                            <div class="tech-name">SHADER GRAPH</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-logo substance-painter-logo">SP</div>
                            <div class="tech-name">SUBSTANCE 3D PAINTER</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-logo substance-designer-logo">SD</div>
                            <div class="tech-name">SUBSTANCE 3D DESIGNER</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-logo marvelous-logo">MD</div>
                            <div class="tech-name">MARVELOUS DESIGNER</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-logo zbrush-logo">Z</div>
                            <div class="tech-name">ZBRUSH</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-logo maya-logo">M</div>
                            <div class="tech-name">MAYA</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Expertise Widget (Wide) -->
            <div class="widget skills-widget wide">
                <div class="widget-header">
                    <div class="widget-icon">⚡</div>
                    <div class="widget-title">Expertise</div>
                </div>
                <div class="widget-content">
                    <div class="expertise-grid">
                        <div class="expertise-item">
                            <div class="expertise-checkbox">✓</div>
                            <div class="expertise-text">CONCEPT, KEY & PRODUCTION ART</div>
                        </div>
                        <div class="expertise-item">
                            <div class="expertise-checkbox">✓</div>
                            <div class="expertise-text">HARD SURFACE MODELS</div>
                        </div>
                        <div class="expertise-item">
                            <div class="expertise-checkbox">✓</div>
                            <div class="expertise-text">MODULAR GAME ASSETS</div>
                        </div>
                        <div class="expertise-item">
                            <div class="expertise-checkbox">✓</div>
                            <div class="expertise-text">CHARACTER MODELS</div>
                        </div>
                        <div class="expertise-item">
                            <div class="expertise-checkbox">✓</div>
                            <div class="expertise-text">TEXTURE & MATERIALS</div>
                        </div>
                        <div class="expertise-item">
                            <div class="expertise-checkbox">✓</div>
                            <div class="expertise-text">HD DIGITAL DOUBLES</div>
                        </div>
                        <div class="expertise-item">
                            <div class="expertise-checkbox">✓</div>
                            <div class="expertise-text">ENVIRONMENTS</div>
                        </div>
                        <div class="expertise-item">
                            <div class="expertise-checkbox">✓</div>
                            <div class="expertise-text">RIGGING</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vibe Check Widget -->
            <div class="widget vibe-widget">
                <div class="widget-header">
                    <div class="widget-icon">✨</div>
                    <div class="widget-title">Vibe Check</div>
                </div>
                <div class="widget-content">
                    <div class="vibe-simple">
                        <div class="widget-label">CURRENT STATUS</div>
                        <div class="vibe-status-row">
                            <div class="status-options">
                                <span class="status-option">OFF</span>
                                <span class="status-option active">ON</span>
                            </div>
                            <div class="status-indicator-large on"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aura Meter Widget -->
            <div class="widget aura-widget">
                <div class="widget-header">
                    <div class="widget-icon">🌟</div>
                    <div class="widget-title">Aura Meter</div>
                </div>
                <div class="widget-content">
                    <div class="aura-meter">
                        <div class="aura-bar">
                            <div class="aura-fill"></div>
                        </div>
                        <div class="aura-percentage">100%</div>
                    </div>
                    <div class="widget-label" style="text-align: center;">MAXIMUM AURA</div>
                </div>
            </div>

            <!-- World Map Widget -->
            <div class="widget map-widget wide">
                <div class="widget-header">
                    <div class="widget-icon">🗺️</div>
                    <div class="widget-title">Locations</div>
                </div>
                <div class="widget-content">
                    <div class="world-map">
                        <!-- Time Zone Clocks -->
                        <div class="timezone-clocks">
                            <div class="clock-item">
                                <div class="clock-face" id="sf-clock">
                                    <div class="clock-ticks">
                                        <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                        <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                        <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                        <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                    </div>
                                    <div class="clock-hand hour-hand" id="sf-hour"></div>
                                    <div class="clock-hand minute-hand" id="sf-minute"></div>
                                </div>
                                <div class="clock-label">
                                    <div class="city-name">SAN FRANCISCO</div>
                                    <div class="time-display" id="sf-time">--:--</div>
                                </div>
                            </div>

                            <div class="clock-item">
                                <div class="clock-face" id="saitama-clock">
                                    <div class="clock-ticks">
                                        <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                        <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                        <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                        <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                    </div>
                                    <div class="clock-hand hour-hand" id="saitama-hour"></div>
                                    <div class="clock-hand minute-hand" id="saitama-minute"></div>
                                </div>
                                <div class="clock-label">
                                    <div class="city-name">SAITAMA</div>
                                    <div class="time-display" id="saitama-time">--:--</div>
                                </div>
                            </div>

                            <div class="clock-item">
                                <div class="clock-face" id="manila-clock">
                                    <div class="clock-ticks">
                                        <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                        <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                        <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                        <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                    </div>
                                    <div class="clock-hand hour-hand" id="manila-hour"></div>
                                    <div class="clock-hand minute-hand" id="manila-minute"></div>
                                </div>
                                <div class="clock-label">
                                    <div class="city-name">MANILA</div>
                                    <div class="time-display" id="manila-time">--:--</div>
                                </div>
                            </div>

                            <div class="clock-item">
                                <div class="clock-face" id="novi-sad-clock">
                                    <div class="clock-ticks">
                                        <div class="clock-tick" style="top: 2px; left: 50%; transform: translateX(-50%);"></div>
                                        <div class="clock-tick" style="top: 50%; right: 2px; transform: translateY(-50%);"></div>
                                        <div class="clock-tick" style="bottom: 2px; left: 50%; transform: translateX(-50%);"></div>
                                        <div class="clock-tick" style="top: 50%; left: 2px; transform: translateY(-50%);"></div>
                                    </div>
                                    <div class="clock-hand hour-hand" id="novi-sad-hour"></div>
                                    <div class="clock-hand minute-hand" id="novi-sad-minute"></div>
                                </div>
                                <div class="clock-label">
                                    <div class="city-name">NOVI SAD</div>
                                    <div class="time-display" id="novi-sad-time">--:--</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add some interactive animations
        document.addEventListener('DOMContentLoaded', function() {
            // Animate progress bars on load
            setTimeout(() => {
                const progressBars = document.querySelectorAll('.progress-fill');
                progressBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 100);
                });
            }, 500);

            // Add hover effects to widgets
            const widgets = document.querySelectorAll('.widget');
            widgets.forEach(widget => {
                widget.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                widget.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // World Clock Functionality
            const timezones = {
                'sf': 'America/Los_Angeles',
                'saitama': 'Asia/Tokyo',
                'manila': 'Asia/Manila',
                'novi-sad': 'Europe/Belgrade'
            };

            function updateClocks() {
                Object.keys(timezones).forEach(city => {
                    const now = new Date();
                    const timeInCity = new Date(now.toLocaleString("en-US", {timeZone: timezones[city]}));

                    const hours = timeInCity.getHours();
                    const minutes = timeInCity.getMinutes();

                    // Update digital time display
                    const timeDisplay = document.getElementById(`${city}-time`);
                    if (timeDisplay) {
                        timeDisplay.textContent = timeInCity.toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                        });
                    }

                    // Update analog clock hands
                    const hourHand = document.getElementById(`${city}-hour`);
                    const minuteHand = document.getElementById(`${city}-minute`);

                    if (hourHand && minuteHand) {
                        const hourAngle = (hours % 12) * 30 + (minutes * 0.5); // 30 degrees per hour + minute adjustment
                        const minuteAngle = minutes * 6; // 6 degrees per minute

                        hourHand.style.transform = `rotate(${hourAngle}deg)`;
                        minuteHand.style.transform = `rotate(${minuteAngle}deg)`;
                    }

                    // Update day/night appearance
                    const clockFace = document.getElementById(`${city}-clock`);
                    if (clockFace) {
                        // Night time is between 6 PM (18:00) and 6 AM (06:00)
                        if (hours >= 18 || hours < 6) {
                            clockFace.classList.add('nighttime');
                        } else {
                            clockFace.classList.remove('nighttime');
                        }
                    }
                });
            }

            // Update clocks immediately and then every second
            updateClocks();
            setInterval(updateClocks, 1000);
        });
    </script>
</body>
</html>

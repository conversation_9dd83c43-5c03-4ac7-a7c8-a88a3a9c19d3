/**
 * Artchop Website - Main JavaScript
 * Combines functionality from script.js, gallery.js, and inline scripts
 */

// ===== GLOBAL VARIABLES =====
const REEL_ITEMS = 7;
let isGalleryInitialized = false;
let currentModalImageIndex = 0;
let galleryImages = [];

// World Clock Configuration
const timezones = {
    'sf': 'America/Los_Angeles',
    'saitama': 'Asia/Tokyo',
    'manila': 'Asia/Manila',
    'novi-sad': 'Europe/Belgrade'
};

// ===== GALLERY FUNCTIONS =====

/**
 * Reset gallery animations to initial state
 */
function resetGalleryAnimations() {
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    galleryItems.forEach(item => {
        // Reset revealed state
        item.classList.remove('revealed');
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        
        // Remove animation classes
        item.classList.remove(
            'animate-slot-scroll',
            'animate-slot-scroll-horizontal',
            'animate-slot-scroll-vertical',
            'animate-zoom-pan',
            'animate-zoom-pan-circular',
            'animate-zoom-pan-left',
            'animate-zoom-pan-right',
            'animate-fade-scale'
        );
        
        // Remove animation containers
        const containers = item.querySelectorAll('.scroll-container, .scroll-container-vertical, .constant-container, .fade-container');
        containers.forEach(container => container.remove());
    });
}

/**
 * Start sequential animation for gallery items
 */
function startSequentialAnimation() {
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    // Reset all items
    galleryItems.forEach(item => {
        item.classList.remove('revealed');
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
    });
    
    // Reveal items one by one
    function revealNextItem(index) {
        if (index >= galleryItems.length) {
            // Start animations after all items are revealed
            if (window.startGalleryAnimations) {
                window.startGalleryAnimations();
            }
            return;
        }
        
        // Force reflow
        void galleryItems[index].offsetHeight;

        // Reveal current item
        galleryItems[index].classList.add('revealed');

        // Queue next item
        setTimeout(() => {
            revealNextItem(index + 1);
        }, 100);
    }

    // Start with first item
    revealNextItem(0);
}

/**
 * Initialize gallery functionality
 */
function initGallery(resetOnly = false) {
    // Reset animations
    resetGalleryAnimations();
    
    // If only resetting, stop here
    if (resetOnly) {
        return;
    }
    
    // Mark as initialized
    isGalleryInitialized = true;
    
    // Start animations
    startSequentialAnimation();
    
    // Initialize lazy loading if available
    if (typeof initLazyLoading === 'function') {
        initLazyLoading();
    }
}

// ===== MODAL FUNCTIONS =====

/**
 * Open modal with image at specified index
 */
function openModal(index) {
    const modal = document.getElementById('gallery-modal');
    const modalImage = document.getElementById('modal-image');
    
    if (!modal || !modalImage || !galleryImages.length) return;
    
    // Update current index
    currentModalImageIndex = (index + galleryImages.length) % galleryImages.length;
    
    // Set image source
    modalImage.src = galleryImages[currentModalImageIndex].src;
    modalImage.alt = galleryImages[currentModalImageIndex].alt || 'Gallery Image';
    
    // Show modal
    modal.style.display = 'flex';
    setTimeout(() => {
        modal.classList.add('active');
        modalImage.classList.add('active');
    }, 10);
    
    // Disable body scroll
    document.body.style.overflow = 'hidden';
}

/**
 * Close the modal
 */
function closeModal() {
    const modal = document.getElementById('gallery-modal');
    const modalImage = document.getElementById('modal-image');
    
    if (!modal || !modalImage) return;
    
    // Hide modal with animation
    modal.classList.remove('active');
    modalImage.classList.remove('active');
    
    // Wait for animation to complete before hiding
    setTimeout(() => {
        modal.style.display = 'none';
        // Re-enable body scroll
        document.body.style.overflow = '';
    }, 300);
}

/**
 * Show previous image in modal
 */
function showPrevImage() {
    currentModalImageIndex = (currentModalImageIndex - 1 + galleryImages.length) % galleryImages.length;
    updateModalImage();
}

/**
 * Show next image in modal
 */
function showNextImage() {
    currentModalImageIndex = (currentModalImageIndex + 1) % galleryImages.length;
    updateModalImage();
}

/**
 * Update the modal image with transition
 */
function updateModalImage() {
    const modalImage = document.getElementById('modal-image');
    if (!modalImage || !galleryImages.length) return;
    
    // Fade out current image
    modalImage.classList.remove('active');
    
    // Update source after fade out
    setTimeout(() => {
        modalImage.src = galleryImages[currentModalImageIndex].src;
        modalImage.alt = galleryImages[currentModalImageIndex].alt || 'Gallery Image';
        
        // Fade in new image
        setTimeout(() => {
            modalImage.classList.add('active');
        }, 10);
    }, 300);
}

// ===== UTILITY FUNCTIONS =====

/**
 * Check if element is in viewport
 */
function isElementInViewport(el) {
    if (!el) return false;
    
    const rect = el.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// ===== INITIALIZATION =====

document.addEventListener('DOMContentLoaded', () => {
    console.log('Artchop website initialized');
    
    // Initialize components
    initHeroSection();
    initHamburgerMenu();
    initCarousel();
    initSmoothScrolling();
    initScrollAnimations();
    initGallery();
    initContactForm();
    initWorldClocks();
    initModals();
    
    // Initialize modal event listeners for gallery
    const modal = document.getElementById('gallery-modal');
    const modalClose = document.getElementById('modal-close');
    const modalPrev = document.getElementById('modal-prev');
    const modalNext = document.getElementById('modal-next');
    
    if (modal && modalClose) {
        modalClose.addEventListener('click', closeModal);
        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeModal();
        });
    }
    
    if (modalPrev) modalPrev.addEventListener('click', showPrevImage);
    if (modalNext) modalNext.addEventListener('click', showNextImage);
    
    // Keyboard navigation for modal
    document.addEventListener('keydown', (e) => {
        if (modal && modal.style.display === 'flex') {
            if (e.key === 'Escape') {
                closeModal();
            } else if (e.key === 'ArrowLeft') {
                showPrevImage();
            } else if (e.key === 'ArrowRight') {
                showNextImage();
            }
        }
    });
    
    // Animate contact form when in view
    animateContactForm();
    
    // Handle page visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);
});

/**
 * Handle page visibility changes
 */
function handleVisibilityChange() {
    if (document.hidden) {
        // Pause animations when page is not visible
        pauseAnimations();
    } else {
        // Resume animations when page becomes visible
        resumeAnimations();
    }
}

/**
 * Pause all animations
 */
function pauseAnimations() {
    // Pause scroll animations
    document.querySelectorAll('.animate-slot-scroll').forEach(item => {
        const containers = item.querySelectorAll('.scroll-container, .scroll-container-vertical');
        containers.forEach(container => {
            container.style.animationPlayState = 'paused';
        });

        // Pause requestAnimationFrame animations
        const reels = item.querySelectorAll('[id^="horizontal-constant-reel"], [id^="vertical-constant-reel"]');
        reels.forEach(reel => {
            if (reel.id && window.animationIds && window.animationIds[reel.id]) {
                cancelAnimationFrame(window.animationIds[reel.id]);
            }
        });
    });

    // Pause zoom-pan animations
    document.querySelectorAll('.animate-zoom-pan img').forEach(img => {
        img.style.animationPlayState = 'paused';
    });
}

/**
 * Resume all animations
 */
function resumeAnimations() {
    // Resume scroll animations
    document.querySelectorAll('.animate-slot-scroll').forEach(item => {
        const containers = item.querySelectorAll('.scroll-container, .scroll-container-vertical');
        containers.forEach(container => {
            container.style.animationPlayState = 'running';
        });

        // Resume constant speed animations
        const horizontalReels = item.querySelectorAll('[id^="horizontal-constant-reel"]');
        horizontalReels.forEach(reel => {
            if (reel.id && reel.parentNode) {
                const reel1Id = reel.id;
                const reel2Id = reel.id.replace('reel1', 'reel2');
                const reel2 = document.getElementById(reel2Id);
                if (reel2) {
                    animateHorizontalConstant(reel1Id, reel2Id);
                }
            }
        });

        const verticalReels = item.querySelectorAll('[id^="vertical-constant-reel"]');
        verticalReels.forEach(reel => {
            if (reel.id && reel.parentNode) {
                const reel1Id = reel.id;
                const reel2Id = reel.id.replace('reel1', 'reel2');
                const reel2 = document.getElementById(reel2Id);
                if (reel2) {
                    animateVerticalConstant(reel1Id, reel2Id);
                }
            }
        });
    });

    // Resume zoom-pan animations
    document.querySelectorAll('.animate-zoom-pan img').forEach(img => {
        img.style.animationPlayState = 'running';
    });
}

// ===== HERO SECTION =====

function initHeroSection() {
    // Initialize hero carousel if it exists
    const heroCarousel = document.querySelector('.hero-carousel');
    if (heroCarousel) {
        initHeroCarousel();
    }
    
    // Initialize any hero section animations
    const heroHeadlines = document.querySelectorAll('.hero-content h1, .hero-content h2');
    heroHeadlines.forEach(headline => {
        headline.style.opacity = '0';
        headline.style.transform = 'translateY(20px)';
        headline.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        
        // Trigger animation after a short delay
        setTimeout(() => {
            headline.style.opacity = '1';
            headline.style.transform = 'translateY(0)';
        }, 100);
    });
}

// ===== HAMBURGER MENU =====

function initHamburgerMenu() {
    const hamburger = document.querySelector('.hamburger-icon');
    const navLinks = document.querySelector('.nav-links');
    
    if (!hamburger || !navLinks) return;
    
    hamburger.addEventListener('click', () => {
        hamburger.classList.toggle('active');
        navLinks.classList.toggle('active');
    });
    
    // Close menu when clicking on a nav link
    const navItems = navLinks.querySelectorAll('a');
    navItems.forEach(item => {
        item.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navLinks.classList.remove('active');
        });
    });
}

// ===== CAROUSEL =====

function initCarousel() {
    const carousel = document.querySelector('.carousel');
    if (!carousel) return;
    
    const slides = carousel.querySelectorAll('.carousel-slide');
    const prevBtn = carousel.querySelector('.carousel-prev');
    const nextBtn = carousel.querySelector('.carousel-next');
    let currentSlide = 0;
    
    function showSlide(index) {
        // Hide all slides
        slides.forEach(slide => {
            slide.style.opacity = '0';
            slide.style.pointerEvents = 'none';
        });
        
        // Show current slide
        const adjustedIndex = (index + slides.length) % slides.length;
        slides[adjustedIndex].style.opacity = '1';
        slides[adjustedIndex].style.pointerEvents = 'auto';
        currentSlide = adjustedIndex;
    }
    
    function nextSlide() {
        showSlide(currentSlide + 1);
    }
    
    function prevSlide() {
        showSlide(currentSlide - 1);
    }
    
    // Add event listeners
    if (nextBtn) nextBtn.addEventListener('click', nextSlide);
    if (prevBtn) prevBtn.addEventListener('click', prevSlide);
    
    // Auto-advance slides
    let slideInterval = setInterval(nextSlide, 5000);
    
    // Pause on hover
    carousel.addEventListener('mouseenter', () => {
        clearInterval(slideInterval);
    });
    
    carousel.addEventListener('mouseleave', () => {
        slideInterval = setInterval(nextSlide, 5000);
    });
    
    // Show first slide
    showSlide(0);
}

// ===== SMOOTH SCROLLING =====

function initSmoothScrolling() {
    // Select all links with hashes
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (!targetElement) return;
            
            // Calculate the target position, accounting for fixed header
            const headerHeight = document.querySelector('header')?.offsetHeight || 80;
            const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight;
            
            // Smooth scroll to target
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
            
            // Update URL without jumping
            if (history.pushState) {
                history.pushState(null, null, targetId);
            } else {
                location.hash = targetId;
            }
        });
    });
}

// ===== SCROLL ANIMATIONS =====

function initScrollAnimations() {
    // Create intersection observer for scroll-triggered animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Add animation class when element comes into view
                entry.target.classList.add('animate');
                
                // Special handling for gallery section
                if (entry.target.id === 'gallery' && !isGalleryInitialized) {
                    initGallery();
                }
            }
        });
    }, {
        threshold: 0.2 // Trigger when 20% of the element is visible
    });
    
    // Observe sections with animations
    document.querySelectorAll('.animate-on-scroll').forEach(element => {
        observer.observe(element);
    });
}

// ===== CONTACT FORM =====

/**
 * Animate contact form when in view
 */
function animateContactForm() {
    const contactSection = document.querySelector('.contact-section');
    const formContainer = document.querySelector('.contact-form-container');
    const contactForm = document.querySelector('.contact-form');
    
    if (!contactSection || !formContainer || !contactForm) return;
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                formContainer.classList.add('animate');
                contactForm.classList.add('animate');
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });
    
    observer.observe(contactSection);
}

/**
 * Initialize contact form submission handler
 */
function initContactForm() {
    const contactForm = document.getElementById('contact-form');
    if (!contactForm) return;
    
    contactForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const form = e.target;
        const submitButton = form.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.textContent;
        
        try {
            // Show loading state
            submitButton.disabled = true;
            submitButton.textContent = 'Sending...';
            
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json'
                }
            });
            
            if (response.ok) {
                // Show success message
                alert('Thank you for your message! We will get back to you soon.');
                form.reset();
            } else {
                throw new Error('Form submission failed');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('There was a problem sending your message. Please try again later.');
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.textContent = originalButtonText;
        }
    });
}

// ===== WORLD CLOCK =====

/**
 * Update all world clocks
 */
function updateClocks() {
    Object.keys(timezones).forEach(city => {
        const now = new Date();
        const timeInCity = new Date(now.toLocaleString("en-US", {timeZone: timezones[city]}));

        const hours = timeInCity.getHours();
        const minutes = timeInCity.getMinutes();

        // Update digital time display
        const timeDisplay = document.getElementById(`${city}-time`);
        if (timeDisplay) {
            timeDisplay.textContent = timeInCity.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
        }

        // Update analog clock hands
        const hourHand = document.getElementById(`${city}-hour`);
        const minuteHand = document.getElementById(`${city}-minute`);

        if (hourHand && minuteHand) {
            const hourAngle = (hours % 12) * 30 + (minutes * 0.5); // 30 degrees per hour + minute adjustment
            const minuteAngle = minutes * 6; // 6 degrees per minute

            hourHand.style.transform = `rotate(${hourAngle}deg)`;
            minuteHand.style.transform = `rotate(${minuteAngle}deg)`;
        }

        // Update day/night appearance
        const clockFace = document.getElementById(`${city}-clock`);
        if (clockFace) {
            // Night time is between 6 PM (18:00) and 6 AM (06:00)
            if (hours >= 18 || hours < 6) {
                clockFace.classList.add('nighttime');
            } else {
                clockFace.classList.remove('nighttime');
            }
        }
    });
}

/**
 * Initialize world clocks
 */
function initWorldClocks() {
    // Only initialize if we have clock elements on the page
    const hasClocks = Object.keys(timezones).some(city => 
        document.getElementById(`${city}-clock`) || document.getElementById(`${city}-time`)
    );
    
    if (hasClocks) {
        // Update clocks immediately and then every second
        updateClocks();
        setInterval(updateClocks, 1000);
    }
}

// ===== MODAL FUNCTIONS =====

/**
 * Show modal by ID
 */
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent scrolling
    }
}

/**
 * Hide modal by ID
 */
function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = 'auto'; // Re-enable scrolling
    }
    return false;
}

/**
 * Initialize modal event listeners
 */
function initModals() {
    // Close modal when clicking outside content
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.classList.remove('active');
            document.body.style.overflow = 'auto'; // Re-enable scrolling
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const activeModal = document.querySelector('.modal.active');
            if (activeModal) {
                hideModal(activeModal.id);
            }
        }
    });
}

// ===== GLOBAL EXPORTS =====

// Make functions available globally
window.resetGalleryAnimations = resetGalleryAnimations;
window.initGallery = initGallery;
window.showModal = showModal;
window.hideModal = hideModal;

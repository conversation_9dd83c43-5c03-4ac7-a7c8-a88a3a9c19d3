/**
 * Set up smooth scrolling functionality
 */
function setupSmoothScrolling() {
    // Add click handler for logo to scroll to top
    const logo = document.querySelector('.site-logo');
    if (logo) {
        logo.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Add click handlers for navigation links with enhanced scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const target = document.querySelector(targetId);
            
            if (target) {
                if (targetId !== '#hero') {
                    const targetPosition = target.getBoundingClientRect().top + window.scrollY;
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                    target.style.position = 'relative';
                    target.style.zIndex = '20';
                } else {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
}

/**
 * Set up parallax scrolling effects
 */
function setupParallaxScrolling() {
    // Helper function to extract scale value from transform matrix
    function getScaleValue(matrix) {
        if (!matrix || matrix === 'none') return 1;
        const matrixValues = matrix.match(/matrix.*\((.+)\)/);
        if (matrixValues && matrixValues[1]) {
            const values = matrixValues[1].split(', ');
            if (values.length >= 1) {
                return parseFloat(values[0]) || 1;
            }
        }
        return 1;
    }

    // Update section classes based on scroll position
    window.addEventListener('scroll', () => {
        const sections = document.querySelectorAll('.section');
        const scrollY = window.scrollY;
        
        // Parallax for hero background (20% scroll speed)
        const heroSlideActive = document.querySelector('.hero-slide.active');
        if (heroSlideActive) {
            const heroImage = heroSlideActive.querySelector('.hero-background-image');
            if (heroImage) {
                const backgroundOffset = scrollY * 0.2;
                const currentTransform = window.getComputedStyle(heroImage).transform;
                if (currentTransform && currentTransform !== 'none') {
                    heroImage.style.transform = `translateY(${backgroundOffset}px) scale(${getScaleValue(currentTransform)})`;
                } else {
                    heroImage.style.transform = `translateY(${backgroundOffset}px)`;
                }
            }
        }
        
        // Parallax for hero overlay (20% scroll speed)
        const heroOverlay = document.querySelector('.hero-overlay');
        if (heroOverlay) {
            const overlayOffset = scrollY * 0.2;
            heroOverlay.style.transform = `translateY(${overlayOffset}px)`;
        }
        
        // Parallax for hero content (60% scroll speed)
        const heroContent = document.querySelector('.hero-content');
        if (heroContent) {
            const contentOffset = scrollY * 0.6;
            heroContent.style.transform = `translateY(${contentOffset}px)`;
        }
        
        // Section active state handling
        sections.forEach(section => {
            const rect = section.getBoundingClientRect();
            if (rect.top <= 0 && rect.bottom >= 0) {
                section.classList.add('active');
                section.classList.remove('hidden');
            } else {
                section.classList.remove('active');
                section.classList.add('hidden');
            }
        });
    });

    // Initialize first section
    const firstSection = document.querySelector('.section');
    if (firstSection) {
        firstSection.classList.add('active');
    }
}

// Immediately start the hero image animation
window.addEventListener('load', function() {
    const firstHeroImage = document.querySelector('.hero-slide.active .hero-background-image');
    if (firstHeroImage) {
        firstHeroImage.style.animation = 'scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards';
    }
});

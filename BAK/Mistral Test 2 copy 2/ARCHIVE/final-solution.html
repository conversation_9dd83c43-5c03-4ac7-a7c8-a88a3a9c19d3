<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Artchop Portfolio</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="typography.css">
    <link href="https://fonts.googleapis.com/css2?family=Tomorrow:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Tomorrow:wght@300;400;500;600;700&family=Space+Mono:wght@300;400;500;600;700&family=Inter:wght@400;700&family=Hanken+Grotesk:wght@400;500;700&family=Syncopate:wght@400;700&display=swap" rel="stylesheet">
    <style>
        /* Define scaleUpHero animation here to ensure it's available */
        @keyframes scaleUpHero {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            90% { transform: scale(1.65); }
            100% { transform: scale(1.75); }
        }

        /* Slot animation styles */
        .slot-mask {
            display: inline-block;
            overflow: hidden;
            vertical-align: bottom;
            position: relative; /* Enable absolute positioning - fixes baseline */
        }

        /* Specific heights for different elements to match their line heights in typography.css */
        h1 .slot-mask {
            height: 0.9em; /* Match h1 line-height - tightens leading */
        }

        .hero-content h1 .slot-mask {
            height: 0.85em; /* Match hero h1 line-height - tightens leading */
        }

        h2 .slot-mask {
            height: 0.92em; /* Match h2 line-height - tightens leading */
        }

        p .slot-mask {
            height: 1.4em; /* Match paragraph line-height - tightens leading */
        }

        /* Button specific styling */
        .cta-button .slot-mask {
            height: auto;
        }

        .slot-reel {
            display: block;
            position: absolute; /* Position absolutely - fixes baseline */
            bottom: 0; /* Align to bottom of mask - fixes baseline */
            left: 0;
            width: 100%;
            transform: translateY(700%);
        }

        /* Initial slot animation */
        @keyframes slotSpin {
            0% { transform: translateY(700%); }
            15% { transform: translateY(500%); }
            30% { transform: translateY(300%); }
            45% { transform: translateY(100%); }
            60% { transform: translateY(-50%); }
            75% { transform: translateY(-20%); }
            85% { transform: translateY(-10%); }
            100% { transform: translateY(0); }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-container">
            <a href="#hero" class="site-logo">
                <div class="logo-container">
                    <img src="images/artchop-logo-1.png" class="logo-image logo-1">
                    <img src="images/artchop-logo-2.png" class="logo-image logo-2">
                </div>
            </a>
            <button class="hamburger-icon" aria-label="Toggle navigation menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="nav-links" id="mobile-menu">
                <a href="#about">ABOUT</a>
                <a href="#gallery">GALLERY</a>
                <a href="#contact">CONTACT</a>
            </div>
        </div>
    </nav>
    <div class="sticky-navbar-spacer"></div>

    <!-- Hero Section -->
    <section id="hero" class="hero-section section">
        <div class="hero-overlay"></div>
        <div class="hero-carousel">
            <div class="hero-slide active">
                <img src="images/Hero1.png" alt="Hero 1" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero2.png" alt="Hero 2" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero3.png" alt="Hero 3" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero4.png" alt="Hero 4" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero5.png" alt="Hero 5" class="hero-background-image">
            </div>
        </div>
        <div class="hero-content-container">
            <div class="hero-content">
                <h1>CREATIVE PARTNER FOR GAMES</h1>
                <p>Catchy sub-headline or description.</p>
                <a href="#contact" class="cta-button">GET IN TOUCH</a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section section">
        <div class="about-content">
            <div class="about-grid">
                <div class="about-text">
                    <h2>WHO WE ARE</h2>
                    <p>Artchop is a creative studio specializing in game development and digital art. We bring passion, expertise, and innovation to every project, crafting experiences that captivate and inspire.</p>
                    <div class="about-stats">
                        <div class="stat-item">
                            <span class="stat-number">10+</span>
                            <span class="stat-label">Years Experience</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">50+</span>
                            <span class="stat-label">Projects Completed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">100+</span>
                            <span class="stat-label">Happy Clients</span>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <img src="images/about-studio.jpg" alt="Studio Team" class="about-image-main">
                    <div class="about-image-overlay"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section id="gallery" class="gallery-section section">
        <div class="gallery-content">
            <h2>OUR WORK</h2>
            <div class="gallery-grid">
                <div class="gallery-item">
                    <img src="images/gallery-1.jpg" alt="Game Art 1">
                </div>
                <div class="gallery-item">
                    <img src="images/gallery-2.jpg" alt="Game Art 2">
                </div>
                <div class="gallery-item">
                    <img src="images/gallery-3.jpg" alt="Game Art 3">
                </div>
                <div class="gallery-item">
                    <img src="images/gallery-4.jpg" alt="Game Art 4">
                </div>
                <div class="gallery-item">
                    <img src="images/gallery-5.jpg" alt="Game Art 5">
                </div>
                <div class="gallery-item">
                    <img src="images/gallery-6.jpg" alt="Game Art 6">
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section section">
        <div class="contact-content">
            <h2>CONTACT</h2>
            <p>This is the contact section content.</p>
        </div>
    </section>

    <script>
        // Helper function to extract scale value from transform matrix
        function getScaleValue(matrix) {
            if (!matrix || matrix === 'none') return 1;
            const matrixValues = matrix.match(/matrix.*\((.+)\)/);
            if (matrixValues && matrixValues[1]) {
                const values = matrixValues[1].split(', ');
                if (values.length >= 1) {
                    return parseFloat(values[0]) || 1;
                }
            }
            return 1;
        }

        // Immediately start the hero image animation
        window.addEventListener('load', function() {
            const firstHeroImage = document.querySelector('.hero-slide.active .hero-background-image');
            if (firstHeroImage) {
                firstHeroImage.style.animation = 'scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards';
            }
        });

        // Number of items to show in each reel
        const REEL_ITEMS = 7;

        document.addEventListener('DOMContentLoaded', () => {
            // Process headlines (h1, h2)
            document.querySelectorAll('h1, h2').forEach(element => {
                processElement(element);
            });

            // Process paragraphs
            document.querySelectorAll('p').forEach(element => {
                processElement(element);
            });

            // Process buttons
            document.querySelectorAll('.cta-button').forEach(element => {
                processButton(element);
            });
        });

        function processElement(element) {
            // Get the original text
            const originalText = element.textContent;

            // Split into words
            const words = originalText.split(/\s+/);

            // Clear the element
            element.innerHTML = '';

            // Process each word
            words.forEach((word, wordIndex) => {
                // Create slot mask
                const slotMask = document.createElement('span');
                slotMask.className = 'slot-mask';

                // Create slot reel
                const slotReel = document.createElement('div');
                slotReel.className = 'slot-reel';

                // Generate items for the reel - all showing the actual word
                for (let i = 0; i < REEL_ITEMS; i++) {
                    const slotItem = document.createElement('div');
                    slotItem.style.height = '1.2em';
                    slotItem.style.lineHeight = '1.2em';
                    slotItem.textContent = word;
                    slotReel.appendChild(slotItem);
                }

                // Create the final word
                const finalItem = document.createElement('div');
                finalItem.style.height = '1.2em';
                finalItem.style.lineHeight = '1.2em';
                finalItem.textContent = word;

                slotReel.appendChild(finalItem);

                // Set animation delay - fast sequence
                const delay = 0.05 + (wordIndex * 0.08);
                const duration = 0.8;

                // Apply the animation
                slotReel.style.animation = `slotSpin ${duration}s cubic-bezier(0.19, 1, 0.22, 1) forwards`;
                slotReel.style.animationDelay = `${delay}s`;

                // Assemble and add to element
                slotMask.appendChild(slotReel);
                element.appendChild(slotMask);

                // Add space after word (except last word)
                if (wordIndex < words.length - 1) {
                    element.appendChild(document.createTextNode(' '));
                }
            });
        }

        // Function to process a button as a whole
        function processButton(button) {
            // Get the original attributes
            const originalHref = button.getAttribute('href');
            const originalText = button.textContent;
            const originalClasses = button.className;

            // Get the parent element
            const parent = button.parentNode;

            // Create slot mask
            const slotMask = document.createElement('span');
            slotMask.className = 'slot-mask';

            // Create slot reel
            const slotReel = document.createElement('div');
            slotReel.className = 'slot-reel';

            // Generate items for the reel - all showing the actual button
            for (let i = 0; i < REEL_ITEMS; i++) {
                const slotItem = document.createElement('a');
                slotItem.href = originalHref;
                slotItem.className = originalClasses;
                slotItem.textContent = originalText;
                slotReel.appendChild(slotItem);
            }

            // Create the final button
            const finalItem = document.createElement('a');
            finalItem.href = originalHref;
            finalItem.className = originalClasses;
            finalItem.textContent = originalText;

            slotReel.appendChild(finalItem);

            // Set animation delay and duration
            const delay = 0.3; // Slightly delayed after headlines
            const duration = 0.8;

            // Apply the animation
            slotReel.style.animation = `slotSpin ${duration}s cubic-bezier(0.19, 1, 0.22, 1) forwards`;
            slotReel.style.animationDelay = `${delay}s`;

            // Assemble and add to parent
            slotMask.appendChild(slotReel);

            // Replace the original button with the animated version
            parent.replaceChild(slotMask, button);
        }

        // Hamburger menu functionality
        const hamburger = document.querySelector('.hamburger-icon');
        const navLinks = document.querySelector('.nav-links');

        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navLinks.classList.toggle('active');
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!hamburger.contains(e.target) && !navLinks.contains(e.target)) {
                hamburger.classList.remove('active');
                navLinks.classList.remove('active');
            }
        });

        // Carousel functionality
        const slides = document.querySelectorAll('.hero-slide');
        let currentSlide = 0;
        const totalSlides = slides.length;
        const transitionTime = 5000; // 5 seconds

        // Auto-advance function
        function autoAdvance() {
            slides[currentSlide].classList.remove('active');
            currentSlide = (currentSlide + 1) % totalSlides;
            slides[currentSlide].classList.add('active');

            const heroImage = slides[currentSlide].querySelector('.hero-background-image');
            if (heroImage) {
                const scrollY = window.scrollY;
                const backgroundOffset = scrollY * 0.2;

                heroImage.style.animation = 'none';
                void heroImage.offsetWidth; // Force reflow
                heroImage.style.animation = 'scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards';

                if (scrollY > 0) {
                    setTimeout(() => {
                        const currentTransform = window.getComputedStyle(heroImage).transform;
                        if (currentTransform && currentTransform !== 'none') {
                            heroImage.style.transform = `translateY(${backgroundOffset}px) scale(${getScaleValue(currentTransform)})`;
                        } else {
                            heroImage.style.transform = `translateY(${backgroundOffset}px)`;
                        }
                    }, 10);
                }
            }
        }

        // Start auto-advancing
        let autoAdvanceInterval = setInterval(autoAdvance, transitionTime);

        // Pause on hover
        document.querySelector('.hero-carousel').addEventListener('mouseenter', () => {
            clearInterval(autoAdvanceInterval);
        });

        document.querySelector('.hero-carousel').addEventListener('mouseleave', () => {
            autoAdvanceInterval = setInterval(autoAdvance, transitionTime);
        });

        // Add click handler for logo to scroll to top
        const logo = document.querySelector('.site-logo');
        logo.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Add click handlers for navigation links with enhanced scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const target = document.querySelector(targetId);

                if (target) {
                    if (targetId !== '#hero') {
                        const targetPosition = target.getBoundingClientRect().top + window.scrollY;
                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                        target.style.position = 'relative';
                        target.style.zIndex = '20';
                    } else {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }
            });
        });

        // Update section classes based on scroll position
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('.section');
            const scrollY = window.scrollY;

            // Parallax for hero background (20% scroll speed)
            const heroSlideActive = document.querySelector('.hero-slide.active');
            if (heroSlideActive) {
                const heroImage = heroSlideActive.querySelector('.hero-background-image');
                if (heroImage) {
                    const backgroundOffset = scrollY * 0.2;
                    const currentTransform = window.getComputedStyle(heroImage).transform;
                    if (currentTransform && currentTransform !== 'none') {
                        heroImage.style.transform = `translateY(${backgroundOffset}px) scale(${getScaleValue(currentTransform)})`;
                    } else {
                        heroImage.style.transform = `translateY(${backgroundOffset}px)`;
                    }
                }
            }

            // Parallax for hero overlay (20% scroll speed)
            const heroOverlay = document.querySelector('.hero-overlay');
            if (heroOverlay) {
                const overlayOffset = scrollY * 0.2;
                heroOverlay.style.transform = `translateY(${overlayOffset}px)`;
            }

            // Parallax for hero content (60% scroll speed)
            const heroContent = document.querySelector('.hero-content');
            if (heroContent) {
                const contentOffset = scrollY * 0.6;
                heroContent.style.transform = `translateY(${contentOffset}px)`;
            }

            // Section active state handling
            sections.forEach(section => {
                const rect = section.getBoundingClientRect();
                if (rect.top <= 0 && rect.bottom >= 0) {
                    section.classList.add('active');
                    section.classList.remove('hidden');
                } else {
                    section.classList.remove('active');
                    section.classList.add('hidden');
                }
            });
        });

        // Initialize first section
        document.querySelector('.section').classList.add('active');
    </script>
</body>
</html>

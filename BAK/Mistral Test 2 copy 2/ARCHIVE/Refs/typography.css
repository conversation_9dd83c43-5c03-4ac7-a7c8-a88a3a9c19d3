/*
==============================================
TYPOGRAPHY STYLES
==============================================
*/

/* Global headline styles */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Syncopate', sans-serif;
    font-weight: 700;
    text-transform: uppercase;
}

/*
Responsive Typography with Clamp
--------------------------------
clamp(min, preferred, max)
- min: smallest size (for small screens)
- preferred: size that scales with viewport
- max: largest size (for large screens)
*/

/* H1 - Main Headings */
h1 {
    /* Responsive sizing with clamp */
    font-size: clamp(2.5rem, 7vw, 5.5rem);
    /* Tightened kerning */
    letter-spacing: -0.05em;
    /* Ultra-tight leading */
    line-height: 0.9;
    margin-bottom: 30px;
}

/* H2 - Section Headings */
h2 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.8rem, 5vw, 4rem);
    /* Tightened kerning */
    letter-spacing: -0.04em;
    /* Ultra-tight leading */
    line-height: 0.92;
    margin-bottom: 25px;
}

/* H3 - Subsection Headings */
h3 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.5rem, 4vw, 3.2rem);
    /* Tightened kerning */
    letter-spacing: -0.035em;
    /* Ultra-tight leading */
    line-height: 0.95;
    margin-bottom: 20px;
}

/* H4 - Minor Headings */
h4 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.3rem, 3vw, 2.5rem);
    /* Tightened kerning */
    letter-spacing: -0.03em;
    /* Ultra-tight leading */
    line-height: 0.98;
    margin-bottom: 15px;
}

/* H5 - Small Headings */
h5 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.1rem, 2.2vw, 2rem);
    /* Tightened kerning */
    letter-spacing: -0.025em;
    /* Ultra-tight leading */
    line-height: 1.0;
    margin-bottom: 15px;
}

/* H6 - Smallest Headings */
h6 {
    /* Responsive sizing with clamp */
    font-size: clamp(1rem, 1.5vw, 1.5rem);
    /* Tightened kerning */
    letter-spacing: -0.02em;
    /* Ultra-tight leading */
    line-height: 1.05;
    margin-bottom: 10px;
}

/* Special styling for hero headline */
.hero-content h1 {
    /* Ultra-tight leading for hero headline */
    line-height: 0.85;
    /* Extremely tight kerning for main hero headline */
    letter-spacing: -0.07em;
    /* Add word-spacing to balance the tight kerning */
    word-spacing: 0.1em;
    margin-bottom: 30px;
    margin-top: 0;
}

/* Body text styles */
body {
    font-family: 'Space Mono', monospace;
    line-height: 1.4; /* Tightened leading for body text */
}

/* Menu and button styles */
.nav-links a,
button,
.cta-button {
    font-family: 'Hanken Grotesk', sans-serif;
    font-weight: 500;
}

/* Paragraph styles */
p {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.2rem;
    line-height: 1.4; /* Tightened leading for paragraphs */
    font-weight: 400;
}

/* Menu font size adjustment */
@media (min-width: 769px) {
    .nav-links a {
        font-size: 0.775em; /* 2 points smaller */
    }
}

@media (max-width: 768px) {
    .nav-links a {
        font-size: 0.875em; /* 2 points smaller */
    }

    p {
        font-size: 1.1rem;
    }
}

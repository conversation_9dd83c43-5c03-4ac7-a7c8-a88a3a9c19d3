/*
==============================================
ARTCHOP CONSOLIDATED CSS
==============================================
*/

/*
1. BASIC RESET & GLOBAL STYLES
------------------------------
*/

/* Basic CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: #1a1a1a;
    color: #ffffff;
    scroll-behavior: smooth;
    overflow-x: hidden;
    scroll-padding-top: 80px; /* Adjust for fixed navbar */
    font-family: 'Space Mono', monospace;
    line-height: 1.4; /* Tightened leading for body text */
}

/*
2. TYPOGRAPHY
------------
*/

/* Global headline styles */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Syncopate', sans-serif;
    font-weight: 700;
    text-transform: uppercase;
}

/* H1 - Main Headings */
h1 {
    /* Responsive sizing with clamp */
    font-size: clamp(2.5rem, 7vw, 5.5rem);
    /* Tightened kerning */
    letter-spacing: -0.05em;
    /* Ultra-tight leading */
    line-height: 0.9;
    margin-bottom: 30px;
}

/* H2 - Section Headings */
h2 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.8rem, 5vw, 4rem);
    /* Tightened kerning */
    letter-spacing: -0.04em;
    /* Ultra-tight leading */
    line-height: 0.92;
    margin-bottom: 25px;
}

/* H3 - Subsection Headings */
h3 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.5rem, 4vw, 3.2rem);
    /* Tightened kerning */
    letter-spacing: -0.035em;
    /* Ultra-tight leading */
    line-height: 0.95;
    margin-bottom: 20px;
}

/* H4 - Minor Headings */
h4 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.3rem, 3vw, 2.5rem);
    /* Tightened kerning */
    letter-spacing: -0.03em;
    /* Ultra-tight leading */
    line-height: 0.98;
    margin-bottom: 15px;
}

/* H5 - Small Headings */
h5 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.1rem, 2.2vw, 2rem);
    /* Tightened kerning */
    letter-spacing: -0.025em;
    /* Ultra-tight leading */
    line-height: 1.0;
    margin-bottom: 15px;
}

/* H6 - Smallest Headings */
h6 {
    /* Responsive sizing with clamp */
    font-size: clamp(1rem, 1.5vw, 1.5rem);
    /* Tightened kerning */
    letter-spacing: -0.02em;
    /* Ultra-tight leading */
    line-height: 1.05;
    margin-bottom: 10px;
}

/* Special styling for hero headline */
.hero-content h1 {
    /* Ultra-tight leading for hero headline */
    line-height: 0.85;
    /* Extremely tight kerning for main hero headline */
    letter-spacing: -0.07em;
    /* Add word-spacing to balance the tight kerning */
    word-spacing: 0.1em;
    margin-bottom: 30px;
    margin-top: 0;
}

/* Menu and button styles */
.nav-links a,
button,
.cta-button {
    font-family: 'Hanken Grotesk', sans-serif;
    font-weight: 500;
}

/* Paragraph styles */
p {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.2rem;
    line-height: 1.4; /* Tightened leading for paragraphs */
    font-weight: 400;
}

/* Menu font size adjustment */
@media (min-width: 769px) {
    .nav-links a {
        font-size: 0.775em; /* 2 points smaller */
    }
}

@media (max-width: 768px) {
    .nav-links a {
        font-size: 0.875em; /* 2 points smaller */
    }

    p {
        font-size: 1.1rem;
    }
}

/*
3. LAYOUT & SECTIONS
-------------------
*/

/* Section Styles */
section {
    min-height: 100vh;
    height: 100vh;
    position: relative;
    overflow: hidden;
    padding: 100px 40px;
    scroll-margin-top: 80px; /* Match navbar height */
}

/* Non-hero sections should cover the hero section */
section:not(#hero) {
    background-color: inherit;
    position: relative;
    z-index: 5; /* Higher than hero but lower than specific section z-indexes */
}

/* Hero Section */
.hero-section {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 0;
    overflow: hidden;
    position: relative;
}

.hero-background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5); /* Dark tint layer */
    z-index: 1;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
}

.hero-background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transform-origin: center;
    transform: scale(1);
}

.hero-slide.active .hero-background-image {
    animation: scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards;
}

/* About Section */
.about-section {
    position: relative;
    overflow: hidden;
    z-index: 10; /* Higher z-index to appear in front of hero section */
    background-color: #f8f9fa;
    transform: translateZ(0); /* Force hardware acceleration */
    padding: 100px 40px;
}

.about-content {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    height: 100%;
}

.about-text {
    display: flex;
    flex-direction: column;
    gap: 30px;
    height: 100%;
    justify-content: center;
}

.about-text h2 {
    color: #2c3e50;
}

.about-text p {
    color: #34495e;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    width: 100%;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #3498db;
}

.stat-label {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    color: #7f8c8d;
    margin-top: 10px;
}

.about-image {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    height: 100%;
}

.about-image-main {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.about-image:hover .about-image-main {
    transform: scale(1.05);
}

.about-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
    transition: opacity 0.3s ease;
}

.about-image:hover .about-image-overlay {
    opacity: 0;
}

/* Gallery Section */
.gallery-section {
    padding: 100px 40px;
    background-color: #ffffff;
    position: relative;
    z-index: 11; /* Higher than about section */
    transform: translateZ(0); /* Force hardware acceleration */
}

.gallery-content {
    max-width: 1200px;
    margin: 0 auto;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    aspect-ratio: 1;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .about-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .about-text p {
        font-size: 1.1rem;
    }

    .about-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .about-section {
        padding: 60px 20px;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

/*
4. NAVIGATION
-----------
*/

nav {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 160px);
    max-width: 1200px;
    height: 44px; /* Decreased from 52px to 44px (additional 8px reduction) */
    background-color: #ffffff;
    z-index: 1000;
    padding: 0 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    outline: none;
    border: none;
    box-sizing: content-box;
    overflow: visible;
}

.navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
}

.hamburger-icon {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px; /* Reduced padding */
    z-index: 1001;
}

.hamburger-icon span {
    display: block;
    width: 22px; /* Slightly smaller width */
    height: 2px; /* Thinner lines */
    background-color: #000000;
    margin: 4px 0; /* Reduced margin */
    transition: all 0.3s ease-in-out;
}

.site-logo {
    height: 100%;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo-container {
    position: relative;
    padding: 12px; /* Further reduced from 16px to fit even smaller navbar */
}

.logo-image {
    position: absolute;
    top: 50%;
    left: 0;
    height: 30px; /* Increased from 26px to 30px (4px increase) */
    width: auto;
    transform: translateY(-50%);
    transition: clip-path 0.25s cubic-bezier(0.4, 0, 0.05, 1);
}

.logo-1 {
    clip-path: inset(0 0 0 0);
}

.logo-2 {
    clip-path: inset(0 100% 0 0);
}

.logo-container:hover .logo-1 {
    clip-path: inset(0 0 0 100%);
}

.logo-container:hover .logo-2 {
    clip-path: inset(0 0 0 0);
}

@media (min-width: 769px) {
    .nav-links a {
        color: #000000;
        text-decoration: none;
        position: relative;
        padding: 4px 8px;
        transition: color 0.25s cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover {
        color: #ffffff;
    }

    .nav-links a::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        background-color: #00A2FF;
        z-index: -1;
        transition: width 0.25s cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover::after {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .hamburger-icon {
        display: block;
    }

    .nav-links {
        display: none;
        position: fixed;
        top: 44px; /* Updated to match new navbar height */
        left: 0;
        right: 0;
        background-color: #ffffff;
        padding: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .nav-links.active {
        display: block;
    }

    .nav-links a {
        display: block;
        width: 100%;
        text-align: center;
        margin: 10px 0;
        padding: 8px 12px;
        color: #000000;
        text-decoration: none;
        position: relative;
        overflow: hidden;
        transition: color 0.25s cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover {
        color: #ffffff;
    }

    .nav-links a::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 100%;
        background-color: #00A2FF;
        z-index: -1;
        transform: translate(-50%, -50%);
        transition: width 0.25s cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover::after {
        width: 150px;
    }

    .hamburger-icon.active span:nth-child(1) {
        transform: translateY(6px) rotate(45deg); /* Adjusted for smaller spacing */
    }

    .hamburger-icon.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger-icon.active span:nth-child(3) {
        transform: translateY(-6px) rotate(-45deg); /* Adjusted for smaller spacing */
    }
}

/*
5. ANIMATIONS
-----------
*/

/* Hero image scale-up animation */
@keyframes scaleUpHero {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.25);
    }
}

/*
5.1 WORD ANIMATION CONTAINERS
---------------------------
*/

/* Container for word animations - preserves layout */
.word-wrap {
    position: relative;
    display: inline-block;
}

/* Clip container for revealing effect */
.word-clip {
    position: relative;
    display: inline-block;
    overflow: hidden;
    position: relative; /* Enable absolute positioning - fixes baseline */
}

/* Specific heights for different elements - tightens leading */
h1 .word-clip {
    height: 0.9em; /* Tighter leading for h1 */
}

.hero-content h1 .word-clip {
    height: 0.85em; /* Tighter leading for hero h1 */
}

h2 .word-clip {
    height: 0.92em; /* Tighter leading for h2 */
}

p .word-clip {
    height: 1.4em; /* Tighter leading for paragraphs */
}

/* Actual word that animates */
.word-inner {
    display: inline-block;
    position: absolute; /* Position absolutely - fixes baseline */
    bottom: 0; /* Align to bottom of mask - fixes baseline */
    left: 0;
    width: 100%;
    height: auto; /* Allow height to adjust to content */
    min-height: 1em; /* Ensure minimum height */
    transform: translateY(100%);
    animation: wordRise 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    opacity: 1; /* Ensure visibility */
}

/* Animation for words rising in place */
@keyframes wordRise {
    0% {
        transform: translateY(100%);
    }
    100% {
        transform: translateY(0);
    }
}

/*
5.2 ANIMATION DELAYS
-----------------
*/

/* Word animation delays */
.word-delay-1 { animation-delay: 0.05s; }
.word-delay-2 { animation-delay: 0.1s; }
.word-delay-3 { animation-delay: 0.15s; }
.word-delay-4 { animation-delay: 0.2s; }
.word-delay-5 { animation-delay: 0.25s; }
.word-delay-6 { animation-delay: 0.3s; }
.word-delay-7 { animation-delay: 0.35s; }
.word-delay-8 { animation-delay: 0.4s; }
.word-delay-9 { animation-delay: 0.45s; }
.word-delay-10 { animation-delay: 0.5s; }
.word-delay-11 { animation-delay: 0.55s; }
.word-delay-12 { animation-delay: 0.6s; }
.word-delay-13 { animation-delay: 0.65s; }
.word-delay-14 { animation-delay: 0.7s; }
.word-delay-15 { animation-delay: 0.75s; }
.word-delay-16 { animation-delay: 0.8s; }
.word-delay-17 { animation-delay: 0.85s; }
.word-delay-18 { animation-delay: 0.9s; }
.word-delay-19 { animation-delay: 0.95s; }
.word-delay-20 { animation-delay: 1.0s; }

/* Element animation delays */
.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.3s; }
.delay-3 { animation-delay: 0.5s; }
.delay-4 { animation-delay: 0.7s; }
.delay-5 { animation-delay: 0.9s; }

/*
5.3 SPECIAL ELEMENT ANIMATIONS
---------------------------
*/

/* For non-text elements like images */
.intro-element {
    opacity: 0;
    transform: translateY(30px);
    animation: elementRise 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    transform-origin: bottom center; /* Set transform origin to bottom */
}

/* Special handling for CTA button */
.cta-button.intro {
    overflow: hidden;
    position: relative;
    display: inline-block;
    color: transparent; /* Hide the original text */
}

.cta-button.intro::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateY(100%);
    animation: buttonRise 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    animation-delay: 0.5s;
    opacity: 1; /* Ensure visibility */
    color: inherit; /* Inherit text color */
    font-size: inherit; /* Inherit font size */
    font-weight: inherit; /* Inherit font weight */
    text-decoration: inherit; /* Inherit text decoration */
}

@keyframes buttonRise {
    0% {
        transform: translateY(100%);
    }
    100% {
        transform: translateY(0);
    }
}

/* Animation for elements rising in place */
@keyframes elementRise {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/*
5.4 IDLE ANIMATIONS
----------------
*/

/* Text shadow pulse animation */
.text-shadow-pulse {
    animation: textShadowPulse 8s infinite;
}

/* Different timing for each headline */
h1.text-shadow-pulse {
    animation-delay: 0s;
}

h2.text-shadow-pulse {
    animation-delay: -3s;
}

h3.text-shadow-pulse {
    animation-delay: -5s;
}

/* Text shadow animation */
@keyframes textShadowPulse {
    0%, 100% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }

    /* First glitch moment */
    2% {
        text-shadow: 1px 0 0 rgba(255, 0, 0, 0.6), -1px 0 0 rgba(0, 0, 255, 0.6);
    }
    3% {
        text-shadow: -1px 0 0 rgba(255, 0, 0, 0.6), 1px 0 0 rgba(0, 0, 255, 0.6);
    }
    4% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }

    /* Second glitch moment */
    30% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    30.5% {
        text-shadow: 2px 0 0 rgba(255, 0, 0, 0.8), -2px 0 0 rgba(0, 0, 255, 0.8);
    }
    30.7% {
        text-shadow: -2px 0 0 rgba(255, 0, 0, 0.8), 2px 0 0 rgba(0, 0, 255, 0.8);
    }
    31% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }

    /* Third glitch moment */
    58% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    58.2% {
        text-shadow: 1px 0 0 rgba(255, 0, 0, 0.7), -1px 0 0 rgba(0, 0, 255, 0.7);
    }
    58.4% {
        text-shadow: -1px 0 0 rgba(255, 0, 0, 0.7), 1px 0 0 rgba(0, 0, 255, 0.7);
    }
    58.6% {
        text-shadow: 1px 0 0 rgba(255, 0, 0, 0.7), -1px 0 0 rgba(0, 0, 255, 0.7);
    }
    58.8% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }

    /* Fourth glitch moment */
    80% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    80.5% {
        text-shadow: -1px 0 0 rgba(255, 0, 0, 0.6), 1px 0 0 rgba(0, 0, 255, 0.6);
    }
    81% {
        text-shadow: 1px 0 0 rgba(255, 0, 0, 0.6), -1px 0 0 rgba(0, 0, 255, 0.6);
    }
    81.5% {
        text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
}

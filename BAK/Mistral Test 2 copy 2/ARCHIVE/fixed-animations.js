/**
 * Fixed Animation System
 * Word-by-word animations that rise in place without changing positions
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('Fixed animations initialized');

    // Keep idle-target class for idle animations

    // Initialize the word-by-word animations
    setupWordAnimations();

    // Log all intro elements to debug
    console.log('All intro elements:', document.querySelectorAll('.intro'));
});

/**
 * Set up word-by-word animations that preserve layout
 */
function setupWordAnimations() {
    // Get all intro elements
    const introElements = document.querySelectorAll('.intro');
    console.log('Found intro elements:', introElements.length);

    introElements.forEach((element, elementIndex) => {
        console.log('Processing element:', element, 'Index:', elementIndex);

        // Skip elements that are already processed
        if (element.classList.contains('processed')) {
            console.log('Element already processed, skipping:', element);
            return;
        }

        // Mark as processed to avoid double-processing
        element.classList.add('processed');

        // Special handling for buttons, images, and other non-text elements
        if (element.tagName === 'A' && element.classList.contains('cta-button')) {
            // Skip CTA buttons - they're handled by CSS now
            // Make sure they have the data-text attribute
            if (!element.hasAttribute('data-text')) {
                element.setAttribute('data-text', element.textContent);
            }
            return;
        }

        if (element.tagName === 'BUTTON' ||
            element.tagName === 'IMG' || element.querySelector('img') ||
            element.classList.contains('no-word-animation')) {

            // Add intro-element class for simple rise animation
            element.classList.add('intro-element');

            // Add appropriate delay class based on element index
            const delayClass = `delay-${Math.min(elementIndex % 5 + 1, 5)}`;
            if (!element.classList.contains(delayClass)) {
                element.classList.add(delayClass);
            }
            return;
        }

        // Get the HTML content
        const originalHTML = element.innerHTML;

        // Create a temporary element to work with the content
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = originalHTML;

        // Process text nodes to wrap words
        processTextNodes(tempDiv);

        // Replace the original content with the processed content
        element.innerHTML = tempDiv.innerHTML;
    });
}

/**
 * Process text nodes to wrap words for animation
 * @param {HTMLElement} element - The element to process
 */
function processTextNodes(element) {
    console.log('Processing text nodes for element:', element);

    // Create a TreeWalker to find all text nodes
    const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    // Array to store text nodes that need to be replaced
    const nodesToReplace = [];

    // Find all text nodes
    let node;
    while (node = walker.nextNode()) {
        console.log('Found text node:', node, 'Value:', node.nodeValue);

        // Skip empty text nodes
        if (node.nodeValue.trim() === '') {
            console.log('Skipping empty text node');
            continue;
        }

        // Add to the list of nodes to replace
        nodesToReplace.push(node);
    }

    console.log('Total text nodes to replace:', nodesToReplace.length);

    // Process each text node
    let wordIndex = 0;
    nodesToReplace.forEach(textNode => {
        // Split the text into words
        const words = textNode.nodeValue.split(/(\s+)/);

        // Create a document fragment to hold the wrapped words
        const fragment = document.createDocumentFragment();

        // Process each word and space
        words.forEach(word => {
            if (word.trim() === '') {
                // It's whitespace, preserve it
                fragment.appendChild(document.createTextNode(word));
            } else {
                // It's a word, wrap it for animation
                const wordWrap = document.createElement('span');
                wordWrap.className = 'word-wrap';

                const wordClip = document.createElement('span');
                wordClip.className = 'word-clip';

                const wordInner = document.createElement('span');
                wordInner.className = 'word-inner';
                wordInner.textContent = word;

                // Add delay class based on word index
                const delayClass = `word-delay-${Math.min(wordIndex + 1, 20)}`;
                wordInner.classList.add(delayClass);

                // Assemble the word structure
                wordClip.appendChild(wordInner);
                wordWrap.appendChild(wordClip);
                fragment.appendChild(wordWrap);

                // Increment word index for staggered animation
                wordIndex++;
            }
        });

        // Replace the text node with the fragment
        textNode.parentNode.replaceChild(fragment, textNode);
    });
}

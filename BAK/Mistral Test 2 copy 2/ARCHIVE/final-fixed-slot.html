<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Fixed Slot Machine</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #222;
            color: white;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        button.active {
            background-color: #2196F3;
        }
        
        .slot-machine {
            width: 300px;
            height: 300px;
            margin: 0 auto;
            background-color: #333;
            border: 10px solid gold;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            position: relative;
            overflow: hidden;
        }
        
        /* Slot window - where symbols are visible */
        .slot-window {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        /* Slot reel - the strip of symbols */
        .slot-reel {
            position: absolute;
            width: 100%;
            height: 1000%;
            top: 0;
            left: 0;
        }
        
        /* Horizontal reel */
        .horizontal-reel {
            position: absolute;
            width: 1000%;
            height: 100%;
            top: 0;
            left: 0;
            display: flex;
        }
        
        /* Individual symbol */
        .symbol {
            width: 100%;
            height: 10%;
            background-size: cover;
            background-position: center;
        }
        
        /* Horizontal symbol */
        .horizontal-symbol {
            width: 10%;
            height: 100%;
            flex-shrink: 0;
            background-size: cover;
            background-position: center;
        }
        
        /* VERTICAL VARIABLE SPEED - speeds up, does revolutions, then slows down */
        .vertical-variable {
            animation: verticalScrollVariable 4s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
        }
        
        @keyframes verticalScrollVariable {
            0% { transform: translateY(0%); }
            100% { transform: translateY(-90%); } /* Move by exactly 9 symbols */
        }
        
        /* HORIZONTAL VARIABLE SPEED - speeds up, does revolutions, then slows down */
        @keyframes horizontalScrollVariable {
            0% { transform: translateX(0%); }
            100% { transform: translateX(-90%); } /* Move by exactly 9 symbols */
        }
        
        /* Constant speed animations are handled by JavaScript */
    </style>
</head>
<body>
    <h1>Final Fixed Slot Machine</h1>
    
    <div class="controls">
        <button id="btn-vertical-variable" class="active">Vertical Variable Speed</button>
        <button id="btn-vertical-constant">Vertical Constant Speed</button>
        <button id="btn-horizontal-variable">Horizontal Variable Speed</button>
        <button id="btn-horizontal-constant">Horizontal Constant Speed</button>
        <button id="btn-upload">Upload Image</button>
    </div>
    
    <input type="file" id="image-upload" accept="image/*" style="display: none;">
    
    <div class="slot-machine">
        <div class="slot-window" id="slot-window">
            <!-- Content will be generated by JavaScript -->
        </div>
    </div>
    
    <script>
        // Get elements
        const slotWindow = document.getElementById('slot-window');
        const btnVerticalVariable = document.getElementById('btn-vertical-variable');
        const btnVerticalConstant = document.getElementById('btn-vertical-constant');
        const btnHorizontalVariable = document.getElementById('btn-horizontal-variable');
        const btnHorizontalConstant = document.getElementById('btn-horizontal-constant');
        const btnUpload = document.getElementById('btn-upload');
        const imageUpload = document.getElementById('image-upload');
        
        // Default image URL
        let currentImageUrl = 'https://images.unsplash.com/photo-1682687982501-1e58ab814714';
        
        // Animation IDs for constant speed animations
        let verticalConstantAnimationId = null;
        let horizontalConstantAnimationId = null;
        
        // Initialize with vertical variable speed
        createVerticalReel('variable');
        
        // Button event listeners
        btnVerticalVariable.addEventListener('click', function() {
            setActiveButton(this);
            stopAllConstantAnimations();
            createVerticalReel('variable');
        });
        
        btnVerticalConstant.addEventListener('click', function() {
            setActiveButton(this);
            stopAllConstantAnimations();
            createVerticalReel('constant');
        });
        
        btnHorizontalVariable.addEventListener('click', function() {
            setActiveButton(this);
            stopAllConstantAnimations();
            createHorizontalReel('variable');
        });
        
        btnHorizontalConstant.addEventListener('click', function() {
            setActiveButton(this);
            stopAllConstantAnimations();
            createHorizontalReel('constant');
        });
        
        // Image upload handling
        btnUpload.addEventListener('click', function() {
            imageUpload.click();
        });
        
        imageUpload.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    currentImageUrl = e.target.result;
                    // Refresh current animation
                    stopAllConstantAnimations();
                    if (btnVerticalVariable.classList.contains('active')) {
                        createVerticalReel('variable');
                    } else if (btnVerticalConstant.classList.contains('active')) {
                        createVerticalReel('constant');
                    } else if (btnHorizontalVariable.classList.contains('active')) {
                        createHorizontalReel('variable');
                    } else {
                        createHorizontalReel('constant');
                    }
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Helper function to set active button
        function setActiveButton(activeButton) {
            const buttons = document.querySelectorAll('.controls button');
            buttons.forEach(btn => btn.classList.remove('active'));
            activeButton.classList.add('active');
        }
        
        // Stop all constant animations
        function stopAllConstantAnimations() {
            if (verticalConstantAnimationId) {
                cancelAnimationFrame(verticalConstantAnimationId);
                verticalConstantAnimationId = null;
            }
            if (horizontalConstantAnimationId) {
                cancelAnimationFrame(horizontalConstantAnimationId);
                horizontalConstantAnimationId = null;
            }
        }
        
        // Create vertical reel
        function createVerticalReel(speedType) {
            if (speedType === 'variable') {
                // Use CSS animation for variable speed (already working well)
                const reel = document.createElement('div');
                reel.className = 'slot-reel vertical-variable';
                
                // Create 10 identical symbols
                for (let i = 0; i < 10; i++) {
                    const symbol = document.createElement('div');
                    symbol.className = 'symbol';
                    symbol.style.backgroundImage = `url('${currentImageUrl}')`;
                    reel.appendChild(symbol);
                }
                
                // Clear and add to slot window
                slotWindow.innerHTML = '';
                slotWindow.appendChild(reel);
            } else {
                // Create two reels for constant speed to ensure no empty space
                const container = document.createElement('div');
                container.style.position = 'relative';
                container.style.width = '100%';
                container.style.height = '100%';
                container.style.overflow = 'hidden';
                
                // First reel
                const reel1 = document.createElement('div');
                reel1.className = 'slot-reel';
                reel1.id = 'vertical-constant-reel1';
                reel1.style.top = '0';
                
                // Second reel (positioned right after the first one)
                const reel2 = document.createElement('div');
                reel2.className = 'slot-reel';
                reel2.id = 'vertical-constant-reel2';
                reel2.style.top = '-100%';
                
                // Create symbols for both reels
                for (let i = 0; i < 10; i++) {
                    const symbol1 = document.createElement('div');
                    symbol1.className = 'symbol';
                    symbol1.style.backgroundImage = `url('${currentImageUrl}')`;
                    reel1.appendChild(symbol1);
                    
                    const symbol2 = document.createElement('div');
                    symbol2.className = 'symbol';
                    symbol2.style.backgroundImage = `url('${currentImageUrl}')`;
                    reel2.appendChild(symbol2);
                }
                
                // Add reels to container
                container.appendChild(reel1);
                container.appendChild(reel2);
                
                // Clear and add to slot window
                slotWindow.innerHTML = '';
                slotWindow.appendChild(container);
                
                // Start constant speed animation
                animateVerticalConstant();
            }
        }
        
        // Create horizontal reel
        function createHorizontalReel(speedType) {
            if (speedType === 'variable') {
                // Use CSS animation for variable speed (already working well)
                const reel = document.createElement('div');
                reel.className = 'horizontal-reel';
                reel.style.animation = 'horizontalScrollVariable 4s cubic-bezier(0.25, 0.1, 0.25, 1) infinite';
                
                // Create 10 identical symbols
                for (let i = 0; i < 10; i++) {
                    const symbol = document.createElement('div');
                    symbol.className = 'horizontal-symbol';
                    symbol.style.backgroundImage = `url('${currentImageUrl}')`;
                    reel.appendChild(symbol);
                }
                
                // Clear and add to slot window
                slotWindow.innerHTML = '';
                slotWindow.appendChild(reel);
            } else {
                // Create two reels for constant speed to ensure no empty space
                const container = document.createElement('div');
                container.style.position = 'relative';
                container.style.width = '100%';
                container.style.height = '100%';
                container.style.overflow = 'hidden';
                
                // First reel
                const reel1 = document.createElement('div');
                reel1.className = 'horizontal-reel';
                reel1.id = 'horizontal-constant-reel1';
                reel1.style.left = '0';
                
                // Second reel (positioned right after the first one)
                const reel2 = document.createElement('div');
                reel2.className = 'horizontal-reel';
                reel2.id = 'horizontal-constant-reel2';
                reel2.style.left = '-100%';
                
                // Create symbols for both reels
                for (let i = 0; i < 10; i++) {
                    const symbol1 = document.createElement('div');
                    symbol1.className = 'horizontal-symbol';
                    symbol1.style.backgroundImage = `url('${currentImageUrl}')`;
                    reel1.appendChild(symbol1);
                    
                    const symbol2 = document.createElement('div');
                    symbol2.className = 'horizontal-symbol';
                    symbol2.style.backgroundImage = `url('${currentImageUrl}')`;
                    reel2.appendChild(symbol2);
                }
                
                // Add reels to container
                container.appendChild(reel1);
                container.appendChild(reel2);
                
                // Clear and add to slot window
                slotWindow.innerHTML = '';
                slotWindow.appendChild(container);
                
                // Start constant speed animation
                animateHorizontalConstant();
            }
        }
        
        // Animate vertical constant speed
        function animateVerticalConstant() {
            const reel1 = document.getElementById('vertical-constant-reel1');
            const reel2 = document.getElementById('vertical-constant-reel2');
            
            if (!reel1 || !reel2) return;
            
            let position1 = 0;
            let position2 = -100;
            const speed = 0.5; // Adjust speed as needed
            
            function animate() {
                // Move both reels downward
                position1 += speed;
                position2 += speed;
                
                // Reset positions when a reel moves completely out of view
                if (position1 >= 100) {
                    position1 = -100;
                }
                if (position2 >= 100) {
                    position2 = -100;
                }
                
                // Apply positions
                reel1.style.top = position1 + '%';
                reel2.style.top = position2 + '%';
                
                // Continue animation
                verticalConstantAnimationId = requestAnimationFrame(animate);
            }
            
            // Start animation
            verticalConstantAnimationId = requestAnimationFrame(animate);
        }
        
        // Animate horizontal constant speed
        function animateHorizontalConstant() {
            const reel1 = document.getElementById('horizontal-constant-reel1');
            const reel2 = document.getElementById('horizontal-constant-reel2');
            
            if (!reel1 || !reel2) return;
            
            let position1 = 0;
            let position2 = -100;
            const speed = 0.5; // Adjust speed as needed
            
            function animate() {
                // Move both reels rightward
                position1 += speed;
                position2 += speed;
                
                // Reset positions when a reel moves completely out of view
                if (position1 >= 100) {
                    position1 = -100;
                }
                if (position2 >= 100) {
                    position2 = -100;
                }
                
                // Apply positions
                reel1.style.left = position1 + '%';
                reel2.style.left = position2 + '%';
                
                // Continue animation
                horizontalConstantAnimationId = requestAnimationFrame(animate);
            }
            
            // Start animation
            horizontalConstantAnimationId = requestAnimationFrame(animate);
        }
    </script>
</body>
</html>

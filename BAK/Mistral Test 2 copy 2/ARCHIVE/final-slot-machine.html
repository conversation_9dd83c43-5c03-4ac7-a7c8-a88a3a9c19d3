<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Slot Machine Animation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #222;
            color: white;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        button.active {
            background-color: #2196F3;
        }
        
        .slot-machine {
            width: 300px;
            height: 300px;
            margin: 0 auto;
            background-color: #333;
            border: 10px solid gold;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            position: relative;
            overflow: hidden;
        }
        
        /* Slot window - where symbols are visible */
        .slot-window {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        /* Slot reel - the strip of symbols */
        .slot-reel {
            position: absolute;
            width: 100%;
            height: 500%;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            flex-wrap: nowrap;
        }
        
        /* Horizontal reel */
        .horizontal-reel {
            position: absolute;
            width: 500%;
            height: 100%;
            top: 0;
            left: 0;
            display: flex;
            flex-wrap: nowrap;
        }
        
        /* Individual symbol */
        .symbol {
            width: 100%;
            height: 100%;
            flex: 0 0 20%;
            background-size: cover;
            background-position: center;
        }
        
        /* Horizontal symbol */
        .horizontal-symbol {
            width: 100%;
            height: 100%;
            flex: 0 0 20%;
            background-size: cover;
            background-position: center;
        }
    </style>
</head>
<body>
    <h1>Final Slot Machine Animation</h1>
    
    <div class="controls">
        <button id="btn-vertical-variable" class="active">Vertical Variable Speed</button>
        <button id="btn-vertical-constant">Vertical Constant Speed</button>
        <button id="btn-horizontal-variable">Horizontal Variable Speed</button>
        <button id="btn-horizontal-constant">Horizontal Constant Speed</button>
        <button id="btn-upload">Upload Image</button>
    </div>
    
    <input type="file" id="image-upload" accept="image/*" style="display: none;">
    
    <div class="slot-machine">
        <div class="slot-window" id="slot-window">
            <!-- Content will be generated by JavaScript -->
        </div>
    </div>
    
    <script>
        // Get elements
        const slotWindow = document.getElementById('slot-window');
        const btnVerticalVariable = document.getElementById('btn-vertical-variable');
        const btnVerticalConstant = document.getElementById('btn-vertical-constant');
        const btnHorizontalVariable = document.getElementById('btn-horizontal-variable');
        const btnHorizontalConstant = document.getElementById('btn-horizontal-constant');
        const btnUpload = document.getElementById('btn-upload');
        const imageUpload = document.getElementById('image-upload');
        
        // Default image URL
        let currentImageUrl = 'https://images.unsplash.com/photo-1682687982501-1e58ab814714';
        
        // Initialize with vertical variable speed
        createVerticalReel('variable');
        
        // Button event listeners
        btnVerticalVariable.addEventListener('click', function() {
            setActiveButton(this);
            createVerticalReel('variable');
        });
        
        btnVerticalConstant.addEventListener('click', function() {
            setActiveButton(this);
            createVerticalReel('constant');
        });
        
        btnHorizontalVariable.addEventListener('click', function() {
            setActiveButton(this);
            createHorizontalReel('variable');
        });
        
        btnHorizontalConstant.addEventListener('click', function() {
            setActiveButton(this);
            createHorizontalReel('constant');
        });
        
        // Image upload handling
        btnUpload.addEventListener('click', function() {
            imageUpload.click();
        });
        
        imageUpload.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    currentImageUrl = e.target.result;
                    // Refresh current animation
                    if (btnVerticalVariable.classList.contains('active')) {
                        createVerticalReel('variable');
                    } else if (btnVerticalConstant.classList.contains('active')) {
                        createVerticalReel('constant');
                    } else if (btnHorizontalVariable.classList.contains('active')) {
                        createHorizontalReel('variable');
                    } else {
                        createHorizontalReel('constant');
                    }
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Helper function to set active button
        function setActiveButton(activeButton) {
            const buttons = document.querySelectorAll('.controls button');
            buttons.forEach(btn => btn.classList.remove('active'));
            activeButton.classList.add('active');
        }
        
        // Create vertical reel
        function createVerticalReel(speedType) {
            // Create reel
            const reel = document.createElement('div');
            reel.className = 'slot-reel';
            
            // Create 5 identical symbols
            for (let i = 0; i < 5; i++) {
                const symbol = document.createElement('div');
                symbol.className = 'symbol';
                symbol.style.backgroundImage = `url('${currentImageUrl}')`;
                reel.appendChild(symbol);
            }
            
            // Clear and add to slot window
            slotWindow.innerHTML = '';
            slotWindow.appendChild(reel);
            
            // Apply animation based on speed type
            if (speedType === 'variable') {
                // Variable speed - starts slow, speeds up, then slows down
                animateElement(reel, 'translateY', 0, -80, '%', 6, 'cubic-bezier(0.25, 0.1, 0.25, 1)');
            } else {
                // Constant speed - opposite direction
                animateElement(reel, 'translateY', 0, 80, '%', 7, 'linear');
            }
        }
        
        // Create horizontal reel
        function createHorizontalReel(speedType) {
            // Create reel
            const reel = document.createElement('div');
            reel.className = 'horizontal-reel';
            
            // Create 5 identical symbols
            for (let i = 0; i < 5; i++) {
                const symbol = document.createElement('div');
                symbol.className = 'horizontal-symbol';
                symbol.style.backgroundImage = `url('${currentImageUrl}')`;
                reel.appendChild(symbol);
            }
            
            // Clear and add to slot window
            slotWindow.innerHTML = '';
            slotWindow.appendChild(reel);
            
            // Apply animation based on speed type
            if (speedType === 'variable') {
                // Variable speed - starts slow, speeds up, then slows down
                animateElement(reel, 'translateX', 0, -80, '%', 6, 'cubic-bezier(0.25, 0.1, 0.25, 1)');
            } else {
                // Constant speed - opposite direction
                animateElement(reel, 'translateX', 0, 80, '%', 7, 'linear');
            }
        }
        
        // Helper function to animate an element with JavaScript
        function animateElement(element, property, start, end, unit, duration, timingFunction) {
            // Set initial position
            element.style.transform = `${property}(${start}${unit})`;
            
            // Animation variables
            const startTime = performance.now();
            const totalFrames = duration * 60; // 60fps
            let frame = 0;
            
            // Animation timing functions
            const timingFunctions = {
                'linear': t => t,
                'cubic-bezier(0.25, 0.1, 0.25, 1)': t => {
                    // Approximation of cubic-bezier(0.25, 0.1, 0.25, 1)
                    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
                }
            };
            
            // Animation loop
            function animate() {
                // Calculate progress (0 to 1)
                frame++;
                const linearProgress = frame / totalFrames;
                
                // Apply timing function
                const progress = timingFunctions[timingFunction](linearProgress % 1);
                
                // Calculate current position
                const current = start + (end - start) * progress;
                
                // Apply transform
                element.style.transform = `${property}(${current}${unit})`;
                
                // Continue animation
                if (frame <= totalFrames) {
                    requestAnimationFrame(animate);
                } else {
                    // Reset and start again for infinite loop
                    frame = 0;
                    requestAnimationFrame(animate);
                }
            }
            
            // Start animation
            requestAnimationFrame(animate);
        }
    </script>
</body>
</html>

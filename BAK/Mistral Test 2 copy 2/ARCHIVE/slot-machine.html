<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slot Machine Animation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #222;
            color: white;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
        }
        
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        button.active {
            background-color: #2196F3;
        }
        
        .slot-machine {
            width: 300px;
            height: 300px;
            margin: 0 auto;
            background-color: #333;
            border: 10px solid #gold;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            position: relative;
            overflow: hidden;
        }
        
        /* Slot window - where symbols are visible */
        .slot-window {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        /* Slot reel - the strip of symbols */
        .slot-reel {
            position: absolute;
            width: 100%;
            /* Height is much taller than the window to contain multiple symbols */
            height: 1000%;
            top: 0;
            left: 0;
            /* Default animation - will be overridden by JS */
            animation: spin-vertical 5s linear infinite;
        }
        
        /* Horizontal reel */
        .horizontal-reel {
            width: 1000%;
            height: 100%;
            display: flex;
            animation: spin-horizontal 5s linear infinite;
        }
        
        /* Individual symbol */
        .symbol {
            width: 100%;
            height: 20%;
            background-size: cover;
            background-position: center;
        }
        
        /* Horizontal symbol */
        .horizontal-symbol {
            width: 20%;
            height: 100%;
            flex-shrink: 0;
            background-size: cover;
            background-position: center;
        }
        
        /* Basic vertical spin - continuous motion */
        @keyframes spin-vertical {
            0% { transform: translateY(0); }
            100% { transform: translateY(20%); } /* Move by one symbol height */
        }
        
        /* Basic horizontal spin - continuous motion */
        @keyframes spin-horizontal {
            0% { transform: translateX(0); }
            100% { transform: translateX(-20%); } /* Move by one symbol width */
        }
        
        /* Variable speed vertical spin */
        @keyframes spin-vertical-variable {
            0% { transform: translateY(0); animation-timing-function: ease-in; }
            30% { transform: translateY(60%); animation-timing-function: linear; }
            70% { transform: translateY(140%); animation-timing-function: ease-out; }
            100% { transform: translateY(200%); }
        }
        
        /* Constant speed vertical spin */
        @keyframes spin-vertical-constant {
            0% { transform: translateY(0); }
            100% { transform: translateY(100%); }
        }
        
        /* Variable speed horizontal spin */
        @keyframes spin-horizontal-variable {
            0% { transform: translateX(0); animation-timing-function: ease-in; }
            30% { transform: translateX(-60%); animation-timing-function: linear; }
            70% { transform: translateX(-140%); animation-timing-function: ease-out; }
            100% { transform: translateX(-200%); }
        }
        
        /* Constant speed horizontal spin */
        @keyframes spin-horizontal-constant {
            0% { transform: translateX(0); }
            100% { transform: translateX(-100%); }
        }
    </style>
</head>
<body>
    <h1>Slot Machine Animation</h1>
    
    <div class="controls">
        <button id="btn-vertical-variable" class="active">Vertical Variable</button>
        <button id="btn-vertical-constant">Vertical Constant</button>
        <button id="btn-horizontal-variable">Horizontal Variable</button>
        <button id="btn-horizontal-constant">Horizontal Constant</button>
        <button id="btn-upload">Upload Image</button>
    </div>
    
    <input type="file" id="image-upload" accept="image/*" style="display: none;">
    
    <div class="slot-machine">
        <div class="slot-window" id="slot-window">
            <!-- Content will be generated by JavaScript -->
        </div>
    </div>
    
    <script>
        // Get elements
        const slotWindow = document.getElementById('slot-window');
        const btnVerticalVariable = document.getElementById('btn-vertical-variable');
        const btnVerticalConstant = document.getElementById('btn-vertical-constant');
        const btnHorizontalVariable = document.getElementById('btn-horizontal-variable');
        const btnHorizontalConstant = document.getElementById('btn-horizontal-constant');
        const btnUpload = document.getElementById('btn-upload');
        const imageUpload = document.getElementById('image-upload');
        
        // Default image URL
        let currentImageUrl = 'https://images.unsplash.com/photo-1682687982501-1e58ab814714';
        
        // Initialize with vertical variable speed
        createVerticalReel('variable');
        
        // Button event listeners
        btnVerticalVariable.addEventListener('click', function() {
            setActiveButton(this);
            createVerticalReel('variable');
        });
        
        btnVerticalConstant.addEventListener('click', function() {
            setActiveButton(this);
            createVerticalReel('constant');
        });
        
        btnHorizontalVariable.addEventListener('click', function() {
            setActiveButton(this);
            createHorizontalReel('variable');
        });
        
        btnHorizontalConstant.addEventListener('click', function() {
            setActiveButton(this);
            createHorizontalReel('constant');
        });
        
        // Image upload handling
        btnUpload.addEventListener('click', function() {
            imageUpload.click();
        });
        
        imageUpload.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    currentImageUrl = e.target.result;
                    // Refresh current animation
                    if (btnVerticalVariable.classList.contains('active')) {
                        createVerticalReel('variable');
                    } else if (btnVerticalConstant.classList.contains('active')) {
                        createVerticalReel('constant');
                    } else if (btnHorizontalVariable.classList.contains('active')) {
                        createHorizontalReel('variable');
                    } else {
                        createHorizontalReel('constant');
                    }
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Helper function to set active button
        function setActiveButton(activeButton) {
            const buttons = document.querySelectorAll('.controls button');
            buttons.forEach(btn => btn.classList.remove('active'));
            activeButton.classList.add('active');
        }
        
        // Create vertical reel
        function createVerticalReel(speedType) {
            // Create reel with 5 copies of the image
            const reel = document.createElement('div');
            reel.className = 'slot-reel';
            
            // Add animation based on speed type
            if (speedType === 'variable') {
                reel.style.animation = 'spin-vertical-variable 8s infinite';
            } else {
                reel.style.animation = 'spin-vertical-constant 10s linear infinite';
            }
            
            // Create 10 symbols (2 complete sets to ensure seamless looping)
            for (let i = 0; i < 10; i++) {
                const symbol = document.createElement('div');
                symbol.className = 'symbol';
                symbol.style.backgroundImage = `url('${currentImageUrl}')`;
                reel.appendChild(symbol);
            }
            
            // Clear and add to slot window
            slotWindow.innerHTML = '';
            slotWindow.appendChild(reel);
        }
        
        // Create horizontal reel
        function createHorizontalReel(speedType) {
            // Create reel with 5 copies of the image
            const reel = document.createElement('div');
            reel.className = 'horizontal-reel';
            
            // Add animation based on speed type
            if (speedType === 'variable') {
                reel.style.animation = 'spin-horizontal-variable 8s infinite';
            } else {
                reel.style.animation = 'spin-horizontal-constant 10s linear infinite';
            }
            
            // Create 10 symbols (2 complete sets to ensure seamless looping)
            for (let i = 0; i < 10; i++) {
                const symbol = document.createElement('div');
                symbol.className = 'horizontal-symbol';
                symbol.style.backgroundImage = `url('${currentImageUrl}')`;
                reel.appendChild(symbol);
            }
            
            // Clear and add to slot window
            slotWindow.innerHTML = '';
            slotWindow.appendChild(reel);
        }
    </script>
</body>
</html>

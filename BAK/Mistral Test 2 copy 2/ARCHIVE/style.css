/* Basic CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Typography styles moved to typography.css */

body {
    /* <PERSON><PERSON> family moved to typography.css */
    background-color: #1a1a1a;
    color: #ffffff;
    scroll-behavior: smooth;
    overflow-x: hidden;
    scroll-padding-top: 80px; /* Adjust for fixed navbar */
}

/* Section Styles */
section {
    min-height: 100vh;
    height: 100vh;
    position: relative;
    overflow: hidden;
    padding: 100px 40px;
    scroll-margin-top: 80px; /* Match navbar height */
}

/* Non-hero sections should cover the hero section */
section:not(#hero) {
    background-color: inherit;
    position: relative;
    z-index: 5; /* Higher than hero but lower than specific section z-indexes */
}

/* Hero Section */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

/* About Section */
.about-section {
    position: relative;
    overflow: hidden;
    z-index: 10; /* Higher z-index to appear in front of hero section */
    background-color: #f8f9fa;
    transform: translateZ(0); /* Force hardware acceleration */
}

.about-image {
    position: relative;
    z-index: 1;
}

.about-image-main {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

/* About Section Styles */
.about-section {
    padding: 100px 40px;
}

/* Gallery Section Styles */
.gallery-section {
    padding: 100px 40px;
    background-color: #ffffff;
    position: relative;
    z-index: 11; /* Higher than about section */
    transform: translateZ(0); /* Force hardware acceleration */
}

.gallery-content {
    max-width: 1200px;
    margin: 0 auto;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    aspect-ratio: 1;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

@media (max-width: 768px) {
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

.about-content {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    height: 100%;
}

.about-text {
    display: flex;
    flex-direction: column;
    gap: 30px;
    height: 100%;
    justify-content: center;
}

.about-text h2 {
    /* Inherits size from global h2 styles */
    color: #2c3e50;
}

.about-text p {
    /* Font family moved to typography.css */
    color: #34495e;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    width: 100%;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-family: 'Hanken Grotesk', sans-serif; /* Using Hanken Grotesk as substitute for Armin Grotesk */
    font-size: 2.5rem;
    font-weight: 700;
    color: #3498db;
}

.stat-label {
    font-family: 'Hanken Grotesk', sans-serif; /* Using Hanken Grotesk as substitute for Armin Grotesk */
    font-size: 1rem;
    font-weight: 500;
    color: #7f8c8d;
    margin-top: 10px;
}

.about-image {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    height: 100%;
}

.about-image-main {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.about-image:hover .about-image-main {
    transform: scale(1.05);
}

.about-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
    transition: opacity 0.3s ease;
}

.about-image:hover .about-image-overlay {
    opacity: 0;
}

@media (max-width: 992px) {
    .about-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    /* About text h2 now uses global responsive sizing */

    .about-text p {
        font-size: 1.1rem;
    }

    .about-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .about-section {
        padding: 60px 20px;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }
}

nav {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 160px);
    max-width: 1200px;
    height: 44px; /* Decreased from 52px to 44px (additional 8px reduction) */
    background-color: #ffffff;
    z-index: 1000;
    padding: 0 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    outline: none;
    border: none;
    box-sizing: content-box;
    overflow: visible;
}

.navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
}

.hamburger-icon {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px; /* Reduced padding */
    z-index: 1001;
}

.hamburger-icon span {
    display: block;
    width: 22px; /* Slightly smaller width */
    height: 2px; /* Thinner lines */
    background-color: #000000;
    margin: 4px 0; /* Reduced margin */
    transition: all 0.3s ease-in-out;
}

@media (max-width: 768px) {
    .hamburger-icon {
        display: block;
    }

    .nav-links {
        display: none;
        position: fixed;
        top: 44px; /* Updated to match new navbar height */
        left: 0;
        right: 0;
        background-color: #ffffff;
        padding: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .nav-links.active {
        display: block;
    }

    .nav-links a {
        display: block;
        width: 100%;
        text-align: center;
        margin: 10px 0;
        padding: 8px 12px;
        color: #000000;
        text-decoration: none;
        font-size: 1em;
        position: relative;
        overflow: hidden;
        transition: color 0.25s cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover {
        color: #ffffff;
    }



    .hamburger-icon.active span:nth-child(1) {
        transform: translateY(6px) rotate(45deg); /* Adjusted for smaller spacing */
    }

    .hamburger-icon.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger-icon.active span:nth-child(3) {
        transform: translateY(-6px) rotate(-45deg); /* Adjusted for smaller spacing */
    }
}

.site-logo {
    height: 100%;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo-container {
    position: relative;
    padding: 12px; /* Further reduced from 16px to fit even smaller navbar */
}

.logo-image {
    position: absolute;
    top: 50%;
    left: 0;
    height: 30px; /* Increased from 26px to 30px (4px increase) */
    width: auto;
    transform: translateY(-50%);
    transition: clip-path 0.25s cubic-bezier(0.4, 0, 0.05, 1);
}

.logo-1 {
    clip-path: inset(0 0 0 0);
}

.logo-2 {
    clip-path: inset(0 100% 0 0);
}

.logo-container:hover .logo-1 {
    clip-path: inset(0 0 0 100%);
}

.logo-container:hover .logo-2 {
    clip-path: inset(0 0 0 0);
}

@media (min-width: 769px) {
    .nav-links a {
        color: #000000;
        text-decoration: none;
        font-size: 0.775em; /* Decreased by approximately 2 points (from ~14.4px to ~12.4px) */
        position: relative;
        padding: 4px 8px;
        transition: color 0.25s cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover {
        color: #ffffff;
    }

    .nav-links a::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        background-color: #00A2FF;
        z-index: -1;
        transition: width 0.25s cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover::after {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .nav-links a {
        display: block;
        width: 100%;
        text-align: center;
        margin: 10px 0;
        padding: 8px 12px;
        color: #000000;
        text-decoration: none;
        font-size: 0.875em; /* Decreased by approximately 2 points (from ~16px to ~14px) */
        position: relative;
        overflow: hidden;
        transition: color 0.25s cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover {
        color: #ffffff;
    }

    .nav-links a::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 100%;
        background-color: #00A2FF;
        z-index: -1;
        transform: translate(-50%, -50%);
        transition: width 0.25s cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover::after {
        width: 150px;
    }
}



/* Hero Section Styles */
.hero-section {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 0;
    overflow: hidden;
    position: relative;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
}

.hero-background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transform-origin: center;
    transform: scale(1);
}

.hero-slide.active .hero-background-image {
    animation: scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards;
}

@keyframes scaleUpHero {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    90% {
        transform: scale(1.65);
    }
    100% {
        transform: scale(1.75);
    }
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(135, 129, 189, 0.85);
    z-index: 2;
}

.hero-content-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: calc(100% - 160px);
    max-width: 1200px;
    padding: 0 20px;
    z-index: 3; /* Place above overlay */
}

.hero-content {
    position: relative;
    margin-left: 0; /* Remove logo container padding margin */
    max-width: 800px;
    padding: 0;
    text-align: left;
    color: #ffffff;
    overflow: hidden;
}

/* Hero headline style already defined in the global styles */



.hero-content p {
    font-family: 'Hanken Grotesk', sans-serif; /* Using Hanken Grotesk as substitute for Armin Grotesk */
    font-size: 1.2em;
    font-weight: 500;
    margin-bottom: 30px;
    letter-spacing: 0.01em;
}

.cta-button {
    display: inline-block;
    background-color: #ffffff; /* White background for the button */
    color: #1a1a1a; /* Dark text color for the button */
    padding: 15px 30px;
    text-decoration: none;
    font-size: 1.1em;
    font-weight: 700; /* Bold weight */
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
    letter-spacing: 0.02em; /* Slight letter spacing for better readability */
}

.cta-button:hover {
    background-color: #eeeeee; /* Slightly darker white on hover */
}

/* Contact Section Styles */
.contact-section {
    position: relative;
    z-index: 12; /* Higher than gallery section */
    background-color: #1a1a1a;
    transform: translateZ(0); /* Force hardware acceleration */
}

/* Spacer for fixed navbar */
.sticky-navbar-spacer {
    height: 0; /* No need for spacer since we're using fixed positioning */
    width: 100%;
}
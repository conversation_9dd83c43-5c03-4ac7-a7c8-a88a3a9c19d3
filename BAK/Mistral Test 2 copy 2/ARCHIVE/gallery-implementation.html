<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery Implementation</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&family=Space+Mono:wght@400;700&family=Syncopate:wght@400;700&family=Space+Grotesk:wght@300;400;500;600;700&family=Hanken+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }

        /* Gallery container */
        .gallery-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 0;
        }

        .gallery-title {
            font-family: 'Syncopate', sans-serif;
            font-size: clamp(1.8rem, 5vw, 4rem);
            letter-spacing: -0.05em;
            line-height: 0.9;
            margin-bottom: 30px;
            text-align: center;
            text-transform: uppercase;
        }

        /* Gallery grid - with visible borders */
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            width: 100%;
            /* Small gap to show borders */
            gap: 2px;
            background-color: #333; /* This color will show in the gaps */
        }

        /* Gallery item */
        .gallery-item {
            position: relative;
            overflow: hidden;
            aspect-ratio: auto; /* Will maintain original aspect ratio */
            cursor: pointer;
            border: 1px solid #444; /* Light border around each item */
        }

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover; /* Cover the container completely */
            display: block;
            opacity: 0; /* Start hidden - empty gallery */
            transition: opacity 0.2s ease-in; /* Fast fade in */
        }

        .gallery-item.revealed img {
            opacity: 1; /* Show image when revealed */
        }

        /* Hover effect - using ::after for magnifying glass */
        .gallery-item .magnify-icon {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 32px;
            height: 32px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/><path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.2s ease;
            z-index: 4; /* Above the reveal overlay */
            pointer-events: none;
        }

        /* Hover overlay */
        .gallery-item .hover-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #8781bd; /* Same purple color */
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 2; /* Above the image but below the magnify icon */
        }

        .gallery-item:hover .hover-overlay {
            opacity: 0.5; /* 50% tint on hover */
        }

        .gallery-item:hover .magnify-icon {
            opacity: 1;
        }

        /* Selection overlay */
        .gallery-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            border: 3px solid #fff;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
            pointer-events: none;
        }

        .gallery-item.selected::after {
            opacity: 1;
        }

        /* Modal styles */
        .gallery-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .gallery-modal.active {
            opacity: 1;
        }

        .modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90vh;
        }

        .modal-image {
            max-width: 100%;
            max-height: 90vh;
            object-fit: contain;
            transform: scale(0.8);
            opacity: 0;
            transition: transform 0.4s ease-out, opacity 0.4s ease-out;
        }

        .modal-image.active {
            transform: scale(1);
            opacity: 1;
        }

        .modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1001;
        }

        .modal-close::before,
        .modal-close::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 2px;
            background-color: white;
        }

        .modal-close::before {
            transform: rotate(45deg);
        }

        .modal-close::after {
            transform: rotate(-45deg);
        }

        .modal-nav {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            pointer-events: none;
        }

        .modal-prev,
        .modal-next {
            width: 50px;
            height: 50px;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            pointer-events: auto;
        }

        .modal-prev::before,
        .modal-next::before {
            content: '';
            width: 10px;
            height: 10px;
            border-style: solid;
            border-color: white;
            border-width: 0 0 2px 2px;
            display: inline-block;
        }

        .modal-prev::before {
            transform: rotate(45deg);
            margin-left: 5px;
        }

        .modal-next::before {
            transform: rotate(-135deg);
            margin-right: 5px;
        }

        /* Base class for zoom-pan animations */
        .animate-zoom-pan {
            position: relative;
            overflow: hidden;
        }

        /* Animation for circular pan */
        .animate-zoom-pan-circular img {
            animation: zoomPanCircular 5.5s ease-in-out;
            animation-play-state: paused; /* Start paused, will be triggered by JS */
            transform-origin: center;
            will-change: transform;
        }

        /* Animation for left-side pan */
        .animate-zoom-pan-left img {
            animation: zoomPanLeft 5.5s ease-in-out;
            animation-play-state: paused; /* Start paused, will be triggered by JS */
            transform-origin: center;
            will-change: transform;
        }

        /* Animation for right-side pan */
        .animate-zoom-pan-right img {
            animation: zoomPanRight 5.5s ease-in-out;
            animation-play-state: paused; /* Start paused, will be triggered by JS */
            transform-origin: center;
            will-change: transform;
        }

        /* Slot scroll animations */
        .animate-slot-scroll {
            position: relative;
            overflow: hidden;
        }

        /* Animation for fast horizontal slot scroll */
        .animate-slot-scroll-horizontal img {
            animation: slotScrollHorizontal 4s ease-in-out;
            animation-play-state: paused; /* Start paused, will be triggered by JS */
            transform-origin: center;
            will-change: transform;
        }

        /* Animation for slow horizontal slot scroll */
        .animate-slot-scroll-horizontal-slow img {
            animation: slotScrollHorizontalSlow 6s ease-in-out;
            animation-play-state: paused; /* Start paused, will be triggered by JS */
            transform-origin: center;
            will-change: transform;
        }

        /* Animation for fast vertical slot scroll */
        .animate-slot-scroll-vertical img {
            animation: slotScrollVertical 4s ease-in-out;
            animation-play-state: paused; /* Start paused, will be triggered by JS */
            transform-origin: center;
            will-change: transform;
        }

        /* Animation for slow vertical slot scroll */
        .animate-slot-scroll-vertical-slow img {
            animation: slotScrollVerticalSlow 6s ease-in-out;
            animation-play-state: paused; /* Start paused, will be triggered by JS */
            transform-origin: center;
            will-change: transform;
        }

        /* Original circular pan animation */
        @keyframes zoomPanCircular {
            /* Initial state */
            0% { transform: scale(1) translate(0, 0); }

            /* Zoom up quickly (0.25s) */
            3.23% { transform: scale(3) translate(0, 0); }

            /* Pan in a circle using sine/cosine pattern (5s) */
            /* We use a mathematical pattern with just a few keyframes */
            19.35% { transform: scale(3) translate(5%, 0); }     /* Right */
            35.48% { transform: scale(3) translate(0, 5%); }     /* Bottom */
            51.61% { transform: scale(3) translate(-5%, 0); }    /* Left */
            67.74% { transform: scale(3) translate(0, -5%); }    /* Top */

            /* Zoom back down (0.5s) */
            74.19% { transform: scale(1) translate(0, 0); }

            /* Rest before repeating */
            100% { transform: scale(1) translate(0, 0); }
        }

        /* Left-side pan animation */
        @keyframes zoomPanLeft {
            /* Initial state */
            0% { transform: scale(1) translate(0, 0); }

            /* Zoom up quickly (0.25s) */
            3.23% { transform: scale(3) translate(15%, 0); }

            /* Pan across left side (5s) */
            19.35% { transform: scale(3) translate(15%, 10%); }
            35.48% { transform: scale(3) translate(15%, 0); }
            51.61% { transform: scale(3) translate(15%, -10%); }
            67.74% { transform: scale(3) translate(15%, 0); }

            /* Zoom back down (0.5s) */
            74.19% { transform: scale(1) translate(0, 0); }

            /* Rest before repeating */
            100% { transform: scale(1) translate(0, 0); }
        }

        /* Right-side pan animation */
        @keyframes zoomPanRight {
            /* Initial state */
            0% { transform: scale(1) translate(0, 0); }

            /* Zoom up quickly (0.25s) */
            3.23% { transform: scale(3) translate(-15%, 0); }

            /* Pan across right side (5s) */
            19.35% { transform: scale(3) translate(-15%, 10%); }
            35.48% { transform: scale(3) translate(-15%, 0); }
            51.61% { transform: scale(3) translate(-15%, -10%); }
            67.74% { transform: scale(3) translate(-15%, 0); }

            /* Zoom back down (0.5s) */
            74.19% { transform: scale(1) translate(0, 0); }

            /* Rest before repeating */
            100% { transform: scale(1) translate(0, 0); }
        }

        /* New animation: Scaled views with cross-fade */
        .animate-scaled-views {
            position: relative;
            overflow: hidden;
        }

        /* Apply animation directly to the image */
        .animate-scaled-views img {
            animation: scaledViews 12s ease-in-out;
            animation-play-state: paused; /* Start paused, will be triggered by JS */
            transform-origin: center;
            will-change: transform;
        }

        /* Animation that shows 8 different static close-ups with simple fades between them */
        @keyframes scaledViews {
            /* Start normal */
            0% { transform: scale(1); opacity: 1; }

            /* Fade to close-up 1: Top-left */
            5% { transform: scale(1); opacity: 0; }
            7% { transform: scale(2.5) translate(20%, 20%); opacity: 0; }
            9% { transform: scale(2.5) translate(20%, 20%); opacity: 1; }
            12% { transform: scale(2.5) translate(20%, 20%); opacity: 1; }

            /* Fade to close-up 2: Top-right */
            14% { transform: scale(2.5) translate(20%, 20%); opacity: 0; }
            16% { transform: scale(2.5) translate(-20%, 20%); opacity: 0; }
            18% { transform: scale(2.5) translate(-20%, 20%); opacity: 1; }
            21% { transform: scale(2.5) translate(-20%, 20%); opacity: 1; }

            /* Fade to close-up 3: Bottom-left */
            23% { transform: scale(2.5) translate(-20%, 20%); opacity: 0; }
            25% { transform: scale(2.5) translate(20%, -20%); opacity: 0; }
            27% { transform: scale(2.5) translate(20%, -20%); opacity: 1; }
            30% { transform: scale(2.5) translate(20%, -20%); opacity: 1; }

            /* Fade to close-up 4: Bottom-right */
            32% { transform: scale(2.5) translate(20%, -20%); opacity: 0; }
            34% { transform: scale(2.5) translate(-20%, -20%); opacity: 0; }
            36% { transform: scale(2.5) translate(-20%, -20%); opacity: 1; }
            39% { transform: scale(2.5) translate(-20%, -20%); opacity: 1; }

            /* Fade to close-up 5: Center */
            41% { transform: scale(2.5) translate(-20%, -20%); opacity: 0; }
            43% { transform: scale(2.5) translate(0, 0); opacity: 0; }
            45% { transform: scale(2.5) translate(0, 0); opacity: 1; }
            48% { transform: scale(2.5) translate(0, 0); opacity: 1; }

            /* Fade to close-up 6: Top-center */
            50% { transform: scale(2.5) translate(0, 0); opacity: 0; }
            52% { transform: scale(2.5) translate(0, 20%); opacity: 0; }
            54% { transform: scale(2.5) translate(0, 20%); opacity: 1; }
            57% { transform: scale(2.5) translate(0, 20%); opacity: 1; }

            /* Fade to close-up 7: Bottom-center */
            59% { transform: scale(2.5) translate(0, 20%); opacity: 0; }
            61% { transform: scale(2.5) translate(0, -20%); opacity: 0; }
            63% { transform: scale(2.5) translate(0, -20%); opacity: 1; }
            66% { transform: scale(2.5) translate(0, -20%); opacity: 1; }

            /* Fade to close-up 8: Left-center */
            68% { transform: scale(2.5) translate(0, -20%); opacity: 0; }
            70% { transform: scale(2.5) translate(20%, 0); opacity: 0; }
            72% { transform: scale(2.5) translate(20%, 0); opacity: 1; }
            75% { transform: scale(2.5) translate(20%, 0); opacity: 1; }

            /* Fade to close-up 9: Right-center */
            77% { transform: scale(2.5) translate(20%, 0); opacity: 0; }
            79% { transform: scale(2.5) translate(-20%, 0); opacity: 0; }
            81% { transform: scale(2.5) translate(-20%, 0); opacity: 1; }
            84% { transform: scale(2.5) translate(-20%, 0); opacity: 1; }

            /* Fade back to normal */
            86% { transform: scale(2.5) translate(-20%, 0); opacity: 0; }
            88% { transform: scale(1) translate(0, 0); opacity: 0; }
            90% { transform: scale(1) translate(0, 0); opacity: 1; }

            /* Rest before repeating */
            100% { transform: scale(1) translate(0, 0); opacity: 1; }
        }

        /* Slot scroll animation base styles */
        .animate-slot-scroll {
            position: relative;
            overflow: hidden;
        }

        /* Hide the original image during slot scroll animations */
        .animate-slot-scroll img {
            opacity: 0 !important;
        }

        /* VARIABLE SPEED ANIMATIONS - CSS-based */

        /* Container for horizontal variable speed */
        .animate-slot-scroll .scroll-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 1000%; /* Multiple copies for seamless scrolling */
            height: 100%;
            display: flex;
        }

        /* Container for vertical variable speed */
        .animate-slot-scroll .scroll-container-vertical {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1000%; /* Multiple copies for seamless scrolling */
            display: flex;
            flex-direction: column;
        }

        /* Style for the images in variable speed animations */
        .animate-slot-scroll .scroll-image,
        .animate-slot-scroll .scroll-image-duplicate {
            width: 100%;
            height: 100%;
            flex: 0 0 10%; /* Each image takes up 10% of the container */
            background-size: cover;
            background-position: center;
        }

        /* Horizontal slot scroll with variable speed (speeds up, 3 scrolls, slows down) */
        @keyframes slotScrollHorizontal {
            0% { transform: translateX(0); }
            100% { transform: translateX(-90%); } /* Move by exactly 9 symbols */
        }

        /* Vertical slot scroll with variable speed (speeds up, 3 scrolls, slows down) */
        @keyframes slotScrollVertical {
            0% { transform: translateY(0); }
            100% { transform: translateY(-90%); } /* Move by exactly 9 symbols */
        }

        /* Apply variable speed animations to containers */
        .animate-slot-scroll-horizontal .scroll-container {
            animation: slotScrollHorizontal 4s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
        }

        .animate-slot-scroll-vertical .scroll-container-vertical {
            animation: slotScrollVertical 4s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
        }

        /* CONSTANT SPEED ANIMATIONS - Special containers */

        /* Container for constant speed animations */
        .animate-slot-scroll .constant-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        /* Reels for constant speed horizontal animation */
        .animate-slot-scroll .horizontal-reel {
            position: absolute;
            width: 1000%;
            height: 100%;
            top: 0;
            left: 0;
            display: flex;
        }

        /* Reels for constant speed vertical animation */
        .animate-slot-scroll .vertical-reel {
            position: absolute;
            width: 100%;
            height: 1000%;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
        }

        /* Symbols for constant speed animations */
        .animate-slot-scroll .horizontal-symbol {
            width: 10%;
            height: 100%;
            flex-shrink: 0;
            background-size: cover;
            background-position: center;
        }

        .animate-slot-scroll .vertical-symbol {
            width: 100%;
            height: 10%;
            flex-shrink: 0;
            background-size: cover;
            background-position: center;
        }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .gallery-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 576px) {
            .gallery-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="gallery-container">
        <h1 class="gallery-title">Gallery</h1>
        <div class="gallery-grid" id="gallery-grid">
            <!-- Gallery items will be loaded dynamically -->
        </div>
    </div>

    <!-- Modal -->
    <div class="gallery-modal" id="gallery-modal">
        <div class="modal-close" id="modal-close"></div>
        <div class="modal-content">
            <img src="" alt="" class="modal-image" id="modal-image">
            <div class="modal-nav">
                <div class="modal-prev" id="modal-prev"></div>
                <div class="modal-next" id="modal-next"></div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const galleryGrid = document.getElementById('gallery-grid');
            const modal = document.getElementById('gallery-modal');
            const modalImage = document.getElementById('modal-image');
            const modalClose = document.getElementById('modal-close');
            const modalPrev = document.getElementById('modal-prev');
            const modalNext = document.getElementById('modal-next');

            let currentIndex = 0;
            let galleryImages = [];

            // Function to load images from the gallery-images directory
            async function loadGalleryImages() {
                try {
                    // For demo purposes, we'll use placeholder images
                    // In a real implementation, you would fetch from your directory
                    const imagePaths = [
                        'images/Hero1.png',
                        'images/Hero2.png',
                        'images/Hero3.png',
                        'images/Hero4.png',
                        'images/Hero5.png',
                        'images/artchop-logo-1.png',
                        'images/artchop-logo-2.png',
                        'images/flavi.png'
                    ];

                    galleryImages = imagePaths;

                    // Create gallery items
                    galleryImages.forEach((imagePath, index) => {
                        const galleryItem = document.createElement('div');
                        galleryItem.className = 'gallery-item';
                        galleryItem.dataset.index = index;

                        const img = document.createElement('img');
                        img.src = imagePath;
                        img.alt = `Gallery image ${index + 1}`;

                        // No tint overlay needed anymore

                        // Create hover overlay element
                        const hoverOverlay = document.createElement('div');
                        hoverOverlay.className = 'hover-overlay';

                        // Create magnify icon element
                        const magnifyIcon = document.createElement('div');
                        magnifyIcon.className = 'magnify-icon';

                        // All images will be animation candidates
                        // Store the image path for animations
                        galleryItem.dataset.imagePath = imagePath;

                        // Add animation-ready class to all items
                        galleryItem.classList.add('animation-ready');

                        galleryItem.appendChild(img);
                        galleryItem.appendChild(hoverOverlay);
                        galleryItem.appendChild(magnifyIcon);
                        galleryGrid.appendChild(galleryItem);

                        // Add click event
                        galleryItem.addEventListener('click', () => {
                            openModal(index);
                        });
                    });

                    // Fast sequential fade-in animation
                    setTimeout(() => {
                        const galleryItems = document.querySelectorAll('.gallery-item');

                        // Function to reveal items sequentially
                        function revealNextItem(index) {
                            if (index >= galleryItems.length) return; // Done with all items

                            // Reveal current item
                            galleryItems[index].classList.add('revealed');

                            // Queue next item with a short delay
                            setTimeout(() => {
                                revealNextItem(index + 1);
                            }, 100); // Fast 100ms delay between items
                        }

                        // Start with the first item
                        revealNextItem(0);

                        // After all images are revealed, start the animation cycle
                        setTimeout(() => {
                            // Get all animation-ready items
                            const allItems = document.querySelectorAll('.animation-ready');

                            // Animation types
                            const animationTypes = [
                                {
                                    name: 'zoom-pan-circular',
                                    apply: (item) => {
                                        resetItemClasses(item);
                                        item.classList.add('animate-zoom-pan');
                                        item.classList.add('animate-zoom-pan-circular');
                                    }
                                },
                                {
                                    name: 'zoom-pan-left',
                                    apply: (item) => {
                                        resetItemClasses(item);
                                        item.classList.add('animate-zoom-pan');
                                        item.classList.add('animate-zoom-pan-left');
                                    }
                                },
                                {
                                    name: 'zoom-pan-right',
                                    apply: (item) => {
                                        resetItemClasses(item);
                                        item.classList.add('animate-zoom-pan');
                                        item.classList.add('animate-zoom-pan-right');
                                    }
                                },
                                {
                                    name: 'scaled-views',
                                    apply: (item) => {
                                        resetItemClasses(item);
                                        item.classList.add('animate-scaled-views');
                                    }
                                },
                                {
                                    name: 'slot-scroll-horizontal',
                                    apply: (item) => {
                                        resetItemClasses(item);
                                        item.classList.add('animate-slot-scroll');
                                        item.classList.add('animate-slot-scroll-horizontal');

                                        // Create container for variable speed animation
                                        const container = document.createElement('div');
                                        container.className = 'scroll-container';

                                        // Create 10 copies of the image for seamless scrolling
                                        for (let i = 0; i < 10; i++) {
                                            const img = document.createElement('div');
                                            img.className = i === 0 ? 'scroll-image' : 'scroll-image-duplicate';
                                            img.style.backgroundImage = `url(${item.dataset.imagePath})`;
                                            container.appendChild(img);
                                        }

                                        item.appendChild(container);
                                    }
                                },
                                {
                                    name: 'slot-scroll-vertical',
                                    apply: (item) => {
                                        resetItemClasses(item);
                                        item.classList.add('animate-slot-scroll');
                                        item.classList.add('animate-slot-scroll-vertical');

                                        // Create container for variable speed animation
                                        const container = document.createElement('div');
                                        container.className = 'scroll-container-vertical';

                                        // Create 10 copies of the image for seamless scrolling
                                        for (let i = 0; i < 10; i++) {
                                            const img = document.createElement('div');
                                            img.className = i === 0 ? 'scroll-image' : 'scroll-image-duplicate';
                                            img.style.backgroundImage = `url(${item.dataset.imagePath})`;
                                            container.appendChild(img);
                                        }

                                        item.appendChild(container);
                                    }
                                },
                                {
                                    name: 'slot-scroll-horizontal-slow',
                                    apply: (item) => {
                                        resetItemClasses(item);
                                        item.classList.add('animate-slot-scroll');

                                        // Create container for constant speed animation
                                        const container = document.createElement('div');
                                        container.className = 'constant-container';

                                        // Create two reels for seamless scrolling
                                        const reel1 = document.createElement('div');
                                        reel1.className = 'horizontal-reel';
                                        reel1.id = `horizontal-constant-reel1-${Date.now()}`;
                                        reel1.style.left = '0';

                                        const reel2 = document.createElement('div');
                                        reel2.className = 'horizontal-reel';
                                        reel2.id = `horizontal-constant-reel2-${Date.now()}`;
                                        reel2.style.left = '-100%';

                                        // Create 10 symbols for each reel
                                        for (let i = 0; i < 10; i++) {
                                            const symbol1 = document.createElement('div');
                                            symbol1.className = 'horizontal-symbol';
                                            symbol1.style.backgroundImage = `url(${item.dataset.imagePath})`;
                                            reel1.appendChild(symbol1);

                                            const symbol2 = document.createElement('div');
                                            symbol2.className = 'horizontal-symbol';
                                            symbol2.style.backgroundImage = `url(${item.dataset.imagePath})`;
                                            reel2.appendChild(symbol2);
                                        }

                                        // Add reels to container
                                        container.appendChild(reel1);
                                        container.appendChild(reel2);

                                        // Add container to item
                                        item.appendChild(container);

                                        // Start constant speed animation
                                        animateHorizontalConstant(reel1.id, reel2.id);
                                    }
                                },
                                {
                                    name: 'slot-scroll-vertical-slow',
                                    apply: (item) => {
                                        resetItemClasses(item);
                                        item.classList.add('animate-slot-scroll');

                                        // Create container for constant speed animation
                                        const container = document.createElement('div');
                                        container.className = 'constant-container';

                                        // Create two reels for seamless scrolling
                                        const reel1 = document.createElement('div');
                                        reel1.className = 'vertical-reel';
                                        reel1.id = `vertical-constant-reel1-${Date.now()}`;
                                        reel1.style.top = '0';

                                        const reel2 = document.createElement('div');
                                        reel2.className = 'vertical-reel';
                                        reel2.id = `vertical-constant-reel2-${Date.now()}`;
                                        reel2.style.top = '-100%';

                                        // Create 10 symbols for each reel
                                        for (let i = 0; i < 10; i++) {
                                            const symbol1 = document.createElement('div');
                                            symbol1.className = 'vertical-symbol';
                                            symbol1.style.backgroundImage = `url(${item.dataset.imagePath})`;
                                            reel1.appendChild(symbol1);

                                            const symbol2 = document.createElement('div');
                                            symbol2.className = 'vertical-symbol';
                                            symbol2.style.backgroundImage = `url(${item.dataset.imagePath})`;
                                            reel2.appendChild(symbol2);
                                        }

                                        // Add reels to container
                                        container.appendChild(reel1);
                                        container.appendChild(reel2);

                                        // Add container to item
                                        item.appendChild(container);

                                        // Start constant speed animation
                                        animateVerticalConstant(reel1.id, reel2.id);
                                    }
                                }
                            ];

                            // Store animation IDs for cleanup
                            const animationIds = {};

                            // Animate horizontal constant speed
                            function animateHorizontalConstant(reel1Id, reel2Id) {
                                const reel1 = document.getElementById(reel1Id);
                                const reel2 = document.getElementById(reel2Id);

                                if (!reel1 || !reel2) return;

                                let position1 = 0;
                                let position2 = -100;
                                const speed = 0.5; // Adjust speed as needed

                                function animate() {
                                    // Move both reels rightward
                                    position1 += speed;
                                    position2 += speed;

                                    // Reset positions when a reel moves completely out of view
                                    if (position1 >= 100) {
                                        position1 = -100;
                                    }
                                    if (position2 >= 100) {
                                        position2 = -100;
                                    }

                                    // Apply positions
                                    if (reel1 && reel1.parentNode) reel1.style.left = position1 + '%';
                                    if (reel2 && reel2.parentNode) reel2.style.left = position2 + '%';

                                    // Continue animation if elements still exist
                                    if (reel1 && reel1.parentNode && reel2 && reel2.parentNode) {
                                        animationIds[reel1Id] = requestAnimationFrame(animate);
                                    } else {
                                        // Clean up if elements are gone
                                        delete animationIds[reel1Id];
                                    }
                                }

                                // Start animation
                                animationIds[reel1Id] = requestAnimationFrame(animate);
                            }

                            // Animate vertical constant speed
                            function animateVerticalConstant(reel1Id, reel2Id) {
                                const reel1 = document.getElementById(reel1Id);
                                const reel2 = document.getElementById(reel2Id);

                                if (!reel1 || !reel2) return;

                                let position1 = 0;
                                let position2 = -100;
                                const speed = 0.5; // Adjust speed as needed

                                function animate() {
                                    // Move both reels downward
                                    position1 += speed;
                                    position2 += speed;

                                    // Reset positions when a reel moves completely out of view
                                    if (position1 >= 100) {
                                        position1 = -100;
                                    }
                                    if (position2 >= 100) {
                                        position2 = -100;
                                    }

                                    // Apply positions
                                    if (reel1 && reel1.parentNode) reel1.style.top = position1 + '%';
                                    if (reel2 && reel2.parentNode) reel2.style.top = position2 + '%';

                                    // Continue animation if elements still exist
                                    if (reel1 && reel1.parentNode && reel2 && reel2.parentNode) {
                                        animationIds[reel1Id] = requestAnimationFrame(animate);
                                    } else {
                                        // Clean up if elements are gone
                                        delete animationIds[reel1Id];
                                    }
                                }

                                // Start animation
                                animationIds[reel1Id] = requestAnimationFrame(animate);
                            }

                            // Function to reset item classes
                            function resetItemClasses(item) {
                                // Remove all animation classes
                                item.classList.remove(
                                    'animate-zoom-pan',
                                    'animate-zoom-pan-circular',
                                    'animate-zoom-pan-left',
                                    'animate-zoom-pan-right',
                                    'animate-scaled-views',
                                    'animate-slot-scroll',
                                    'animate-slot-scroll-horizontal',
                                    'animate-slot-scroll-vertical',
                                    'animate-slot-scroll-horizontal-slow',
                                    'animate-slot-scroll-vertical-slow'
                                );

                                // Cancel any running animations
                                const reels = item.querySelectorAll('[id^="horizontal-constant-reel"], [id^="vertical-constant-reel"]');
                                reels.forEach(reel => {
                                    if (reel.id && animationIds[reel.id]) {
                                        cancelAnimationFrame(animationIds[reel.id]);
                                        delete animationIds[reel.id];
                                    }
                                });

                                // Remove any containers
                                const containers = item.querySelectorAll('.scroll-container, .scroll-container-vertical, .constant-container');
                                containers.forEach(container => {
                                    container.remove();
                                });

                                // Reset any animation state on the image
                                const img = item.querySelector('img');
                                if (img) {
                                    img.style.animation = 'none';
                                    img.offsetHeight; // Trigger reflow
                                    img.style.animation = '';
                                    img.style.animationPlayState = 'running';
                                    img.style.opacity = '1'; // Make sure the image is visible
                                }
                            }

                            // Function to apply a random animation to an item
                            function applyRandomAnimation(item) {
                                const randomType = animationTypes[Math.floor(Math.random() * animationTypes.length)];
                                randomType.apply(item);

                                // Get the image element
                                const img = item.querySelector('img');
                                if (img) {
                                    // Make sure animation is running
                                    img.style.animationPlayState = 'running';

                                    // When animation ends, go back to static for 3 seconds
                                    img.addEventListener('animationend', function onAnimationEnd() {
                                        // Remove this listener to avoid duplicates
                                        img.removeEventListener('animationend', onAnimationEnd);

                                        // Reset to static state
                                        resetItemClasses(item);

                                        // After 3 seconds, apply a new random animation
                                        setTimeout(() => {
                                            applyRandomAnimation(item);
                                        }, 3000);
                                    }, { once: true });
                                } else {
                                    // For slot scroll animations, we need to handle differently
                                    // since they use pseudo-elements
                                    if (item.classList.contains('animate-slot-scroll')) {
                                        console.log('Starting slot scroll animation for', item);

                                        // Make sure the background image is set
                                        if (!item.style.getPropertyValue('--image-url')) {
                                            item.style.setProperty('--image-url', `url(${item.dataset.imagePath})`);
                                        }

                                        // Set a timeout to go back to static after animation duration
                                        const duration = item.classList.contains('animate-slot-scroll-horizontal-slow') ||
                                                        item.classList.contains('animate-slot-scroll-vertical-slow') ?
                                                        6000 : 4000;

                                        setTimeout(() => {
                                            console.log('Slot scroll animation ended for', item);
                                            // Reset to static state
                                            resetItemClasses(item);

                                            // After 3 seconds, apply a new random animation
                                            setTimeout(() => {
                                                applyRandomAnimation(item);
                                            }, 3000);
                                        }, duration);
                                    }
                                }
                            }

                            // Start animations for all items with staggered delays
                            allItems.forEach((item, index) => {
                                // Stagger the initial animations
                                setTimeout(() => {
                                    applyRandomAnimation(item);
                                }, index * 500); // 500ms stagger between items
                            });
                        }, 2000); // Start animations 2 seconds after all images are revealed
                    }, 300); // Small initial delay before starting
                } catch (error) {
                    console.error('Error loading gallery images:', error);
                }
            }

            // Function to open modal with expand animation
            function openModal(index) {
                // Clear any previous selection
                document.querySelectorAll('.gallery-item.selected').forEach(item => {
                    item.classList.remove('selected');
                });

                // Mark the clicked item as selected
                const selectedItem = document.querySelector(`.gallery-item[data-index="${index}"]`);
                if (selectedItem) {
                    selectedItem.classList.add('selected');
                }

                currentIndex = index;

                // Set the image source but don't animate yet
                modalImage.src = galleryImages[index];
                modalImage.classList.remove('active');

                // Display the modal first without opacity
                modal.style.display = 'flex';

                // Trigger animations after a tiny delay to ensure display:flex is applied
                setTimeout(() => {
                    // Fade in the modal background
                    modal.classList.add('active');

                    // After background starts fading in, expand the image
                    setTimeout(() => {
                        modalImage.classList.add('active');
                    }, 100);
                }, 10);

                document.body.style.overflow = 'hidden'; // Prevent scrolling
            }

            // Function to close modal with animation
            function closeModal() {
                // First shrink the image
                modalImage.classList.remove('active');

                // Then fade out the modal background
                setTimeout(() => {
                    modal.classList.remove('active');

                    // After fade out completes, hide the modal
                    setTimeout(() => {
                        modal.style.display = 'none';
                        document.body.style.overflow = ''; // Re-enable scrolling
                    }, 300); // Match the transition duration
                }, 100);

                // Keep the selection visible for a moment after closing
                setTimeout(() => {
                    document.querySelectorAll('.gallery-item.selected').forEach(item => {
                        item.classList.remove('selected');
                    });
                }, 500);
            }

            // Function to navigate to previous image with animation
            function prevImage() {
                // First fade out the current image
                modalImage.classList.remove('active');

                setTimeout(() => {
                    // Update current index
                    currentIndex = (currentIndex - 1 + galleryImages.length) % galleryImages.length;

                    // Update the image source
                    modalImage.src = galleryImages[currentIndex];

                    // After a tiny delay to ensure the new image is loaded, fade it in
                    setTimeout(() => {
                        modalImage.classList.add('active');
                    }, 50);

                    // Update selection state
                    document.querySelectorAll('.gallery-item.selected').forEach(item => {
                        item.classList.remove('selected');
                    });

                    const selectedItem = document.querySelector(`.gallery-item[data-index="${currentIndex}"]`);
                    if (selectedItem) {
                        selectedItem.classList.add('selected');

                        // Scroll the item into view if needed
                        if (!isElementInViewport(selectedItem)) {
                            selectedItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }
                }, 200); // Short delay to allow fade out
            }

            // Function to navigate to next image with animation
            function nextImage() {
                // First fade out the current image
                modalImage.classList.remove('active');

                setTimeout(() => {
                    // Update current index
                    currentIndex = (currentIndex + 1) % galleryImages.length;

                    // Update the image source
                    modalImage.src = galleryImages[currentIndex];

                    // After a tiny delay to ensure the new image is loaded, fade it in
                    setTimeout(() => {
                        modalImage.classList.add('active');
                    }, 50);

                    // Update selection state
                    document.querySelectorAll('.gallery-item.selected').forEach(item => {
                        item.classList.remove('selected');
                    });

                    const selectedItem = document.querySelector(`.gallery-item[data-index="${currentIndex}"]`);
                    if (selectedItem) {
                        selectedItem.classList.add('selected');

                        // Scroll the item into view if needed
                        if (!isElementInViewport(selectedItem)) {
                            selectedItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }
                }, 200); // Short delay to allow fade out
            }

            // Helper function to check if an element is in the viewport
            function isElementInViewport(el) {
                const rect = el.getBoundingClientRect();
                return (
                    rect.top >= 0 &&
                    rect.left >= 0 &&
                    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
                );
            }

            // Event listeners
            modalClose.addEventListener('click', closeModal);
            modalPrev.addEventListener('click', prevImage);
            modalNext.addEventListener('click', nextImage);

            // Close modal when clicking outside the image
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (modal.style.display === 'flex') {
                    if (e.key === 'Escape') {
                        closeModal();
                    } else if (e.key === 'ArrowLeft') {
                        prevImage();
                    } else if (e.key === 'ArrowRight') {
                        nextImage();
                    }
                }
            });

            // Load gallery images
            loadGalleryImages();
        });
    </script>
</body>
</html>

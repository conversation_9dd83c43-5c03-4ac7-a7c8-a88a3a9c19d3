<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery Workshop</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&family=Space+Mono:wght@400;700&family=Syncopate:wght@400;700&family=Space+Grotesk:wght@300;400;500;600;700&family=Hanken+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <style>
        /* Additional styles specific to this workshop */
        body {
            padding-top: 0;
            scroll-padding-top: 0;
        }

        .gallery-section {
            min-height: 100vh;
            padding: 80px 40px;
            background-color: #e74c3c; /* Bright red */
        }

        .gallery-content {
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 40px;
        }

        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            aspect-ratio: 16/9;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            background-color: #000;
        }

        .gallery-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            display: block;
            transition: transform 0.5s ease, opacity 0.3s ease;
        }

        .gallery-item:hover img {
            transform: scale(1.1);
            opacity: 0.8;
        }

        .gallery-item-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 20px;
            background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
            color: white;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .gallery-item:hover .gallery-item-overlay {
            opacity: 1;
        }

        .gallery-item-title {
            font-family: 'Syncopate', sans-serif;
            font-size: 0.9rem;
            font-weight: 700;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: -0.05em;
        }

        .gallery-item-category {
            font-family: 'Hanken Grotesk', sans-serif;
            font-size: 0.775rem;
            font-weight: 500;
        }

        /* Lightbox styles */
        .lightbox {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.95);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            padding: 40px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .lightbox.active {
            display: flex;
            opacity: 1;
        }

        .lightbox-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            transform: scale(0.95);
            transition: transform 0.3s ease;
        }

        .lightbox.active .lightbox-content {
            transform: scale(1);
        }

        .lightbox-image {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
            border-radius: 4px;
            box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
        }

        .lightbox-caption {
            position: absolute;
            bottom: -60px;
            left: 0;
            width: 100%;
            color: white;
            text-align: center;
            padding: 10px;
        }

        .lightbox-title {
            font-family: 'Syncopate', sans-serif;
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: -0.05em;
        }

        .lightbox-category {
            font-family: 'Hanken Grotesk', sans-serif;
            font-size: 0.875rem;
            font-weight: 500;
            opacity: 0.8;
        }

        .lightbox-close {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 30px;
            cursor: pointer;
            z-index: 10000;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            transition: background-color 0.3s ease, transform 0.3s ease;
        }

        .lightbox-close:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(90deg);
        }

        .lightbox-nav {
            position: absolute;
            top: 50%;
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0 20px;
            transform: translateY(-50%);
        }

        .lightbox-prev, .lightbox-next {
            color: white;
            font-size: 30px;
            cursor: pointer;
            background: rgba(0, 0, 0, 0.5);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease, transform 0.3s ease;
        }

        .lightbox-prev:hover, .lightbox-next:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .lightbox-prev:hover {
            transform: translateX(-5px);
        }

        .lightbox-next:hover {
            transform: translateX(5px);
        }

        /* Gallery filter */
        .gallery-filter {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            margin: 30px 0;
        }

        .filter-button {
            background: transparent;
            border: 2px solid #ffffff;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 30px;
            font-family: 'Hanken Grotesk', sans-serif;
            font-size: 0.775rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .filter-button:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .filter-button.active {
            background: #ffffff;
            color: #e74c3c;
        }

        /* Animation for filtering */
        .gallery-item {
            transition: opacity 0.5s ease, transform 0.5s ease;
        }

        .gallery-item.hidden {
            opacity: 0;
            transform: scale(0.8);
            pointer-events: none;
        }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .gallery-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .gallery-filter {
                gap: 8px;
            }

            .filter-button {
                padding: 6px 12px;
                font-size: 0.7rem;
            }
        }

        @media (max-width: 576px) {
            .gallery-grid {
                grid-template-columns: 1fr;
            }

            .gallery-section {
                padding: 60px 20px;
            }

            .gallery-filter {
                flex-direction: column;
                align-items: center;
                gap: 8px;
            }

            .filter-button {
                width: 100%;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <section class="gallery-section" id="gallery">
        <div class="gallery-content">
            <h2>OUR WORK</h2>
            <p>Explore our latest projects and creative endeavors.</p>

            <div class="gallery-filter">
                <button class="filter-button active" data-filter="all">All</button>
                <button class="filter-button" data-filter="Photography">Photography</button>
                <button class="filter-button" data-filter="Digital Art">Digital Art</button>
                <button class="filter-button" data-filter="Commercial">Commercial</button>
                <button class="filter-button" data-filter="Design">Design</button>
                <button class="filter-button" data-filter="Branding">Branding</button>
            </div>

            <div class="gallery-grid">
                <!-- Gallery Item 1 -->
                <div class="gallery-item" data-title="Hero Image 1" data-category="Photography">
                    <img src="images/Hero1.png" alt="Hero Image 1">
                    <div class="gallery-item-overlay">
                        <div class="gallery-item-title">Hero Image 1</div>
                        <div class="gallery-item-category">Photography</div>
                    </div>
                </div>

                <!-- Gallery Item 2 -->
                <div class="gallery-item" data-title="Hero Image 2" data-category="Digital Art">
                    <img src="images/Hero2.png" alt="Hero Image 2">
                    <div class="gallery-item-overlay">
                        <div class="gallery-item-title">Hero Image 2</div>
                        <div class="gallery-item-category">Digital Art</div>
                    </div>
                </div>

                <!-- Gallery Item 3 -->
                <div class="gallery-item" data-title="Hero Image 3" data-category="Commercial">
                    <img src="images/Hero3.png" alt="Hero Image 3">
                    <div class="gallery-item-overlay">
                        <div class="gallery-item-title">Hero Image 3</div>
                        <div class="gallery-item-category">Commercial</div>
                    </div>
                </div>

                <!-- Gallery Item 4 -->
                <div class="gallery-item" data-title="Hero Image 4" data-category="Photography">
                    <img src="images/Hero4.png" alt="Hero Image 4">
                    <div class="gallery-item-overlay">
                        <div class="gallery-item-title">Hero Image 4</div>
                        <div class="gallery-item-category">Photography</div>
                    </div>
                </div>

                <!-- Gallery Item 5 -->
                <div class="gallery-item" data-title="Hero Image 5" data-category="Design">
                    <img src="images/Hero5.png" alt="Hero Image 5">
                    <div class="gallery-item-overlay">
                        <div class="gallery-item-title">Hero Image 5</div>
                        <div class="gallery-item-category">Design</div>
                    </div>
                </div>

                <!-- Gallery Item 6 (Repeating Hero1 for the sixth item) -->
                <div class="gallery-item" data-title="Logo Design" data-category="Branding">
                    <img src="images/artchop-logo-1.png" alt="Logo Design">
                    <div class="gallery-item-overlay">
                        <div class="gallery-item-title">Logo Design</div>
                        <div class="gallery-item-category">Branding</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Lightbox -->
    <div class="lightbox">
        <div class="lightbox-close">&times;</div>
        <div class="lightbox-content">
            <img class="lightbox-image" src="" alt="">
            <div class="lightbox-caption">
                <h3 class="lightbox-title"></h3>
                <p class="lightbox-category"></p>
            </div>
        </div>
        <div class="lightbox-nav">
            <div class="lightbox-prev">&lt;</div>
            <div class="lightbox-next">&gt;</div>
        </div>
    </div>

    <script>
        // Gallery and Lightbox functionality
        document.addEventListener('DOMContentLoaded', function() {
            const galleryItems = document.querySelectorAll('.gallery-item');
            const filterButtons = document.querySelectorAll('.filter-button');
            const lightbox = document.querySelector('.lightbox');
            const lightboxImage = document.querySelector('.lightbox-image');
            const lightboxTitle = document.querySelector('.lightbox-title');
            const lightboxCategory = document.querySelector('.lightbox-category');
            const lightboxClose = document.querySelector('.lightbox-close');
            const lightboxPrev = document.querySelector('.lightbox-prev');
            const lightboxNext = document.querySelector('.lightbox-next');

            let currentIndex = 0;
            let visibleItems = [...galleryItems]; // Track visible items for lightbox navigation

            // Filter functionality
            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Update active button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');

                    const filterValue = button.dataset.filter;

                    // Filter gallery items
                    galleryItems.forEach(item => {
                        const itemCategory = item.dataset.category;

                        if (filterValue === 'all' || filterValue === itemCategory) {
                            item.classList.remove('hidden');
                        } else {
                            item.classList.add('hidden');
                        }
                    });

                    // Update visible items array for lightbox navigation
                    visibleItems = [...galleryItems].filter(item => !item.classList.contains('hidden'));
                });
            });

            // Open lightbox when clicking on a gallery item
            galleryItems.forEach((item) => {
                item.addEventListener('click', () => {
                    // Find index in visible items array
                    currentIndex = visibleItems.indexOf(item);
                    if (currentIndex === -1) return; // Safety check

                    const imgSrc = item.querySelector('img').src;
                    const title = item.dataset.title;
                    const category = item.dataset.category;

                    lightboxImage.src = imgSrc;
                    lightboxTitle.textContent = title;
                    lightboxCategory.textContent = category;
                    lightbox.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Prevent scrolling
                });
            });

            // Close lightbox
            lightboxClose.addEventListener('click', () => {
                lightbox.classList.remove('active');
                document.body.style.overflow = ''; // Re-enable scrolling
            });

            // Navigate to previous image
            lightboxPrev.addEventListener('click', () => {
                currentIndex = (currentIndex - 1 + visibleItems.length) % visibleItems.length;
                updateLightbox();
            });

            // Navigate to next image
            lightboxNext.addEventListener('click', () => {
                currentIndex = (currentIndex + 1) % visibleItems.length;
                updateLightbox();
            });

            // Update lightbox content
            function updateLightbox() {
                const item = visibleItems[currentIndex];
                const imgSrc = item.querySelector('img').src;
                const title = item.dataset.title;
                const category = item.dataset.category;

                // Animate the image change
                lightboxImage.style.opacity = 0;
                lightboxTitle.style.opacity = 0;
                lightboxCategory.style.opacity = 0;

                setTimeout(() => {
                    lightboxImage.src = imgSrc;
                    lightboxTitle.textContent = title;
                    lightboxCategory.textContent = category;

                    lightboxImage.style.opacity = 1;
                    lightboxTitle.style.opacity = 1;
                    lightboxCategory.style.opacity = 1;
                }, 300);
            }

            // Close lightbox when clicking outside the content
            lightbox.addEventListener('click', (e) => {
                if (e.target === lightbox) {
                    lightbox.classList.remove('active');
                    document.body.style.overflow = ''; // Re-enable scrolling
                }
            });

            // Keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (!lightbox.classList.contains('active')) return;

                if (e.key === 'Escape') {
                    lightbox.classList.remove('active');
                    document.body.style.overflow = ''; // Re-enable scrolling
                } else if (e.key === 'ArrowLeft') {
                    currentIndex = (currentIndex - 1 + visibleItems.length) % visibleItems.length;
                    updateLightbox();
                } else if (e.key === 'ArrowRight') {
                    currentIndex = (currentIndex + 1) % visibleItems.length;
                    updateLightbox();
                }
            });

            // Add transition for lightbox image
            lightboxImage.style.transition = 'opacity 0.3s ease';
            lightboxTitle.style.transition = 'opacity 0.3s ease';
            lightboxCategory.style.transition = 'opacity 0.3s ease';
        });
    </script>
</body>
</html>

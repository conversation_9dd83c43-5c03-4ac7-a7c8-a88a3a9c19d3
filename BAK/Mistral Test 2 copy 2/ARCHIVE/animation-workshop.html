<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animation Workshop</title>
    <style>
        /* Base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        h1 {
            margin-bottom: 20px;
            text-align: center;
        }

        .controls {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            gap: 10px;
            flex-wrap: wrap;
        }

        button {
            padding: 8px 16px;
            background-color: #333;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Inter', sans-serif;
        }

        button:hover {
            background-color: #555;
        }

        button.active {
            background-color: #0066cc;
        }

        .workshop-container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .animation-box {
            width: 300px;
            height: 300px;
            background-color: #ddd;
            position: relative;
            overflow: hidden;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .animation-box img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Slot scroll animation styles */
        .slot-scroll {
            position: relative;
            overflow: hidden;
        }

        .slot-scroll img {
            opacity: 0;
        }

        /* Container for horizontal scrolling */
        .scroll-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 200%; /* Double width to hold two copies */
            height: 100%;
            display: flex;
        }

        /* Container for vertical scrolling */
        .scroll-container-vertical {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 200%; /* Double height to hold two copies */
            display: flex;
            flex-direction: column;
        }

        /* Style for the original and duplicate images */
        .scroll-image,
        .scroll-image-duplicate {
            width: 100%;
            height: 100%;
            flex: 0 0 50%; /* Each image takes up half of the container */
            background-size: cover;
            background-position: center;
        }

        /* Horizontal slot scroll with variable speed (speeds up, 3 scrolls, slows down) */
        @keyframes slotScrollHorizontal {
            /* Start slow */
            0% { transform: translateX(0); }
            5% { transform: translateX(-10%); }

            /* Speed up */
            10% { transform: translateX(-25%); }
            15% { transform: translateX(-50%); }

            /* First full scroll */
            20% { transform: translateX(-100%); }

            /* Second full scroll (faster) */
            30% { transform: translateX(-200%); }

            /* Third full scroll (faster) */
            40% { transform: translateX(-300%); }

            /* Slow down */
            45% { transform: translateX(-325%); }
            50% { transform: translateX(-350%); }

            /* Reset position (the container has looped back) */
            50.1% { transform: translateX(0); }

            /* Rest before repeating */
            100% { transform: translateX(0); }
        }

        /* Horizontal slot scroll with constant speed (5 scrolls) */
        @keyframes slotScrollHorizontalSlow {
            /* Constant speed, 5 full scrolls */
            0% { transform: translateX(0); }
            10% { transform: translateX(-100%); }
            20% { transform: translateX(-200%); }
            30% { transform: translateX(-300%); }
            40% { transform: translateX(-400%); }
            50% { transform: translateX(-500%); }

            /* Reset position (the container has looped back) */
            50.1% { transform: translateX(0); }

            /* Rest before repeating */
            100% { transform: translateX(0); }
        }

        /* Vertical slot scroll with variable speed (speeds up, 3 scrolls, slows down) */
        @keyframes slotScrollVertical {
            /* Start slow */
            0% { transform: translateY(0); }
            5% { transform: translateY(-10%); }

            /* Speed up */
            10% { transform: translateY(-25%); }
            15% { transform: translateY(-50%); }

            /* First full scroll */
            20% { transform: translateY(-100%); }

            /* Second full scroll (faster) */
            30% { transform: translateY(-200%); }

            /* Third full scroll (faster) */
            40% { transform: translateY(-300%); }

            /* Slow down */
            45% { transform: translateY(-325%); }
            50% { transform: translateY(-350%); }

            /* Reset position (the container has looped back) */
            50.1% { transform: translateY(0); }

            /* Rest before repeating */
            100% { transform: translateY(0); }
        }

        /* Vertical slot scroll with constant speed (5 scrolls) */
        @keyframes slotScrollVerticalSlow {
            /* Constant speed, 5 full scrolls */
            0% { transform: translateY(0); }
            10% { transform: translateY(-100%); }
            20% { transform: translateY(-200%); }
            30% { transform: translateY(-300%); }
            40% { transform: translateY(-400%); }
            50% { transform: translateY(-500%); }

            /* Reset position (the container has looped back) */
            50.1% { transform: translateY(0); }

            /* Rest before repeating */
            100% { transform: translateY(0); }
        }

        /* Animation classes */
        .horizontal-variable .scroll-container {
            animation: slotScrollHorizontal 8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
        }

        .horizontal-constant .scroll-container {
            animation: slotScrollHorizontalSlow 10s linear infinite;
        }

        .vertical-variable .scroll-container-vertical {
            animation: slotScrollVertical 8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
        }

        .vertical-constant .scroll-container-vertical {
            animation: slotScrollVerticalSlow 10s linear infinite;
        }
    </style>
</head>
<body>
    <h1>Animation Workshop</h1>

    <div class="controls">
        <button id="horizontal-variable" class="active">Horizontal (Variable Speed)</button>
        <button id="horizontal-constant">Horizontal (Constant Speed)</button>
        <button id="vertical-variable">Vertical (Variable Speed)</button>
        <button id="vertical-constant">Vertical (Constant Speed)</button>
    </div>

    <div class="workshop-container">
        <div class="animation-box slot-scroll horizontal-variable">
            <img src="https://via.placeholder.com/300x300/3498db/ffffff?text=Image+1" alt="Placeholder image">
        </div>
    </div>

    <script>
        // Sample images - using placeholder images that are guaranteed to work
        const imageSources = [
            'https://via.placeholder.com/300x300/3498db/ffffff?text=Image+1',
            'https://via.placeholder.com/300x300/e74c3c/ffffff?text=Image+2',
            'https://via.placeholder.com/300x300/2ecc71/ffffff?text=Image+3',
            'https://via.placeholder.com/300x300/f39c12/ffffff?text=Image+4'
        ];

        // Get elements
        const animationBox = document.querySelector('.animation-box');
        const buttons = document.querySelectorAll('.controls button');

        // Initialize the animation box
        function initAnimationBox() {
            // Clear any existing containers
            const existingContainers = animationBox.querySelectorAll('.scroll-container, .scroll-container-vertical');
            existingContainers.forEach(container => container.remove());

            // Get the image source
            const img = animationBox.querySelector('img');
            const imageSrc = img.src;

            // Create the appropriate container based on current class
            if (animationBox.classList.contains('horizontal-variable') ||
                animationBox.classList.contains('horizontal-constant')) {
                // Create horizontal container
                const container = document.createElement('div');
                container.className = 'scroll-container';

                const img1 = document.createElement('div');
                img1.className = 'scroll-image';
                img1.style.backgroundImage = `url(${imageSrc})`;

                const img2 = document.createElement('div');
                img2.className = 'scroll-image-duplicate';
                img2.style.backgroundImage = `url(${imageSrc})`;

                container.appendChild(img1);
                container.appendChild(img2);
                animationBox.appendChild(container);
            } else {
                // Create vertical container
                const container = document.createElement('div');
                container.className = 'scroll-container-vertical';

                const img1 = document.createElement('div');
                img1.className = 'scroll-image';
                img1.style.backgroundImage = `url(${imageSrc})`;

                const img2 = document.createElement('div');
                img2.className = 'scroll-image-duplicate';
                img2.style.backgroundImage = `url(${imageSrc})`;

                container.appendChild(img1);
                container.appendChild(img2);
                animationBox.appendChild(container);
            }
        }

        // Initialize on page load
        initAnimationBox();

        // Add debug message
        console.log('Animation workshop initialized');
        document.body.insertAdjacentHTML('beforeend', '<div style="margin-top: 20px; text-align: center;">If animations are not visible, check console for errors.</div>');

        // Button click handlers
        buttons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                buttons.forEach(b => b.classList.remove('active'));

                // Add active class to clicked button
                button.classList.add('active');

                // Remove all animation classes
                animationBox.classList.remove(
                    'horizontal-variable',
                    'horizontal-constant',
                    'vertical-variable',
                    'vertical-constant'
                );

                // Add the selected animation class
                animationBox.classList.add(button.id);

                // Reinitialize the animation box
                initAnimationBox();
            });
        });
    </script>
</body>
</html>

/**
 * Artchop Consolidated JavaScript
 * Combines all animation and interaction functionality
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('Artchop scripts initialized');

    // Initialize the word-by-word animations
    setupWordAnimations();

    // Initialize idle animations after intro animations complete
    setTimeout(() => {
        // Add a subtle text shadow animation to idle-target elements
        const idleTargets = document.querySelectorAll('.idle-target');

        idleTargets.forEach(target => {
            // Add a class for the animation
            target.classList.add('text-shadow-pulse');
        });
    }, 2500);

    // Hamburger menu functionality
    setupHamburgerMenu();

    // Carousel functionality
    setupCarousel();

    // Smooth scrolling
    setupSmoothScrolling();

    // Parallax scrolling
    setupParallaxScrolling();
});

/**
 * Set up word-by-word animations that preserve layout
 */
function setupWordAnimations() {
    // Get all intro elements
    const introElements = document.querySelectorAll('.intro');
    console.log('Found intro elements:', introElements.length);

    introElements.forEach((element, elementIndex) => {
        // Skip elements that are already processed
        if (element.classList.contains('processed')) {
            return;
        }

        // Mark as processed to avoid double-processing
        element.classList.add('processed');

        // Special handling for buttons, images, and other non-text elements
        if (element.tagName === 'A' && element.classList.contains('cta-button')) {
            // Skip CTA buttons - they're handled by CSS now
            // Make sure they have the data-text attribute
            if (!element.hasAttribute('data-text')) {
                element.setAttribute('data-text', element.textContent);
            }
            return;
        }

        if (element.tagName === 'BUTTON' ||
            element.tagName === 'IMG' || element.querySelector('img') ||
            element.classList.contains('no-word-animation')) {

            // Add intro-element class for simple rise animation
            element.classList.add('intro-element');

            // Add appropriate delay class based on element index
            const delayClass = `delay-${Math.min(elementIndex % 5 + 1, 5)}`;
            if (!element.classList.contains(delayClass)) {
                element.classList.add(delayClass);
            }
            return;
        }

        // Get the HTML content
        const originalHTML = element.innerHTML;

        // Create a temporary element to work with the content
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = originalHTML;

        // Process text nodes to wrap words
        processTextNodes(tempDiv);

        // Replace the original content with the processed content
        element.innerHTML = tempDiv.innerHTML;
    });
}

/**
 * Process text nodes to wrap words for animation
 * @param {HTMLElement} element - The element to process
 */
function processTextNodes(element) {
    // Create a TreeWalker to find all text nodes
    const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    // Array to store text nodes that need to be replaced
    const nodesToReplace = [];

    // Find all text nodes
    let node;
    while (node = walker.nextNode()) {
        // Skip empty text nodes
        if (node.nodeValue.trim() === '') continue;

        // Add to the list of nodes to replace
        nodesToReplace.push(node);
    }

    // Process each text node
    let wordIndex = 0;
    nodesToReplace.forEach(textNode => {
        // Split the text into words
        const words = textNode.nodeValue.split(/(\s+)/);

        // Create a document fragment to hold the wrapped words
        const fragment = document.createDocumentFragment();

        // Process each word and space
        words.forEach(word => {
            if (word.trim() === '') {
                // It's whitespace, preserve it
                fragment.appendChild(document.createTextNode(word));
            } else {
                // It's a word, wrap it for animation
                const wordWrap = document.createElement('span');
                wordWrap.className = 'word-wrap';

                const wordClip = document.createElement('span');
                wordClip.className = 'word-clip';

                const wordInner = document.createElement('span');
                wordInner.className = 'word-inner';
                wordInner.textContent = word;

                // Add delay class based on word index
                const delayClass = `word-delay-${Math.min(wordIndex + 1, 20)}`;
                wordInner.classList.add(delayClass);

                // Assemble the word structure
                wordClip.appendChild(wordInner);
                wordWrap.appendChild(wordClip);
                fragment.appendChild(wordWrap);

                // Increment word index for staggered animation
                wordIndex++;
            }
        });

        // Replace the text node with the fragment
        textNode.parentNode.replaceChild(fragment, textNode);
    });
}

/**
 * Set up hamburger menu functionality
 */
function setupHamburgerMenu() {
    const hamburger = document.querySelector('.hamburger-icon');
    const navLinks = document.querySelector('.nav-links');

    if (!hamburger || !navLinks) return;

    hamburger.addEventListener('click', () => {
        hamburger.classList.toggle('active');
        navLinks.classList.toggle('active');
    });

    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
        if (!hamburger.contains(e.target) && !navLinks.contains(e.target)) {
            hamburger.classList.remove('active');
            navLinks.classList.remove('active');
        }
    });
}

/**
 * Set up carousel functionality
 */
function setupCarousel() {
    const slides = document.querySelectorAll('.hero-slide');
    if (slides.length === 0) return;

    let currentSlide = 0;
    const totalSlides = slides.length;
    const transitionTime = 5000; // 5 seconds

    // Helper function to extract scale value from transform matrix
    function getScaleValue(matrix) {
        if (!matrix || matrix === 'none') return 1;
        const matrixValues = matrix.match(/matrix.*\((.+)\)/);
        if (matrixValues && matrixValues[1]) {
            const values = matrixValues[1].split(', ');
            if (values.length >= 1) {
                return parseFloat(values[0]) || 1;
            }
        }
        return 1;
    }

    // Auto-advance function
    function autoAdvance() {
        slides[currentSlide].classList.remove('active');
        currentSlide = (currentSlide + 1) % totalSlides;
        slides[currentSlide].classList.add('active');

        const heroImage = slides[currentSlide].querySelector('.hero-background-image');
        if (heroImage) {
            const scrollY = window.scrollY;
            const backgroundOffset = scrollY * 0.2;

            heroImage.style.animation = 'none';
            void heroImage.offsetWidth; // Force reflow
            heroImage.style.animation = 'scaleUpHero 5s cubic-bezier(0.16, 1, 0.3, 1) forwards';

            // When animation ends, explicitly set the scale to ensure it stays at 1.25
            heroImage.addEventListener('animationend', function() {
                // Get current scroll position for parallax
                const scrollY = window.scrollY;
                const backgroundOffset = scrollY * 0.2;

                // Set explicit transform to maintain scale
                heroImage.style.transform = `translateY(${backgroundOffset}px) scale(1.25)`;
            });

            if (scrollY > 0) {
                setTimeout(() => {
                    const currentTransform = window.getComputedStyle(heroImage).transform;
                    if (currentTransform && currentTransform !== 'none') {
                        heroImage.style.transform = `translateY(${backgroundOffset}px) scale(${getScaleValue(currentTransform)})`;
                    } else {
                        heroImage.style.transform = `translateY(${backgroundOffset}px)`;
                    }
                }, 10);
            }
        }
    }

    // Start auto-advancing
    let autoAdvanceInterval = setInterval(autoAdvance, transitionTime);

    // Pause on hover
    const heroCarousel = document.querySelector('.hero-carousel');
    if (heroCarousel) {
        heroCarousel.addEventListener('mouseenter', () => {
            clearInterval(autoAdvanceInterval);
        });

        heroCarousel.addEventListener('mouseleave', () => {
            autoAdvanceInterval = setInterval(autoAdvance, transitionTime);
        });
    }

    // Add keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
            slides[currentSlide].classList.remove('active');

            if (e.key === 'ArrowLeft') {
                currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            } else {
                currentSlide = (currentSlide + 1) % totalSlides;
            }

            slides[currentSlide].classList.add('active');

            const heroImage = slides[currentSlide].querySelector('.hero-background-image');
            if (heroImage) {
                const scrollY = window.scrollY;
                const backgroundOffset = scrollY * 0.2;

                heroImage.style.animation = 'none';
                void heroImage.offsetWidth; // Force reflow
                heroImage.style.animation = 'scaleUpHero 5s cubic-bezier(0.16, 1, 0.3, 1) forwards';

                // When animation ends, explicitly set the scale to ensure it stays at 1.25
                heroImage.addEventListener('animationend', function() {
                    // Get current scroll position for parallax
                    const scrollY = window.scrollY;
                    const backgroundOffset = scrollY * 0.2;

                    // Set explicit transform to maintain scale
                    heroImage.style.transform = `translateY(${backgroundOffset}px) scale(1.25)`;
                });

                if (scrollY > 0) {
                    setTimeout(() => {
                        const currentTransform = window.getComputedStyle(heroImage).transform;
                        if (currentTransform && currentTransform !== 'none') {
                            heroImage.style.transform = `translateY(${backgroundOffset}px) scale(${getScaleValue(currentTransform)})`;
                        } else {
                            heroImage.style.transform = `translateY(${backgroundOffset}px)`;
                        }
                    }, 10);
                }
            }
        }
    });
}
/**
 * Set up smooth scrolling functionality
 */
function setupSmoothScrolling() {
    // Add click handler for logo to scroll to top
    const logo = document.querySelector('.site-logo');
    if (logo) {
        logo.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Add click handlers for navigation links with enhanced scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const target = document.querySelector(targetId);

            if (target) {
                if (targetId !== '#hero') {
                    const targetPosition = target.getBoundingClientRect().top + window.scrollY;
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                    target.style.position = 'relative';
                    target.style.zIndex = '20';
                } else {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
}

/**
 * Set up parallax scrolling effects
 */
function setupParallaxScrolling() {
    // Helper function to extract scale value from transform matrix
    function getScaleValue(matrix) {
        if (!matrix || matrix === 'none') return 1;
        const matrixValues = matrix.match(/matrix.*\((.+)\)/);
        if (matrixValues && matrixValues[1]) {
            const values = matrixValues[1].split(', ');
            if (values.length >= 1) {
                return parseFloat(values[0]) || 1;
            }
        }
        return 1;
    }

    // Update section classes based on scroll position
    window.addEventListener('scroll', () => {
        const sections = document.querySelectorAll('.section');
        const scrollY = window.scrollY;

        // Parallax for hero background (20% scroll speed)
        const heroSlideActive = document.querySelector('.hero-slide.active');
        if (heroSlideActive) {
            const heroImage = heroSlideActive.querySelector('.hero-background-image');
            if (heroImage) {
                const backgroundOffset = scrollY * 0.2;
                const currentTransform = window.getComputedStyle(heroImage).transform;

                // Check if animation is still running
                const isAnimating = heroImage.getAnimations().some(animation =>
                    animation.animationName === 'scaleUpHero' &&
                    animation.playState !== 'finished');

                if (isAnimating) {
                    // During animation, use the current transform scale
                    if (currentTransform && currentTransform !== 'none') {
                        heroImage.style.transform = `translateY(${backgroundOffset}px) scale(${getScaleValue(currentTransform)})`;
                    } else {
                        heroImage.style.transform = `translateY(${backgroundOffset}px)`;
                    }
                } else {
                    // After animation, always maintain scale at 1.25
                    heroImage.style.transform = `translateY(${backgroundOffset}px) scale(1.25)`;
                }
            }
        }

        // Parallax for hero overlay (20% scroll speed)
        const heroOverlay = document.querySelector('.hero-overlay');
        if (heroOverlay) {
            const overlayOffset = scrollY * 0.2;
            heroOverlay.style.transform = `translateY(${overlayOffset}px)`;
        }

        // Parallax for hero content (60% scroll speed)
        const heroContent = document.querySelector('.hero-content');
        if (heroContent) {
            const contentOffset = scrollY * 0.6;
            heroContent.style.transform = `translateY(${contentOffset}px)`;
        }

        // Section active state handling
        sections.forEach(section => {
            const rect = section.getBoundingClientRect();
            if (rect.top <= 0 && rect.bottom >= 0) {
                section.classList.add('active');
                section.classList.remove('hidden');
            } else {
                section.classList.remove('active');
                section.classList.add('hidden');
            }
        });
    });

    // Initialize first section
    const firstSection = document.querySelector('.section');
    if (firstSection) {
        firstSection.classList.add('active');
    }
}

// Immediately start the hero image animation
window.addEventListener('load', function() {
    const firstHeroImage = document.querySelector('.hero-slide.active .hero-background-image');
    if (firstHeroImage) {
        // Apply animation with forwards fill mode to maintain final state
        firstHeroImage.style.animation = 'scaleUpHero 5s cubic-bezier(0.16, 1, 0.3, 1) forwards';

        // When animation ends, explicitly set the scale to ensure it stays at 1.25
        firstHeroImage.addEventListener('animationend', function() {
            // Get current scroll position for parallax
            const scrollY = window.scrollY;
            const backgroundOffset = scrollY * 0.2;

            // Set explicit transform to maintain scale
            firstHeroImage.style.transform = `translateY(${backgroundOffset}px) scale(1.25)`;
        });
    }
});

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Animation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            text-align: center;
        }

        .animation-container {
            width: 300px;
            height: 300px;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
            border: 2px solid #333;
        }

        /* Horizontal Slot Scroll - Continuous scrolling with multiple copies */
        .horizontal-scroll-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 300%; /* Three copies of the image */
            height: 100%;
            display: flex;
            animation: horizontalScroll 5s linear infinite;
        }

        .horizontal-scroll-container div {
            width: 33.333%; /* Each copy takes 1/3 of the container */
            height: 100%;
            background-image: url('https://images.unsplash.com/photo-1682687982501-1e58ab814714');
            background-size: cover;
            background-position: center;
            flex-shrink: 0; /* Prevent shrinking */
        }

        /* Basic horizontal scroll - smooth continuous motion */
        @keyframes horizontalScroll {
            0% { transform: translateX(0); }
            100% { transform: translateX(-66.666%); } /* Move by 2/3 width (2 copies) */
        }

        /* Variable speed horizontal scroll (speeds up, 3 scrolls, slows down) */
        @keyframes horizontalScrollVariable {
            /* Start slow */
            0% { transform: translateX(0); }
            2% { transform: translateX(-2%); }
            4% { transform: translateX(-5%); }
            6% { transform: translateX(-9%); }
            8% { transform: translateX(-14%); }

            /* First full scroll - accelerating */
            10% { transform: translateX(-20%); }
            12% { transform: translateX(-27%); }
            14% { transform: translateX(-35%); }
            16% { transform: translateX(-44%); }
            18% { transform: translateX(-54%); }
            20% { transform: translateX(-66.666%); } /* Complete first scroll */

            /* Second full scroll - peak speed */
            25% { transform: translateX(-100%); }
            30% { transform: translateX(-133.333%); } /* Complete second scroll */

            /* Third full scroll - decelerating */
            32% { transform: translateX(-143%); }
            34% { transform: translateX(-152%); }
            36% { transform: translateX(-160%); }
            38% { transform: translateX(-167%); }
            40% { transform: translateX(-173%); }
            42% { transform: translateX(-178%); }
            44% { transform: translateX(-182%); }
            46% { transform: translateX(-185%); }
            48% { transform: translateX(-188%); }
            50% { transform: translateX(-200%); } /* Complete third scroll */

            /* Reset position (the container has looped back) */
            50.1% { transform: translateX(0); }

            /* Rest before repeating */
            100% { transform: translateX(0); }
        }

        /* Constant speed horizontal scroll (5 scrolls) */
        @keyframes horizontalScrollConstant {
            /* Constant speed, 5 full scrolls */
            0% { transform: translateX(0); }
            10% { transform: translateX(-66.666%); } /* First scroll */
            20% { transform: translateX(-133.333%); } /* Second scroll */
            30% { transform: translateX(-200%); } /* Third scroll */
            40% { transform: translateX(-266.666%); } /* Fourth scroll */
            50% { transform: translateX(-333.333%); } /* Fifth scroll */

            /* Reset position (the container has looped back) */
            50.1% { transform: translateX(0); }

            /* Rest before repeating */
            100% { transform: translateX(0); }
        }

        /* Vertical Slot Scroll - Continuous scrolling with multiple copies */
        .vertical-scroll-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 300%; /* Three copies of the image */
            display: flex;
            flex-direction: column;
            animation: verticalScroll 5s linear infinite;
        }

        .vertical-scroll-container div {
            width: 100%;
            height: 33.333%; /* Each copy takes 1/3 of the container */
            background-image: url('https://images.unsplash.com/photo-1682687982501-1e58ab814714');
            background-size: cover;
            background-position: center;
            flex-shrink: 0; /* Prevent shrinking */
        }

        /* Basic vertical scroll - smooth continuous motion */
        @keyframes verticalScroll {
            0% { transform: translateY(0); }
            100% { transform: translateY(-66.666%); } /* Move by 2/3 height (2 copies) */
        }

        /* Variable speed vertical scroll (speeds up, 3 scrolls, slows down) */
        @keyframes verticalScrollVariable {
            /* Start slow */
            0% { transform: translateY(0); }
            2% { transform: translateY(-2%); }
            4% { transform: translateY(-5%); }
            6% { transform: translateY(-9%); }
            8% { transform: translateY(-14%); }

            /* First full scroll - accelerating */
            10% { transform: translateY(-20%); }
            12% { transform: translateY(-27%); }
            14% { transform: translateY(-35%); }
            16% { transform: translateY(-44%); }
            18% { transform: translateY(-54%); }
            20% { transform: translateY(-66.666%); } /* Complete first scroll */

            /* Second full scroll - peak speed */
            25% { transform: translateY(-100%); }
            30% { transform: translateY(-133.333%); } /* Complete second scroll */

            /* Third full scroll - decelerating */
            32% { transform: translateY(-143%); }
            34% { transform: translateY(-152%); }
            36% { transform: translateY(-160%); }
            38% { transform: translateY(-167%); }
            40% { transform: translateY(-173%); }
            42% { transform: translateY(-178%); }
            44% { transform: translateY(-182%); }
            46% { transform: translateY(-185%); }
            48% { transform: translateY(-188%); }
            50% { transform: translateY(-200%); } /* Complete third scroll */

            /* Reset position (the container has looped back) */
            50.1% { transform: translateY(0); }

            /* Rest before repeating */
            100% { transform: translateY(0); }
        }

        /* Constant speed vertical scroll (5 scrolls) */
        @keyframes verticalScrollConstant {
            /* Constant speed, 5 full scrolls */
            0% { transform: translateY(0); }
            10% { transform: translateY(-66.666%); } /* First scroll */
            20% { transform: translateY(-133.333%); } /* Second scroll */
            30% { transform: translateY(-200%); } /* Third scroll */
            40% { transform: translateY(-266.666%); } /* Fourth scroll */
            50% { transform: translateY(-333.333%); } /* Fifth scroll */

            /* Reset position (the container has looped back) */
            50.1% { transform: translateY(0); }

            /* Rest before repeating */
            100% { transform: translateY(0); }
        }

        /* Animation classes */
        .horizontal-variable .horizontal-scroll-container {
            animation: horizontalScrollVariable 8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
        }

        .horizontal-constant .horizontal-scroll-container {
            animation: horizontalScrollConstant 10s linear infinite;
        }

        .vertical-variable .vertical-scroll-container {
            animation: verticalScrollVariable 8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
        }

        .vertical-constant .vertical-scroll-container {
            animation: verticalScrollConstant 10s linear infinite;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        button {
            padding: 8px 16px;
            background-color: #333;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background-color: #555;
        }

        button.active {
            background-color: #0066cc;
        }
    </style>
</head>
<body>
    <h1>Simple Animation Test</h1>

    <div class="controls">
        <button id="btn-horizontal-variable" class="active">Horizontal (Variable Speed)</button>
        <button id="btn-horizontal-constant">Horizontal (Constant Speed)</button>
        <button id="btn-vertical-variable">Vertical (Variable Speed)</button>
        <button id="btn-vertical-constant">Vertical (Constant Speed)</button>
        <button id="btn-upload">Upload Image</button>
    </div>

    <input type="file" id="image-upload" accept="image/*" style="display: none;">

    <div class="animation-container horizontal-variable" id="animation-container">
        <div class="horizontal-scroll-container">
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>

    <script>
        // Get elements
        const container = document.getElementById('animation-container');
        const btnHorizontalVariable = document.getElementById('btn-horizontal-variable');
        const btnHorizontalConstant = document.getElementById('btn-horizontal-constant');
        const btnVerticalVariable = document.getElementById('btn-vertical-variable');
        const btnVerticalConstant = document.getElementById('btn-vertical-constant');
        const btnUpload = document.getElementById('btn-upload');
        const imageUpload = document.getElementById('image-upload');

        // Default image URL
        let currentImageUrl = 'https://images.unsplash.com/photo-1682687982501-1e58ab814714';

        // Show horizontal variable scroll by default
        showAnimation('horizontal-variable');

        // Button event listeners
        btnHorizontalVariable.addEventListener('click', function() {
            setActiveButton(this);
            showAnimation('horizontal-variable');
        });

        btnHorizontalConstant.addEventListener('click', function() {
            setActiveButton(this);
            showAnimation('horizontal-constant');
        });

        btnVerticalVariable.addEventListener('click', function() {
            setActiveButton(this);
            showAnimation('vertical-variable');
        });

        btnVerticalConstant.addEventListener('click', function() {
            setActiveButton(this);
            showAnimation('vertical-constant');
        });

        // Image upload handling
        btnUpload.addEventListener('click', function() {
            imageUpload.click();
        });

        imageUpload.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    currentImageUrl = e.target.result;
                    // Refresh current animation with new image
                    const currentAnimation = getCurrentAnimation();
                    showAnimation(currentAnimation);
                };
                reader.readAsDataURL(file);
            }
        });

        // Helper function to set active button
        function setActiveButton(activeButton) {
            // Remove active class from all buttons
            [btnHorizontalVariable, btnHorizontalConstant, btnVerticalVariable, btnVerticalConstant].forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to clicked button
            activeButton.classList.add('active');
        }

        // Helper function to get current animation type
        function getCurrentAnimation() {
            if (btnHorizontalVariable.classList.contains('active')) return 'horizontal-variable';
            if (btnHorizontalConstant.classList.contains('active')) return 'horizontal-constant';
            if (btnVerticalVariable.classList.contains('active')) return 'vertical-variable';
            if (btnVerticalConstant.classList.contains('active')) return 'vertical-constant';
            return 'horizontal-variable'; // Default
        }

        // Function to show the selected animation
        function showAnimation(animationType) {
            // Remove all animation classes
            container.classList.remove('horizontal-variable', 'horizontal-constant', 'vertical-variable', 'vertical-constant');

            // Add the selected animation class
            container.classList.add(animationType);

            // Create the appropriate container based on animation type
            if (animationType.startsWith('horizontal')) {
                container.innerHTML = `
                    <div class="horizontal-scroll-container">
                        <div style="background-image: url('${currentImageUrl}');"></div>
                        <div style="background-image: url('${currentImageUrl}');"></div>
                        <div style="background-image: url('${currentImageUrl}');"></div>
                    </div>
                `;
            } else {
                container.innerHTML = `
                    <div class="vertical-scroll-container">
                        <div style="background-image: url('${currentImageUrl}');"></div>
                        <div style="background-image: url('${currentImageUrl}');"></div>
                        <div style="background-image: url('${currentImageUrl}');"></div>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>

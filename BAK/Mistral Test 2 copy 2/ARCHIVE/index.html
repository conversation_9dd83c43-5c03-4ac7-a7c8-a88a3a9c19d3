<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Artchop Portfolio</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="typography.css">
    <link rel="stylesheet" href="fixed-animations.css">
    <link rel="stylesheet" href="minimal.css">
    <link href="https://fonts.googleapis.com/css2?family=Tomorrow:wght@400;700&display=swap" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Tomorrow:wght@300;400;500;600;700&family=Space+Mono:wght@300;400;500;600;700&family=Inter:wght@400;700&family=Hanken+Grotesk:wght@400;500;700&family=Syncopate:wght@400;700&display=swap" rel="stylesheet">
    <!-- GSAP and ScrollTrigger CDN links -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollToPlugin.min.js"></script>
    <script src="fixed-animations.js" defer></script>
    <script src="minimal.js" defer></script>
</head>
<body>
    <script>
        // Helper function to extract scale value from transform matrix
        function getScaleValue(matrix) {
            // Default scale if we can't extract it
            if (!matrix || matrix === 'none') return 1;

            // Extract scale from matrix
            const matrixValues = matrix.match(/matrix.*\((.+)\)/);
            if (matrixValues && matrixValues[1]) {
                const values = matrixValues[1].split(', ');
                // For 2D matrix, scale is at index 0 (scaleX)
                // For 3D matrix, scale is at index 0 (scaleX)
                // We'll use scaleX as an approximation
                if (values.length >= 1) {
                    return parseFloat(values[0]) || 1;
                }
            }
            return 1;
        }

        // Immediately start the hero image animation
        window.addEventListener('load', function() {
            const firstHeroImage = document.querySelector('.hero-slide.active .hero-background-image');
            if (firstHeroImage) {
                firstHeroImage.style.animation = 'scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards';
            }
        });
    </script>
    <nav class="navbar">
        <div class="navbar-container">
            <a href="#hero" class="site-logo">
                <div class="logo-container">
                    <img src="images/artchop-logo-1.png" class="logo-image logo-1">
                    <img src="images/artchop-logo-2.png" class="logo-image logo-2">
                </div>
            </a>
            <button class="hamburger-icon" aria-label="Toggle navigation menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="nav-links" id="mobile-menu">
                <a href="#about">ABOUT</a>
                <a href="#gallery">GALLERY</a>
                <a href="#contact">CONTACT</a>
            </div>
        </div>
    </nav>
    <div class="sticky-navbar-spacer"></div>

    <!-- Hero Section -->
    <section id="hero" class="hero-section section">
        <div class="hero-overlay"></div>
        <div class="hero-carousel">
            <div class="hero-slide active">
                <img src="images/Hero1.png" alt="Hero 1" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero2.png" alt="Hero 2" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero3.png" alt="Hero 3" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero4.png" alt="Hero 4" class="hero-background-image">
            </div>
            <div class="hero-slide">
                <img src="images/Hero5.png" alt="Hero 5" class="hero-background-image">
            </div>
        </div>
        <div class="hero-content-container">
            <div class="hero-content">
                <h1 class="intro delay-1 idle-target" data-text="CREATIVE PARTNER FOR GAMES">CREATIVE PARTNER FOR GAMES</h1>
                <p class="intro delay-2">Catchy sub-headline or description.</p>
                <a href="#contact" class="cta-button intro delay-3" data-text="Get in Touch">Get in Touch</a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section section">
        <div class="about-content">
            <div class="about-grid">
                <div class="about-text">
                    <h2 class="intro delay-1 idle-target" data-text="WHO WE ARE">WHO WE ARE</h2>
                    <p class="intro delay-2">Artchop is a creative studio specializing in game development and digital art. We bring passion, expertise, and innovation to every project, crafting experiences that captivate and inspire.</p>
                    <div class="about-stats intro delay-3">
                        <div class="stat-item">
                            <span class="stat-number">10+</span>
                            <span class="stat-label">Years Experience</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">50+</span>
                            <span class="stat-label">Projects Completed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">100+</span>
                            <span class="stat-label">Happy Clients</span>
                        </div>
                    </div>
                </div>
                <div class="about-image intro delay-2">
                    <img src="images/about-studio.jpg" alt="Studio Team" class="about-image-main">
                    <div class="about-image-overlay"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section id="gallery" class="gallery-section section">
        <div class="gallery-content">
            <h2 class="intro delay-1 idle-target" data-text="Our Work">Our Work</h2>
            <div class="gallery-grid">
                <div class="gallery-item intro delay-2">
                    <img src="images/gallery-1.jpg" alt="Game Art 1">
                </div>
                <div class="gallery-item intro delay-2">
                    <img src="images/gallery-2.jpg" alt="Game Art 2">
                </div>
                <div class="gallery-item intro delay-3">
                    <img src="images/gallery-3.jpg" alt="Game Art 3">
                </div>
                <div class="gallery-item intro delay-3">
                    <img src="images/gallery-4.jpg" alt="Game Art 4">
                </div>
                <div class="gallery-item intro delay-4">
                    <img src="images/gallery-5.jpg" alt="Game Art 5">
                </div>
                <div class="gallery-item intro delay-4">
                    <img src="images/gallery-6.jpg" alt="Game Art 6">
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section section">
        <div class="contact-content">
            <h2 class="intro delay-1 idle-target" data-text="Contact">Contact</h2>
            <p class="intro delay-2">This is the contact section content.</p>
        </div>
    </section>

    <script>
        // Create sliding animation for each letter
        document.querySelectorAll('.letter span').forEach(letter => {
            if (letter.textContent.trim() !== '') { // Skip spaces
                const tl = gsap.timeline({
                    repeat: -1,
                    repeatDelay: Math.random() * 2 + 1,
                    delay: Math.random() * 2
                });

                tl.to(letter, {
                    x: '100%',
                    duration: 0.5,
                    ease: 'power2.in',
                })
                .set(letter, { x: '-100%' })
                .to(letter, {
                    x: '0%',
                    duration: 0.5,
                    ease: 'power2.out',
                });
            }
        });

        // Hamburger menu functionality
        const hamburger = document.querySelector('.hamburger-icon');
        const navLinks = document.querySelector('.nav-links');

        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navLinks.classList.toggle('active');
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!hamburger.contains(e.target) && !navLinks.contains(e.target)) {
                hamburger.classList.remove('active');
                navLinks.classList.remove('active');
            }
        });

        // Carousel functionality
        const slides = document.querySelectorAll('.hero-slide');
        let currentSlide = 0;
        const totalSlides = slides.length;
        const transitionTime = 5000; // 5 seconds

        // Add active class to first slide
        slides[currentSlide].classList.add('active');

        // Auto-advance function
        function autoAdvance() {
            // Remove active class from current slide
            slides[currentSlide].classList.remove('active');
            // Move to next slide
            currentSlide = (currentSlide + 1) % totalSlides;
            // Add active class to new slide
            slides[currentSlide].classList.add('active');

            // Reset and restart the animation for the new active slide
            const heroImage = slides[currentSlide].querySelector('.hero-background-image');
            if (heroImage) {
                // Get current scroll position for parallax
                const scrollY = window.scrollY;
                const backgroundOffset = scrollY * 0.2;

                // Reset animation
                heroImage.style.animation = 'none';
                void heroImage.offsetWidth; // Force reflow

                // Apply animation with parallax offset if scrolled
                heroImage.style.animation = 'scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards';

                // Apply parallax offset if needed
                if (scrollY > 0) {
                    // Wait for animation to be applied
                    setTimeout(() => {
                        const currentTransform = window.getComputedStyle(heroImage).transform;
                        if (currentTransform && currentTransform !== 'none') {
                            heroImage.style.transform = `translateY(${backgroundOffset}px) scale(${getScaleValue(currentTransform)})`;
                        } else {
                            heroImage.style.transform = `translateY(${backgroundOffset}px)`;
                        }
                    }, 10);
                }
            }
        }

        // Start auto-advancing
        const autoAdvanceInterval = setInterval(autoAdvance, transitionTime);

        // Pause on hover
        document.querySelector('.hero-carousel').addEventListener('mouseenter', () => {
            clearInterval(autoAdvanceInterval);
        });

        document.querySelector('.hero-carousel').addEventListener('mouseleave', () => {
            autoAdvanceInterval = setInterval(autoAdvance, transitionTime);
        });

        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                // Remove active class from current slide
                slides[currentSlide].classList.remove('active');

                // Update current slide index
                if (e.key === 'ArrowLeft') {
                    currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
                } else {
                    currentSlide = (currentSlide + 1) % totalSlides;
                }

                // Add active class to new slide
                slides[currentSlide].classList.add('active');

                // Reset and restart the animation for the new active slide
                const heroImage = slides[currentSlide].querySelector('.hero-background-image');
                if (heroImage) {
                    // Get current scroll position for parallax
                    const scrollY = window.scrollY;
                    const backgroundOffset = scrollY * 0.2;

                    // Reset animation
                    heroImage.style.animation = 'none';
                    void heroImage.offsetWidth; // Force reflow

                    // Apply animation with parallax offset if scrolled
                    heroImage.style.animation = 'scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards';

                    // Apply parallax offset if needed
                    if (scrollY > 0) {
                        // Wait for animation to be applied
                        setTimeout(() => {
                            const currentTransform = window.getComputedStyle(heroImage).transform;
                            if (currentTransform && currentTransform !== 'none') {
                                heroImage.style.transform = `translateY(${backgroundOffset}px) scale(${getScaleValue(currentTransform)})`;
                            } else {
                                heroImage.style.transform = `translateY(${backgroundOffset}px)`;
                            }
                        }, 10);
                    }
                }
            }
        });

        // Add click handler for logo to scroll to top
        const logo = document.querySelector('.site-logo');
        logo.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Add click handlers for navigation links with enhanced scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const target = document.querySelector(targetId);

                if (target) {
                    // If navigating from hero to another section, apply a faster scroll
                    if (targetId !== '#hero') {
                        // Get the target's position
                        const targetPosition = target.getBoundingClientRect().top + window.scrollY;

                        // Scroll with a custom animation
                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });

                        // Ensure the target section is visible and in front
                        target.style.position = 'relative';
                        target.style.zIndex = '20';
                    } else {
                        // For scrolling to hero, use default behavior
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }
            });
        });

        // Update section classes based on scroll position
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('.section');

            const scrollY = window.scrollY;

            // Parallax for hero background (20% scroll speed)
            const heroSlideActive = document.querySelector('.hero-slide.active');
            if (heroSlideActive) {
                const heroImage = heroSlideActive.querySelector('.hero-background-image');
                if (heroImage) {
                    const backgroundOffset = scrollY * 0.2;
                    // Apply transform while preserving any existing scale transform from the animation
                    const currentTransform = window.getComputedStyle(heroImage).transform;
                    if (currentTransform && currentTransform !== 'none') {
                        // Only apply Y translation, don't affect other transforms
                        heroImage.style.transform = `translateY(${backgroundOffset}px) scale(${getScaleValue(currentTransform)})`;
                    } else {
                        heroImage.style.transform = `translateY(${backgroundOffset}px)`;
                    }
                }
            }

            // Parallax for hero overlay (20% scroll speed)
            const heroOverlay = document.querySelector('.hero-overlay');
            if (heroOverlay) {
                const overlayOffset = scrollY * 0.2;
                heroOverlay.style.transform = `translateY(${overlayOffset}px)`;
            }

            // Parallax for hero content (headline, subheadline, CTA) (60% scroll speed)
            const heroContent = document.querySelector('.hero-content');
            if (heroContent) {
                // Move the hero content at 60% of the scroll speed
                const contentOffset = scrollY * 0.6;
                heroContent.style.transform = `translateY(${contentOffset}px)`;
            }

            // Original section active state handling
            sections.forEach(section => {
                const rect = section.getBoundingClientRect();
                if (rect.top <= 0 && rect.bottom >= 0) {
                    section.classList.add('active');
                    section.classList.remove('hidden');
                } else {
                    section.classList.remove('active');
                    section.classList.add('hidden');
                }
            });
        });

        // Initialize first section
        document.querySelector('.section').classList.add('active');

        // Ensure the first hero image animation starts immediately on page load
        document.addEventListener('DOMContentLoaded', () => {
            // Force a reflow to ensure the animation starts immediately
            const firstHeroImage = document.querySelector('.hero-slide.active .hero-background-image');
            if (firstHeroImage) {
                // Get current scroll position for parallax
                const scrollY = window.scrollY;
                const backgroundOffset = scrollY * 0.2;

                // Trigger a reflow
                void firstHeroImage.offsetWidth;

                // Apply the animation class
                firstHeroImage.style.animation = 'none';
                setTimeout(() => {
                    firstHeroImage.style.animation = 'scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards';

                    // Apply parallax offset if needed
                    if (scrollY > 0) {
                        // Wait for animation to be applied
                        setTimeout(() => {
                            const currentTransform = window.getComputedStyle(firstHeroImage).transform;
                            if (currentTransform && currentTransform !== 'none') {
                                firstHeroImage.style.transform = `translateY(${backgroundOffset}px) scale(${getScaleValue(currentTransform)})`;
                            } else {
                                firstHeroImage.style.transform = `translateY(${backgroundOffset}px)`;
                            }
                        }, 10);
                    }
                }, 10);
            }
        });
    </script>
    </div>
</body>
</html>
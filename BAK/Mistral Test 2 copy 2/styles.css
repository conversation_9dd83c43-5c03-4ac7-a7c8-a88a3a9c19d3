/* ===== GLOBAL STYLES ===== */
:root {
    /* Colors */
    --color-background: #1a1a1a;
    --color-text: #ffffff;
    --color-primary: #8781bd;
    --color-secondary: #FF42A1;
    --color-dark: #000000;
    --color-light: #ffffff;
    --color-gray: #333333;
    --color-light-gray: #eeeeee;

    /* Section Colors */
    --color-about-bg: #3498db;
    --color-gallery-bg: #000000;
    --color-contact-bg: #2ecc71;

    /* Spacing */
    --spacing-xs: 10px;
    --spacing-sm: 20px;
    --spacing-md: 40px;
    --spacing-lg: 60px;
    --spacing-xl: 100px;

    /* Animation Timing */
    --timing-fast: 0.3s;
    --timing-medium: 0.5s;
    --timing-slow: 0.8s;
    
    /* Z-index layers */
    --z-background: 0;
    --z-content: 5;
    --z-sections: 10;
    --z-navbar: 9999;
}

/* ===== BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Hanken Grotesk', sans-serif;
    background-color: var(--color-background);
    color: var(--color-text);
    overflow-x: hidden;
    scroll-padding-top: 80px;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Syncopate', sans-serif;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 0.5em;
}

p {
    margin-bottom: 1em;
    line-height: 1.6;
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--timing-fast) ease;
}

a:hover {
    color: var(--color-secondary);
}

/* ===== LAYOUT ===== */
.section {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    padding: 100px 40px;
    scroll-margin-top: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== NAVBAR ===== */
.navbar {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 160px);
    max-width: 1200px;
    height: 44px;
    background-color: var(--color-light);
    z-index: var(--z-navbar);
    padding: 0 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    overflow: visible;
}

.navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

/* Logo Styles */
.site-logo {
    height: 100%;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo-container {
    position: relative;
    padding: 12px;
    height: 100%;
    display: flex;
    align-items: center;
}

.logo-image {
    height: 30px;
    width: auto;
    transition: opacity 0.3s ease;
}

.logo-2 {
    position: absolute;
    left: 12px;
    opacity: 0;
}

.logo-container:hover .logo-1 {
    opacity: 0;
}

.logo-container:hover .logo-2 {
    opacity: 1;
}

/* Navigation Links */
.nav-links {
    display: flex;
    gap: 30px;
}

.nav-links a {
    color: var(--color-dark);
    text-decoration: none;
    font-size: 0.775em;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    position: relative;
    padding: 4px 8px;
    transition: all var(--timing-fast) ease;
}

.nav-links a:hover {
    color: var(--color-light);
}

.nav-links a::after {
    content: '';
    position: absolute;
    top: 0;
    left: -5px;
    width: calc(100% + 10px);
    height: 100%;
    background-color: var(--color-secondary);
    z-index: -1;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--timing-fast) ease;
}

.nav-links a:hover::after {
    transform: scaleX(1);
}

/* Mobile Menu */
.hamburger-icon {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    z-index: 1001;
}

.hamburger-icon span {
    display: block;
    width: 22px;
    height: 2px;
    background-color: var(--color-dark);
    margin: 4px 0;
    transition: all var(--timing-fast) ease-in-out;
}

/* ===== HERO SECTION ===== */
.hero-section {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background-color: var(--color-dark);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: left;
    max-width: 1200px;
    width: 100%;
    padding: 0 40px;
}

.hero-content h1 {
    font-size: clamp(2.5rem, 8vw, 6rem);
    margin-bottom: 20px;
    line-height: 1;
    color: var(--color-light);
}

.hero-content p {
    font-size: 1.25rem;
    margin-bottom: 30px;
    max-width: 600px;
}

/* ===== BUTTONS ===== */
.cta-button {
    display: inline-block;
    padding: 15px 30px;
    background-color: var(--color-primary);
    color: var(--color-light);
    text-decoration: none;
    border-radius: 0;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: all var(--timing-fast) ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: var(--color-light);
    transition: all var(--timing-medium) ease;
    z-index: -1;
}

.cta-button:hover {
    color: var(--color-dark);
}

.cta-button:hover::before {
    left: 0;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.action-buttons .action-widget-btn {
    width: 85% !important;
    padding: 12px 20px !important;
    margin: 0 !important;
    font-family: 'Hanken Grotesk', sans-serif !important;
    font-size: 1rem !important;
    font-weight: 700 !important;
    line-height: 1.4 !important;
    letter-spacing: 0.1em !important;
    text-transform: uppercase !important;
    text-align: center !important;
    text-decoration: none !important;
    border: none !important;
    border-radius: 0 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    display: block !important;
}

.action-buttons .action-pink {
    background-color: #FF42A1 !important;
    color: white !important;
}

.action-buttons .action-blue {
    background-color: #4A90E2 !important;
    color: white !important;
}

.action-buttons .action-widget-btn:hover {
    background-color: white !important;
    color: black !important;
}

/* ===== WIDGETS ===== */
.widget-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 12px;
    grid-auto-rows: minmax(140px, auto);
    width: 100%;
}

.widget {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0;
    padding: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.widget:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Widget Sizes */
.widget.large {
    grid-column: span 2;
    grid-row: span 2;
}

.widget.dominant {
    grid-column: span 3;
    grid-row: span 2;
}

.widget.extra-wide {
    grid-column: span 4;
    grid-row: span 2;
}

.widget.wide {
    grid-column: span 2;
}

.widget.tall {
    grid-row: span 2;
}

/* Widget Content */
.widget-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.widget-icon {
    display: none;
}

.widget-title {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    background: rgba(255, 255, 255, 0.15);
    padding: 6px 10px;
    border-radius: 0;
    display: inline-block;
    margin-bottom: 8px;
}

.widget-content {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.75rem;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: 0.02em;
    opacity: 0.9;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* ===== GALLERY ===== */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    width: 100%;
    gap: 12px;
    padding: 0;
    margin: 0;
    background-color: var(--color-dark);
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 0;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.1);
    aspect-ratio: 2/1;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateY(20px);
}

.gallery-item.revealed {
    opacity: 1;
    transform: translateY(0);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    opacity: 0;
    transition: opacity 0.3s ease-in;
}

.gallery-item.revealed img {
    opacity: 1;
}

/* Gallery item sizes */
.gallery-item.size-small {
    grid-row: span 1;
    grid-column: span 1;
}

.gallery-item.size-medium {
    grid-row: span 2;
    grid-column: span 1;
}

.gallery-item.size-large {
    grid-row: span 2;
    grid-column: span 2;
}

.gallery-item.size-wide {
    grid-row: span 1;
    grid-column: span 2;
}

.gallery-item.size-tall {
    grid-row: span 3;
    grid-column: span 1;
}

/* Hover effects */
.gallery-item:hover {
    transform: scale(1.02);
    border-color: var(--color-light);
}

.gallery-item .hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-primary);
    opacity: 0;
    transition: opacity var(--timing-fast) ease;
    z-index: 3;
}

.gallery-item:hover .hover-overlay {
    opacity: 0.5;
}

/* Modal styles */
.gallery-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 99999;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.gallery-modal.active {
    display: flex;
    opacity: 1;
}

.modal-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-image {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    opacity: 0;
    transform: scale(0.9);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.modal-image.active {
    opacity: 1;
    transform: scale(1);
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 30px;
    color: white;
    cursor: pointer;
    z-index: 10;
}

.modal-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-nav:hover {
    background: rgba(255, 255, 255, 0.4);
}

.modal-prev {
    left: 20px;
}

.modal-next {
    right: 20px;
}

/* ===== CONTACT FORM ===== */
.contact-section {
    padding: 100px 40px;
    background: linear-gradient(to bottom, #000000 0%, #3498db 100%);
    position: relative;
    min-height: 100vh;
    z-index: calc(var(--z-sections) + 2);
}

.contact-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: left;
    padding: 0 20px;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 0;
    font-family: inherit;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--color-primary);
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 1024px) {
    .widget.dominant,
    .widget.extra-wide {
        grid-column: span 2;
    }
}

@media (max-width: 768px) {
    .navbar {
        width: calc(100% - 40px);
        padding: 0 10px;
    }

    .hamburger-icon {
        display: block;
    }

    .nav-links {
        display: none;
        position: fixed;
        top: 84px;
        left: 0;
        right: 0;
        background-color: white;
        padding: 20px;
        flex-direction: column;
        gap: 15px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .nav-links.active {
        display: flex;
    }

    .hamburger-icon.active span:nth-child(1) {
        transform: translateY(6px) rotate(45deg);
    }

    .hamburger-icon.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger-icon.active span:nth-child(3) {
        transform: translateY(-6px) rotate(-45deg);
    }

    .section {
        padding: 80px 20px;
    }

    .widget {
        grid-column: 1 / -1 !important;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
    }

    .gallery-item.size-large,
    .gallery-item.size-wide {
        grid-column: span 1;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .cta-button {
        width: 100%;
        text-align: center;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

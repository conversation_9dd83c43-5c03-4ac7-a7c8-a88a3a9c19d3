/* Process Section */
.process-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
    color: #ffffff;
    position: relative;
    overflow: hidden;
}

.process-content {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 100px 40px;
    box-sizing: border-box;
    position: relative;
    z-index: 1;
}

.process-content h2 {
    font-family: 'Syncopate', sans-serif;
    font-size: clamp(1.8rem, 5vw, 4rem);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: -0.05em;
    line-height: 0.9;
    margin: 0 0 60px 0;
    color: #ffffff;
    text-align: left;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 40px;
}

.process-step {
    position: relative;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.process-step:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.step-number {
    font-family: 'Syncopate', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #FF42A1;
    margin-bottom: 15px;
    display: inline-block;
}

.process-step h3 {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 15px 0;
    color: #ffffff;
}

.process-step p {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1rem;
    line-height: 1.6;
    color: #cccccc;
    margin: 0;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .process-steps {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .process-steps {
        grid-template-columns: 1fr;
    }
    
    .process-content {
        padding: 80px 20px;
    }
    
    .process-step {
        padding: 25px;
    }
}

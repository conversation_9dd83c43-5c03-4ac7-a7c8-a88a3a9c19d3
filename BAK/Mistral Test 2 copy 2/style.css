/* CSS Variables for better maintainability */
:root {
    /* Colors */
    --color-background: #1a1a1a;
    --color-text: #ffffff;
    --color-primary: #8781bd; /* Purple accent color */
    --color-secondary: #00A2FF; /* Blue accent color */
    --color-dark: #000000;
    --color-light: #ffffff;
    --color-gray: #333333;
    --color-light-gray: #eeeeee;

    /* Section Colors */
    --color-about-bg: #3498db; /* Bright blue */
    --color-gallery-bg: #000000; /* Black */
    --color-contact-bg: #2ecc71; /* Bright green */

    /* Spacing */
    --spacing-xs: 10px;
    --spacing-sm: 20px;
    --spacing-md: 40px;
    --spacing-lg: 60px;
    --spacing-xl: 100px;

    /* Animation Timing */
    --timing-fast: 0.3s;
    --timing-medium: 0.5s;
    --timing-slow: 0.8s;

    /* Z-index layers */
    --z-background: 0;
    --z-content: 5;
    --z-sections: 10;
    --z-navbar: 9999;
}

/* Basic CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Typography styles moved to typography.css */

body {
    /* Font family moved to typography.css */
    background-color: var(--color-background);
    color: var(--color-text);
    overflow-x: hidden;
    scroll-padding-top: 80px; /* Adjust for fixed navbar */
}

/* Section Styles */
section {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    padding: 100px 40px;
    scroll-margin-top: 80px; /* Match navbar height */
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Consistent Section Headers */
.section-header {
    position: relative;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto 30px;
    padding: 0 20px;
    box-sizing: border-box;
}

.section-header h2,
.about-section h2 {
    font-family: 'Syncopate', sans-serif;
    font-size: clamp(1.8rem, 5vw, 4rem);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: -0.05em;
    line-height: 0.9;
    margin: 0 0 20px 0;
    color: #ffffff;
    max-width: 600px;
    opacity: 1;
    transform: none;
}

/* Remove the active state since we're not using the animation */

/* Fullscreen Section Styles */
.fullscreen-section {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Left align like gallery section */
    text-align: left; /* Changed from center to left */
}

.fullscreen-section .about-content,
.fullscreen-section .contact-content {
    display: flex;
    align-items: flex-start; /* Changed from center to flex-start */
    justify-content: flex-start; /* Match gallery section alignment */
    flex-direction: column;
    width: 100%;
    max-width: 1200px; /* Match gallery and navbar width */
    height: 100%;
    color: #ffffff; /* Ensure text is white in all sections */
    text-align: left; /* Changed from center to left */
    padding: 0 20px;
}

/* Gallery content needs different styling to accommodate the grid */
.fullscreen-section .gallery-content {
    display: flex;
    align-items: flex-start; /* Changed from center to flex-start */
    justify-content: flex-start;
    flex-direction: column;
    width: 100%;
    max-width: 1200px; /* Match navbar width */
    color: #ffffff;
    text-align: left; /* Changed from center to left */
    padding: 40px 20px;
}

.fullscreen-section h2 {
    color: #ffffff; /* Ensure headlines are white */
    margin-bottom: 30px;
    /* Removed opacity: 1 !important to allow animations */
}

.fullscreen-section p {
    color: #ffffff; /* Ensure paragraphs are white */
    margin-bottom: 30px;
    font-size: 0.775rem; /* Exact same as menu text */
    max-width: 600px;
    line-height: 1.2; /* Consistent leading */
    letter-spacing: 0.01em; /* Consistent kerning */
    font-weight: 700; /* Bold weight */
    /* Removed opacity: 1 !important to allow animations */
}

/* Global CTA button styles - exclude action widget buttons */
.cta-button:not(.action-widget-btn) {
    margin-top: 20px;
    background-color: #FF42A1; /* Pink background - hardcoded to keep it pink */
    color: #ffffff; /* White text */
    padding: 15px 30px; /* Normal button size */
    border-radius: 0; /* Sharp corners */
    text-decoration: none;
    font-family: 'Hanken Grotesk', sans-serif !important; /* Exactly matching subheadline font */
    font-weight: 700 !important; /* Bold weight */
    transition: all 0.3s ease;
    display: inline-block;
    font-size: 1.2rem !important; /* Matching the subheadline size */
    line-height: 1.4 !important; /* Matching subheadline line height */
    letter-spacing: 0.01em !important; /* Matching subheadline kerning */
    border: none;
    cursor: pointer;
    text-transform: uppercase; /* Match the uppercase style of the subheadline */
}

.cta-button:not(.action-widget-btn) {
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: #ffffff; /* White background on hover */
    transition: all 0.3s ease;
    z-index: -1;
}

.cta-button:not(.action-widget-btn):hover {
    color: #333333 !important; /* Black text on hover */
}

.cta-button:not(.action-widget-btn):hover::before {
    left: 0;
}

/* Section spacer */
.section-spacer {
    height: 30px;
    width: 100%;
}

/* Non-hero sections should cover the hero section */
section:not(#hero) {
    position: relative;
    z-index: 5; /* Higher than hero but lower than specific section z-indexes */
}

/* Hero Section */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

/* About Section */
.about-section {
    position: relative;
    overflow: hidden;
    z-index: var(--z-sections); /* Higher z-index to appear in front of hero section */
    background: linear-gradient(to bottom, #3498db 0%, #000000 100%) !important; /* Gradient from blue to black */
    transform: translateZ(0); /* Force hardware acceleration */
    padding: var(--spacing-xl) var(--spacing-md);
    min-height: 100vh; /* Full viewport height */
    display: flex;
    align-items: center;
}

/* Our Work Section (about-2) specific styles */
#about-2 {
    background: #000 !important; /* Solid black background */
    color: #fff;
}

/* Our Expertise Section (about-3) with flipped gradient */
#about-3 {
    background: linear-gradient(to top, #3498db 0%, #000000 100%) !important; /* Flipped gradient */
    color: #fff;
}

/* Ensure the headline in Our Expertise stays on one line */
#about-3 h2 {
    white-space: nowrap;
    display: inline-block;
}

/* Additional About Section Styles */

/* Gallery Section Styles */
.gallery-section {
    padding: var(--spacing-xl) var(--spacing-md); /* Restore horizontal padding */
    background-color: #000; /* Solid black */
    color: #fff; /* White text for better contrast */
    position: relative;
    z-index: calc(var(--z-sections) + 1); /* Higher than about section */
    transform: translateZ(0); /* Force hardware acceleration */
    min-height: 100vh; /* Ensure full viewport height */
    display: flex;
    align-items: center;
}

.gallery-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: left; /* Change to left-aligned */
}

/* Gallery content is responsive by default */

/* Gallery styles moved to gallery.css */

/* .about-content styles moved to about-widgets.css for widget layout */

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    height: 100%;
}

.about-text {
    display: flex;
    flex-direction: column;
    gap: 30px;
    height: 100%;
    justify-content: center;
}

.about-text h2 {
    /* Inherits size from global h2 styles */
    color: #2c3e50;
}

.about-text p {
    /* Font family moved to typography.css */
    color: #34495e;
    max-width: 600px; /* Match headline width constraint */
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    width: 100%;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-family: 'Hanken Grotesk', sans-serif; /* Using Hanken Grotesk as substitute for Armin Grotesk */
    font-size: 2.5rem;
    font-weight: 700;
    color: #3498db;
}

.stat-label {
    font-family: 'Hanken Grotesk', sans-serif; /* Using Hanken Grotesk as substitute for Armin Grotesk */
    font-size: 1rem;
    font-weight: 500;
    color: #7f8c8d;
    margin-top: 10px;
}

.about-image {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    height: 100%;
}

.about-image-main {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.about-image:hover .about-image-main {
    transform: scale(1.05);
}

.about-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
    transition: opacity 0.3s ease;
}

.about-image:hover .about-image-overlay {
    opacity: 0;
}

@media (max-width: 992px) {
    .about-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    /* About text h2 now uses global responsive sizing */

    .about-text p {
        font-size: 0.775rem; /* Exact same as menu text */
    }

    .about-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .about-section {
        padding: 60px 20px;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }
}

/* ===== NAVBAR STYLES ===== */
nav {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 160px);
    max-width: 1200px;
    height: 44px; /* Decreased from 52px to 44px (additional 8px reduction) */
    background-color: var(--color-light);
    z-index: var(--z-navbar); /* Increased z-index to ensure it stays above all elements */
    padding: 0 var(--spacing-sm);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    outline: none;
    border: none;
    box-sizing: content-box;
    overflow: visible;
}

.navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
}

.hamburger-icon {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px; /* Reduced padding */
    z-index: 1001;
}

.hamburger-icon span {
    display: block;
    width: 22px; /* Slightly smaller width */
    height: 2px; /* Thinner lines */
    background-color: var(--color-dark);
    margin: 4px 0; /* Reduced margin */
    transition: all var(--timing-fast) ease-in-out;
}

.site-logo {
    height: 100%;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo-container {
    position: relative;
    padding: 12px; /* Further reduced from 16px to fit even smaller navbar */
}

.logo-image {
    position: absolute;
    top: 50%;
    left: 0;
    height: 30px; /* Increased from 26px to 30px (4px increase) */
    width: auto;
    transform: translateY(-50%);
    transition: clip-path 0.25s cubic-bezier(0.4, 0, 0.05, 1);
}

.logo-1 {
    clip-path: inset(0 0 0 0);
}

.logo-2 {
    clip-path: inset(0 100% 0 0);
}

.logo-container:hover .logo-1 {
    clip-path: inset(0 0 0 100%);
}

.logo-container:hover .logo-2 {
    clip-path: inset(0 0 0 0);
}

/* Desktop Navigation */
@media (min-width: 769px) {
    .nav-links a {
        color: var(--color-dark);
        text-decoration: none;
        font-size: 0.775em; /* Decreased by approximately 2 points (from ~14.4px to ~12.4px) */
        position: relative;
        padding: 4px 8px;
        transition: color var(--timing-fast) cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover {
        color: var(--color-light);
    }

    .nav-links a::after {
        content: '';
        position: absolute;
        top: 0;
        left: -5px;
        width: calc(100% + 10px); /* Text width plus 5px on each side */
        height: 100%;
        background-color: var(--color-secondary);
        z-index: -1;
        transform: scaleX(0);
        transform-origin: left;
        transition: transform var(--timing-fast) cubic-bezier(0.4, 0, 0.05, 1);
    }

    .nav-links a:hover::after {
        transform: scaleX(1);
    }
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .hamburger-icon {
        display: block;
    }

    .nav-links {
        display: none;
        position: fixed;
        top: 44px; /* Updated to match new navbar height */
        left: 0;
        right: 0;
        background-color: #ffffff;
        padding: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        z-index: 9998; /* High z-index to ensure visibility */
    }

    .nav-links.active {
        display: block;
    }

    .hamburger-icon.active span:nth-child(1) {
        transform: translateY(6px) rotate(45deg); /* Adjusted for smaller spacing */
    }

    .hamburger-icon.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger-icon.active span:nth-child(3) {
        transform: translateY(-6px) rotate(-45deg); /* Adjusted for smaller spacing */
    }
}



/* Hero Section Styles */
.hero-section {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 0;
    overflow: hidden;
    position: relative;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
}

.hero-background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transform-origin: center;
    transform: scale(1);
}

.hero-slide.active .hero-background-image {
    animation: scaleUpHero 10s cubic-bezier(0.1, 0.05, 0.5, 1.0) forwards;
}

@keyframes scaleUpHero {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    90% {
        transform: scale(1.65);
    }
    100% {
        transform: scale(1.75);
    }
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(135, 129, 189, 0.85);
    z-index: 2;
}

.hero-content-container {
    position: absolute;
    top: 50%; /* Center vertically */
    left: 50%;
    transform: translate(-50%, -50%); /* Center both horizontally and vertically */
    width: calc(100% - 160px);
    max-width: 1200px;
    padding: 0;
    z-index: 10; /* Increase z-index to ensure visibility */
}

.hero-content {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 0; /* Remove logo container padding margin */
    max-width: 600px; /* Reduced from 800px to prevent headline from running too long */
    padding: 0;
    text-align: left;
    color: #ffffff;
    z-index: 10; /* Ensure it's above other elements */
}

/* Hero headline style already defined in the global styles */



.hero-content .subheadline {
    max-width: 600px; /* Match headline width constraint */
    margin-bottom: 25px; /* Space before button */
}

.hero-content .subheadline p {
    font-family: 'Hanken Grotesk', sans-serif; /* Using Hanken Grotesk as substitute for Armin Grotesk */
    font-size: 1.2rem; /* Restored larger font size */
    font-weight: 700; /* Bold weight */
    letter-spacing: 0.15em; /* Slightly looser kerning */
    line-height: 1.4; /* Line height */
    margin: 0 0 5px 0; /* Small gap between lines, no bottom margin on last item */
    display: block; /* Ensure each paragraph is on its own line */
}

/* Hero section CTA button with exact subheadline styling */
.hero-content .cta-button:not(.action-widget-btn) {
    display: inline-block;
    margin-top: 0; /* Removed margin since p already has margin-bottom */
    background-color: #FF42A1; /* Pink background - hardcoded to keep it pink */
    color: #ffffff; /* White text */
    padding: 15px 30px; /* Normal button size */
    border-radius: 0; /* Sharp corners */
    text-decoration: none;
    border: none;
    cursor: pointer;

    /* Exact subheadline font styling */
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.2rem; /* Matching the subheadline size */
    font-weight: 700;
    line-height: 1.4;
    letter-spacing: 0.15em;
    text-transform: uppercase;
}

/* Contact Section Styles */
.contact-section {
    position: relative;
    z-index: calc(var(--z-sections) + 2); /* Higher than gallery section */
    background-color: var(--color-contact-bg); /* Bright green */
    transform: translateZ(0); /* Force hardware acceleration */
    padding: var(--spacing-xl) var(--spacing-md);
}

/* Spacer for fixed navbar */
.sticky-navbar-spacer {
    height: 0; /* No need for spacer since we're using fixed positioning */
    width: 100%;
}/*
==============================================
TYPOGRAPHY STYLES
==============================================
*/

/* Global headline styles */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Syncopate', sans-serif;
    font-weight: 700;
    text-transform: uppercase;
}

/*
Responsive Typography with Clamp
--------------------------------
clamp(min, preferred, max)
- min: smallest size (for small screens)
- preferred: size that scales with viewport
- max: largest size (for large screens)
*/

/* Heading Styles */
h1, h2 {
    /* All headlines use the smaller size (previously used for section headings) */
    font-size: clamp(1.8rem, 5vw, 4rem);
    /* Consistent kerning */
    letter-spacing: -0.05em;
    /* Consistent leading */
    line-height: 0.9;
    margin-bottom: 25px; /* Adjusted for consistent spacing */
}

/* H3 - Subsection Headings */
h3 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.8rem, 5vw, 4rem);
    /* Consistent kerning */
    letter-spacing: -0.05em;
    /* Consistent leading */
    line-height: 0.9;
    margin-bottom: 25px;
}

/* H4 - Minor Headings */
h4 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.5rem, 4vw, 3.2rem);
    /* Consistent kerning */
    letter-spacing: -0.05em;
    /* Consistent leading */
    line-height: 0.9;
    margin-bottom: 20px;
}

/* H5 - Small Headings */
h5 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.3rem, 3vw, 2.5rem);
    /* Consistent kerning */
    letter-spacing: -0.05em;
    /* Consistent leading */
    line-height: 0.9;
    margin-bottom: 15px;
}

/* H6 - Smallest Headings */
h6 {
    /* Responsive sizing with clamp */
    font-size: clamp(1.1rem, 2vw, 2rem);
    /* Consistent kerning */
    letter-spacing: -0.05em;
    /* Consistent leading */
    line-height: 0.9;
    margin-bottom: 15px;
}

/* Body text styles */
body {
    font-family: 'Hanken Grotesk', sans-serif;
    line-height: 1.4; /* Tightened leading for body text */
}

/* Menu styles - base size for all text */
.nav-links a {
    font-family: 'Hanken Grotesk', sans-serif;
    font-weight: 500;
    font-size: 0.775rem; /* Exact menu text size */
    letter-spacing: 0.01em; /* Consistent kerning */
    line-height: 1.2; /* Consistent leading */
}

/* Paragraph and subheadline styles - same as menu text but bold */
p {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.775rem; /* Exact same as menu text */
    line-height: 1.2; /* Consistent leading */
    font-weight: 700; /* Bold weight */
    letter-spacing: 0.15em; /* Slightly looser kerning */
    margin-bottom: 20px;
}

/* Button styles - match subheadline */
button:not(.action-widget-btn),
.cta-button:not(.action-widget-btn) {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.2rem !important; /* Match subheadline size */
    font-weight: 700; /* Bold weight */
    letter-spacing: 0.15em; /* Match subheadline kerning */
    line-height: 1.4; /* Match subheadline line height */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .nav-links a,
    p {
        font-size: 0.775rem; /* Maintain consistent size */
        letter-spacing: 0.15em; /* Maintain slightly looser kerning on mobile */
    }

    button:not(.action-widget-btn),
    .cta-button:not(.action-widget-btn) {
        font-size: 1.2rem !important; /* Match subheadline size even on mobile */
    }
}
/*
==============================================
ANIMATIONS
==============================================
*/

/* Hero image scale-up animation */
@keyframes scaleUpHero {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.25);
    }
}

/* Slot animation styles - optimized for capital letters */
.slot-mask {
    display: inline-block;
    overflow: hidden;
    vertical-align: bottom;
    height: 1em; /* Optimized for capital letters */
    position: relative; /* Ensure proper stacking */
}

/* For headline */
.heading-mask,
h1 .slot-mask, h2 .slot-mask, h3 .slot-mask, h4 .slot-mask, h5 .slot-mask, h6 .slot-mask {
    height: 1em;
}

/* For subheadline */
.paragraph-mask,
p .slot-mask {
    height: 1em;
}

/* For button */
.button-mask {
    display: inline-block;
    overflow: hidden;
    vertical-align: bottom;
    height: auto; /* Auto height to accommodate any button size */
    margin-top: 0;
    margin-bottom: 0;
    position: relative; /* Ensure proper stacking */
}

/* Ensure button has no margins inside the mask */
.button-mask .cta-button:not(.action-widget-btn) {
    margin: 0;
    padding: 15px 30px; /* Normal button size */
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Add the hover effect to buttons in masks too */
.button-mask .cta-button:not(.action-widget-btn)::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    transition: all 0.3s ease;
    z-index: -1;
}

.button-mask .cta-button:not(.action-widget-btn):hover {
    color: #333333 !important;
}

.button-mask .cta-button:not(.action-widget-btn):hover::before {
    left: 0;
}

.slot-reel {
    display: block;
    transform: none; /* Remove initial transform to prevent flicker */
    line-height: inherit;
    position: relative; /* Ensure proper stacking */
    will-change: transform; /* Optimize for transform animations */
    opacity: 0; /* Start invisible */
    animation: slotSpin 0.8s cubic-bezier(0.19, 1, 0.22, 1) forwards;
}

/* Initial slot animation */
@keyframes slotSpin {
    0% {
        transform: translateY(700%);
        opacity: 0;
    }
    15% {
        transform: translateY(500%);
        opacity: 1;
    }
    30% {
        transform: translateY(300%);
    }
    45% {
        transform: translateY(100%);
    }
    60% {
        transform: translateY(-50%);
    }
    75% {
        transform: translateY(-20%);
    }
    85% {
        transform: translateY(-10%);
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* About Widgets CSS - Exact copy from about-widgets.html */

/* About Content Container - Match gallery section exactly */
.about-section .about-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: left;
}

/* Widget Grid */
.widget-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 12px;
    grid-auto-rows: minmax(140px, auto);
    width: 100%;
}

/* Widget Base Styles */
.widget {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.widget:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Widget Sizes */
.widget.large {
    grid-column: span 2;
    grid-row: span 2;
}

.widget.dominant {
    grid-column: span 3;
    grid-row: span 2;
}

.widget.extra-wide {
    grid-column: span 4;
    grid-row: span 2;
}

.widget.wide {
    grid-column: span 2;
}

.widget.tall {
    grid-row: span 2;
}

/* Widget Content */
.widget-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.widget-icon {
    display: none;
}

.widget-title {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    background: rgba(255, 255, 255, 0.15);
    padding: 6px 10px;
    border-radius: 4px;
    display: inline-block;
    margin-bottom: 8px;
}

.widget-content {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.75rem;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: 0.02em;
    opacity: 0.9;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.widget-number {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 4px;
    background: linear-gradient(45deg, #ffffff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.widget-label {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.65rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    opacity: 0.8;
    line-height: 1.2;
}

/* Specific Widget Styles */
.team-widget {
    background: rgba(255, 255, 255, 0.1);
}

.experience-widget {
    background: rgba(255, 255, 255, 0.1);
}

.projects-widget {
    background: rgba(255, 255, 255, 0.1);
}

.mission-widget {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
}

.mission-widget .widget-title {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

.mission-widget .widget-content {
    color: #ffffff;
}

/* Section Intro Animations - Only for about section widgets */
.about-section .widget {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.7s ease-out, transform 0.7s ease-out;
}

/* Only apply animations when section has 'active' class */
.about-section.active .widget:nth-child(1) { opacity: 1; transform: scale(1); transition-delay: 0.1s; }
.about-section.active .widget:nth-child(2) { opacity: 1; transform: scale(1); transition-delay: 0.2s; }
.about-section.active .widget:nth-child(3) { opacity: 1; transform: scale(1); transition-delay: 0.3s; }
.about-section.active .widget:nth-child(4) { opacity: 1; transform: scale(1); transition-delay: 0.4s; }
.about-section.active .widget:nth-child(5) { opacity: 1; transform: scale(1); transition-delay: 0.5s; }
.about-section.active .widget:nth-child(6) { opacity: 1; transform: scale(1); transition-delay: 0.6s; }
.about-section.active .widget:nth-child(7) { opacity: 1; transform: scale(1); transition-delay: 0.7s; }
.about-section.active .widget:nth-child(8) { opacity: 1; transform: scale(1); transition-delay: 0.8s; }

@keyframes scaleInFade {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.skills-widget {
    background: rgba(255, 255, 255, 0.1);
}

.vision-widget {
    background: rgba(255, 255, 255, 0.1);
}

.vibe-widget {
    background: rgba(255, 255, 255, 0.1);
}

.aura-widget {
    background: rgba(255, 255, 255, 0.1);
}

.map-widget {
    background: rgba(255, 255, 255, 0.1);
}

.action-widget {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
}

.action-widget .widget-title {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

.action-widget .widget-content {
    color: #ffffff;
}

/* Skill Tags */
.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
}

.skill-tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 0;
    font-family: 'Inter', sans-serif;
    font-size: 0.65rem;
    font-weight: 700;
}

/* Progress Bars */
.progress-item {
    margin-bottom: 8px;
}

.progress-label {
    font-family: 'Inter', sans-serif;
    font-size: 0.65rem;
    font-weight: 700;
    margin-bottom: 3px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ffffff, rgba(255, 255, 255, 0.8));
    border-radius: 3px;
    transition: width 1s ease;
}

/* Vibe Check Styles */
.vibe-simple {
    text-align: center;
}

.vibe-simple .widget-label {
    margin-bottom: 12px;
}

.vibe-status-row {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
}

.status-options {
    display: flex;
    gap: 12px;
}

.status-option {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.1rem;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 4px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.status-option.active {
    opacity: 1;
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    text-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
}

.status-indicator-large {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ff4444;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.status-indicator-large.on {
    background: #00ff88;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
    animation: sineWave 4s infinite ease-in-out !important;
    position: relative;
    overflow: hidden;
}

.status-indicator-large.on::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: sineWaveFlow 6s infinite;
}

@keyframes sineWave {
    0% {
        transform: scale(1) translateY(0px);
        box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
    }
    25% {
        transform: scale(1.1) translateY(-1px);
        box-shadow: 0 0 25px rgba(0, 255, 136, 0.8);
    }
    50% {
        transform: scale(1) translateY(0px);
        box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
    }
    75% {
        transform: scale(0.9) translateY(1px);
        box-shadow: 0 0 15px rgba(0, 255, 136, 0.4);
    }
    100% {
        transform: scale(1) translateY(0px);
        box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
    }
}

@keyframes sineWaveFlow {
    0% { left: -100%; }
    100% { left: 100%; }
}

.status-text {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.1rem;
    font-weight: 700;
    color: #00ff88;
    text-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
}

/* Aura Meter Styles */
.aura-meter {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 16px;
}

.aura-bar {
    width: 100%;
    height: 14px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 0;
    overflow: hidden;
    position: relative;
}

.aura-fill {
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7);
    border-radius: 0;
    position: relative;
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.aura-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shine 2s infinite;
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.aura-percentage {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.3rem;
    font-weight: 700;
    text-align: center;
    background: linear-gradient(45deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: rainbow 3s infinite;
    line-height: 1;
}

@keyframes rainbow {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(180deg); }
}

/* World Map Styles */
.world-map {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.map-svg {
    width: 100%;
    height: 120px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0;
    padding: 8px;
}

.location-marker {
    cursor: pointer;
    filter: drop-shadow(0 0 6px currentColor);
}

.location-marker:hover {
    filter: drop-shadow(0 0 12px currentColor);
}

/* Timezone Clocks */
.timezone-clocks {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 8px;
}

.clock-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.team-count {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.2rem;
    font-weight: 700;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
}

.clock-face {
    width: 60px;
    height: 60px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    position: relative;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.clock-face::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 4px;
    background: #ffffff;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
    box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
}

.clock-ticks {
    position: absolute;
    width: 100%;
    height: 100%;
}

.clock-tick {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
}

.clock-face.nighttime {
    background: rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.2);
}

.clock-face.nighttime .clock-tick {
    background: rgba(255, 255, 255, 0.4);
}

.clock-face.nighttime .clock-hand {
    background: rgba(255, 255, 255, 0.8);
}

.clock-hand {
    position: absolute;
    background: #ffffff;
    transform-origin: bottom center;
    border-radius: 2px;
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

.hour-hand {
    width: 2px;
    height: 18px;
    top: 12px;
    left: 50%;
    margin-left: -1px;
    z-index: 2;
}

.minute-hand {
    width: 1px;
    height: 24px;
    top: 6px;
    left: 50%;
    margin-left: -0.5px;
    z-index: 1;
}

.clock-label {
    text-align: center;
}

.city-name {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.75rem;
    font-weight: 700;
    margin-bottom: 1px;
}

.time-display {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.55rem;
    font-weight: 700;
    opacity: 0.9;
}

/* Pie Chart Styles */
.pie-chart {
    position: relative;
    width: 200px;
    height: 200px;
    flex-shrink: 0;
}

.pie-chart-inner {
    position: relative;
    width: 200px;
    height: 200px;
}

.pie-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

/* Initial state for pie chart slices */
.pie-svg circle {
    stroke-dasharray: 0 251.2;
    opacity: 0;
    transition: all 0.8s ease-out;
}

/* Animate slices when section is active */
.about-section.active .pie-svg circle {
    animation: pieSliceGrow 1.5s ease-out forwards;
    opacity: 1;
}

.about-section.active .pie-svg circle:nth-child(2) { animation-delay: 0.1s; }
.about-section.active .pie-svg circle:nth-child(3) { animation-delay: 0.2s; }
.about-section.active .pie-svg circle:nth-child(4) { animation-delay: 0.3s; }
.about-section.active .pie-svg circle:nth-child(5) { animation-delay: 0.4s; }
.about-section.active .pie-svg circle:nth-child(6) { animation-delay: 0.5s; }
.about-section.active .pie-svg circle:nth-child(7) { animation-delay: 0.6s; }
.about-section.active .pie-svg circle:nth-child(8) { animation-delay: 0.7s; }

@keyframes pieSliceGrow {
    from {
        stroke-dasharray: 0 251.2;
    }
    to {
        stroke-dasharray: var(--final-dash-array);
    }
}

.pie-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 2.8rem;
    font-weight: 700;
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
    transition: all 0.8s ease-out;
}

.about-section.active .pie-center {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Team Breakdown Layout */
.team-breakdown-layout {
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: center;
}

.pie-legend {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 8px;
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.75rem;
    font-weight: 700;
    flex: 1;
    text-align: left;
}

.pie-chart {
    display: flex;
    align-items: center;
    justify-content: center;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 3px;
    flex-shrink: 0;
}

/* Bar Chart Styles */
.bar-chart {
    display: flex;
    align-items: end;
    gap: 6px;
    height: 120px;
    padding: 8px 0;
}

.bar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
}

.bar-container {
    width: 100%;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    display: flex;
    align-items: end;
    margin: 4px 0;
}

.bar-fill {
    width: 100%;
    border-radius: 2px;
    transition: height 1s ease;
    animation: barGrow 1s ease;
}

@keyframes barGrow {
    from { height: 0%; }
    to { height: var(--final-height); }
}

.bar-label {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.5rem;
    font-weight: 700;
    text-align: center;
    writing-mode: vertical-rl;
    text-orientation: mixed;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bar-value {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.6rem;
    font-weight: 700;
    margin-top: 2px;
}

/* Expertise Grid */
.expertise-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.expertise-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.expertise-checkbox {
    width: 16px;
    height: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 700;
    color: #00ff88;
    flex-shrink: 0;
}

.expertise-text {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.75rem;
    font-weight: 700;
    line-height: 1.2;
}

/* ARTCHOP Widget Layout */
.mission-widget .widget-content {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.75rem;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: 0.01em;
}

/* Override global paragraph styles for ALL widget content */
.widget .widget-content p {
    font-family: 'Hanken Grotesk', sans-serif !important;
    font-size: 0.75rem !important;
    font-weight: 400 !important;
    line-height: 1.6 !important;
    letter-spacing: 0.02em !important;
    opacity: 0.9 !important;
    margin-bottom: 0 !important;
    text-transform: none !important;
}

/* ARTCHOP widget gets larger font size */
.mission-widget .widget-content p {
    font-size: 1.1rem !important;
}



.artchop-content {
    display: flex;
    gap: 24px;
    align-items: flex-start;
}

.artchop-text {
    flex: 2;
    min-width: 0;
}

.artchop-visual {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 200px;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 12px;
    border-radius: 0;
    text-align: center;
}

.stat-number {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 4px;
    background: linear-gradient(45deg, #ffffff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.65rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    opacity: 0.8;
}

.artchop-logo {
    background: rgba(255, 255, 255, 0.1);
    padding: 16px;
    border-radius: 0;
    text-align: center;
}

.logo-text {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 4px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-tagline {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    opacity: 0.8;
}

/* Technology Logos */
.tech-logos {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 8px;
}

.tech-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0;
    transition: all 0.3s ease;
}

.tech-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.tech-logo {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.8rem;
    font-weight: 700;
    flex-shrink: 0;
}

.tech-name {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 0.75rem;
    font-weight: 700;
    line-height: 1;
}

/* Individual Logo Colors */
.unity-logo { background: #000000; color: #ffffff; }
.unreal-logo { background: #0e1128; color: #ffffff; }
.shader-logo { background: #4a90e2; color: #ffffff; }
.substance-painter-logo { background: #f57c00; color: #ffffff; }
.substance-designer-logo { background: #e65100; color: #ffffff; }
.marvelous-logo { background: #8e24aa; color: #ffffff; }
.zbrush-logo { background: #d32f2f; color: #ffffff; }
.maya-logo { background: #37a5cc; color: #ffffff; }

/* Action Buttons Widget - Completely clean base */
.action-widget .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 10px 0;
    /* Prevent any animations */
    animation: none !important;
    transition: none !important;
}

/* Button styles */
#about .action-widget-btn {
    background-color: #FF42A1 !important;
    color: white !important;
    font-size: 0.9rem !important;
    padding: 12px 20px !important;
    margin: 0 auto 12px !important;
    width: 85% !important;
    display: block !important;
    border: 2px solid #FF42A1 !important;
    border-radius: 0 !important;
    text-align: center !important;
    text-decoration: none !important;
    font-family: 'Hanken Grotesk', sans-serif !important;
    font-weight: 700 !important;
    line-height: 1.4 !important;
    letter-spacing: 0.1em !important;
    text-transform: uppercase !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    z-index: 1 !important;
    overflow: hidden !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

/* Blue button */
#about .action-blue {
    background-color: #4A90E2 !important;
    border-color: #4A90E2 !important;
    margin-bottom: 0 !important;
}

/* Hover effect */
#about .action-widget-btn:hover {
    background-color: white !important;
    color: black !important;
    border-color: white !important;
}

/* Action Buttons - Simple */
.action-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.action-btn {
    padding: 9px 18px; /* Reduced from 15px 30px to make button 6px larger than text */
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.2rem;
    font-weight: 700;
    text-transform: uppercase;
    border: none;
    cursor: pointer;
    color: white !important;
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: color 0.3s ease 0.1s !important; /* Slight delay for text color change */
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: white;
    z-index: -1;
    transition: width 0.3s cubic-bezier(0.4, 0, 0.05, 1);
}

.action-btn:hover::before {
    width: 100%;
}

.action-pink {
    background: #FF42A1 !important;
    color: white !important;
}

.action-blue {
    background: #4A90E2 !important;
    color: white !important;
}

/* High specificity hover rule */
body #about .widget.action-widget .action-buttons .action-btn:hover,
body #about .about-section .widget.action-widget .action-buttons .action-btn:hover {
    color: black !important;
}

body #about .widget.action-widget .action-buttons .action-btn:hover::before,
body #about .about-section .widget.action-widget .action-buttons .action-btn:hover::before {
    width: 100%;
}

/* Action Widget Buttons - Very Specific and Forceful Styles */
body #about .widget.action-widget .action-widget-btn,
body #about .about-section .widget.action-widget .action-widget-btn,
.about-section .widget.action-widget .action-widget-btn,
.widget.action-widget .action-widget-btn {
    all: unset !important; /* Reset all styles */
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    display: block !important;
    width: 85% !important;
    margin: 0 auto 12px !important;
    padding: 12px 20px !important;
    text-align: center !important;
    text-decoration: none !important;
    font-family: 'Hanken Grotesk', sans-serif !important;
    font-weight: 700 !important;
    font-size: 1rem !important;
    line-height: 1.4 !important;
    letter-spacing: 0.1em !important;
    text-transform: uppercase !important;
    border: none !important;
    border-radius: 0 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    z-index: 1 !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
}

/* Remove margin from the last button */
body #about .widget.action-widget .action-widget-btn.last-button,
body #about .about-section .widget.action-widget .action-widget-btn.last-button,
.about-section .widget.action-widget .action-widget-btn.last-button,
.widget.action-widget .action-widget-btn.last-button,
body #about .widget.action-widget .action-widget-btn:last-child,
body #about .about-section .widget.action-widget .action-widget-btn:last-child,
.about-section .widget.action-widget .action-widget-btn:last-child,
.widget.action-widget .action-widget-btn:last-child {
    margin-bottom: 0 !important;
}

/* Pink button style - Very specific and forceful */
body #about .widget.action-widget .action-pink,
body #about .about-section .widget.action-widget .action-pink,
.about-section .widget.action-widget .action-pink,
.widget.action-widget .action-pink {
    background-color: #FF42A1 !important;
    color: #ffffff !important;
}

/* Blue button style - Very specific and forceful */
body #about .widget.action-widget .action-blue,
body #about .about-section .widget.action-widget .action-blue,
.about-section .widget.action-widget .action-blue,
.widget.action-widget .action-blue {
    background-color: #4A90E2 !important;
    color: #ffffff !important;
    margin-bottom: 0 !important;
}

/* Hover effect for both buttons - Very specific and forceful */
body #about .widget.action-widget .action-widget-btn:hover,
body #about .about-section .widget.action-widget .action-widget-btn:hover,
.about-section .widget.action-widget .action-widget-btn:hover,
.widget.action-widget .action-widget-btn:hover {
    color: #000000 !important;
    background-color: #ffffff !important;
}

/* Pink button - using ID and !important to ensure it overrides other styles */
#about .action-pink,
.about-section .action-pink,
.widget.action-widget .action-pink {
    background-color: #FF42A1 !important; /* Pink */
    color: #FFFFFF !important; /* White text */
    border: none !important;
}

/* Blue button - using ID and !important to ensure it overrides other styles */
#about .action-blue,
.about-section .action-blue,
.widget.action-widget .action-blue {
    background-color: #4A90E2 !important; /* Blue */
    color: #FFFFFF !important; /* White text */
    border: none !important;
}

/* Hover states */
#about .action-pink:hover,
.about-section .action-pink:hover,
.widget.action-widget .action-pink:hover,
#about .action-blue:hover,
.about-section .action-blue:hover,
.widget.action-widget .action-blue:hover {
    background-color: #FFFFFF !important; /* White */
    color: #000000 !important; /* Black text */
    border: none !important;
}

/* Ensure no animations from parent elements */
.no-animation,
.no-animation * {
    animation: none !important;
    transition: none !important;
    transform: none !important;
    opacity: 1 !important;
}

/* Responsive for ARTCHOP widget */
@media (max-width: 1024px) {
    .artchop-content {
        flex-direction: column;
        gap: 16px;
    }

    .artchop-visual {
        min-width: auto;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .widget-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .widget.large,
    .widget.wide,
    .widget.dominant,
    .widget.extra-wide {
        grid-column: span 1;
    }

    .widget.large,
    .widget.dominant,
    .widget.extra-wide {
        grid-row: span 1;
    }
}

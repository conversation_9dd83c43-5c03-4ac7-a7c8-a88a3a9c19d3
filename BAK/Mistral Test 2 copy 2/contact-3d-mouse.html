<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Section with Mouse-Controlled 3D Background</title>
    <!-- Include Three.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Hanken Grotesk', sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            overflow-x: hidden;
            cursor: default;
        }

        /* Typography */
        h2 {
            font-family: 'Syncopate', sans-serif;
            font-weight: 700;
            text-transform: uppercase;
            font-size: clamp(1.8rem, 5vw, 4rem);
            letter-spacing: -0.05em;
            line-height: 0.9;
            margin-bottom: 25px;
        }

        /* 3D Background Canvas */
        #bg-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        /* Contact Section */
        .contact-section {
            position: relative;
            min-height: 100vh;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 100px 40px;
            z-index: 1;
        }

        .contact-content {
            max-width: 600px;
            text-align: left;
        }

        /* Mouse position indicator (optional) */
        .mouse-position {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            color: white;
            z-index: 100;
        }
    </style>
</head>
<body>
    <!-- 3D Background Canvas -->
    <canvas id="bg-canvas"></canvas>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="contact-content">
            <h2>CONTACT</h2>
        </div>
    </section>

    <!-- Mouse position indicator (optional) -->
    <div class="mouse-position">Move your mouse to control the scene</div>

    <script>
        // Three.js setup
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({
            canvas: document.getElementById('bg-canvas'),
            antialias: true,
            alpha: true
        });
        
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        
        // Set background color (green with some transparency)
        scene.background = new THREE.Color(0x2ecc71);
        scene.fog = new THREE.FogExp2(0x2ecc71, 0.02);
        
        // Camera position
        camera.position.set(0, 0, 30);
        
        // Create a group to hold all objects
        const group = new THREE.Group();
        scene.add(group);
        
        // Create particles
        const particlesGeometry = new THREE.BufferGeometry();
        const particlesCount = 2000;
        
        const posArray = new Float32Array(particlesCount * 3);
        
        // Fill arrays with random positions
        for (let i = 0; i < particlesCount * 3; i += 3) {
            // Position
            posArray[i] = (Math.random() - 0.5) * 100;
            posArray[i + 1] = (Math.random() - 0.5) * 100;
            posArray[i + 2] = (Math.random() - 0.5) * 100;
        }
        
        particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
        
        // Material
        const particlesMaterial = new THREE.PointsMaterial({
            size: 0.2,
            color: 0xffffff,
            transparent: true,
            opacity: 0.8,
            sizeAttenuation: true
        });
        
        // Create points
        const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
        group.add(particlesMesh);
        
        // Add some geometric shapes
        const geometries = [
            new THREE.IcosahedronGeometry(5, 0),
            new THREE.OctahedronGeometry(5, 0),
            new THREE.TetrahedronGeometry(5, 0),
            new THREE.BoxGeometry(5, 5, 5),
            new THREE.TorusGeometry(5, 1, 16, 100)
        ];
        
        const material = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            wireframe: true,
            transparent: true,
            opacity: 0.3
        });
        
        const meshes = [];
        
        for (let i = 0; i < 15; i++) {
            const geometry = geometries[Math.floor(Math.random() * geometries.length)];
            const mesh = new THREE.Mesh(geometry, material);
            
            mesh.position.x = (Math.random() - 0.5) * 80;
            mesh.position.y = (Math.random() - 0.5) * 80;
            mesh.position.z = (Math.random() - 0.5) * 80;
            
            mesh.rotation.x = Math.random() * Math.PI;
            mesh.rotation.y = Math.random() * Math.PI;
            
            mesh.scale.setScalar(Math.random() * 0.5 + 0.5);
            
            group.add(mesh);
            meshes.push(mesh);
        }
        
        // Mouse movement tracking
        let mouseX = 0;
        let mouseY = 0;
        let targetX = 0;
        let targetY = 0;
        
        // Track mouse position
        document.addEventListener('mousemove', (event) => {
            // Calculate normalized mouse position (-1 to 1)
            mouseX = (event.clientX / window.innerWidth) * 2 - 1;
            mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
        });
        
        // For touch devices
        document.addEventListener('touchmove', (event) => {
            if (event.touches.length > 0) {
                // Calculate normalized touch position (-1 to 1)
                mouseX = (event.touches[0].clientX / window.innerWidth) * 2 - 1;
                mouseY = -(event.touches[0].clientY / window.innerHeight) * 2 + 1;
                
                // Prevent scrolling
                event.preventDefault();
            }
        }, { passive: false });
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            // Smooth camera movement
            targetX = mouseX * 10;
            targetY = mouseY * 10;
            
            // Move camera based on mouse position with smooth easing
            camera.position.x += (targetX - camera.position.x) * 0.05;
            camera.position.y += (targetY - camera.position.y) * 0.05;
            camera.lookAt(scene.position);
            
            // Rotate meshes slightly
            meshes.forEach((mesh, i) => {
                mesh.rotation.x += 0.001 * (i % 2 ? 1 : -1);
                mesh.rotation.y += 0.001 * (i % 3 ? 1 : -1);
            });
            
            // Subtle particle rotation
            particlesMesh.rotation.y += 0.0005;
            
            renderer.render(scene, camera);
        }
        
        animate();
        
        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // Update mouse position indicator (optional)
        const mousePositionElement = document.querySelector('.mouse-position');
        setInterval(() => {
            mousePositionElement.textContent = `Mouse: X: ${mouseX.toFixed(2)}, Y: ${mouseY.toFixed(2)}`;
        }, 100);
    </script>
</body>
</html>

/* ===== components.css ===== */
/* Reusable Components */

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-decoration: none;
    transition: all var(--timing-fast) ease;
    cursor: pointer;
    border: 2px solid transparent;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background-color: var(--color-primary);
    color: white;
    border-color: var(--color-primary);
}

.btn-primary:hover {
    background-color: var(--color-secondary);
    border-color: var(--color-secondary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--color-light);
    color: var(--color-light);
}

.btn-outline:hover {
    background-color: var(--color-light);
    color: var(--color-dark);
    transform: translateY(-2px);
}

/* Cards */
.card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: var(--spacing-md);
    transition: transform var(--timing-fast) ease, box-shadow var(--timing-fast) ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Forms */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    color: var(--color-light);
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1rem;
    transition: border-color var(--timing-fast) ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--color-primary);
}

textarea.form-control {
    min-height: 150px;
    resize: vertical;
}

/* Alerts */
.alert {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 4px;
    margin-bottom: var(--spacing-md);
    font-size: 0.9rem;
}

.alert-success {
    background-color: rgba(46, 204, 113, 0.2);
    border: 1px solid #2ecc71;
    color: #2ecc71;
}

.alert-error {
    background-color: rgba(231, 76, 60, 0.2);
    border: 1px solid #e74c3c;
    color: #e74c3c;
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--color-light);
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: var(--color-dark);
    color: var(--color-light);
    text-align: center;
    border-radius: 4px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity var(--timing-fast) ease;
    font-size: 0.8rem;
    line-height: 1.4;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    opacity: 0;
    transition: opacity var(--timing-medium) ease;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.modal-content {
    background-color: var(--color-light);
    padding: var(--spacing-md);
    border-radius: 8px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    color: var(--color-dark);
}

.modal-close {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--color-dark);
    background: none;
    border: none;
    padding: 0;
    line-height: 1;
}

/* Responsive Components */
@media (max-width: 768px) {
    .btn {
        padding: 10px 20px;
        font-size: 0.85rem;
    }
    
    .card {
        padding: var(--spacing-sm);
    }
    
    .modal-content {
        width: 95%;
        padding: var(--spacing-sm);
    }
}

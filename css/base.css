/* ===== base.css ===== */
/* CSS Variables and Base Styles */
:root {
    /* Colors */
    --color-background: #1a1a1a;
    --color-text: #ffffff;
    --color-primary: #8781bd;
    --color-secondary: #FF42A1;
    --color-dark: #000000;
    --color-light: #ffffff;
    --color-gray: #333333;
    --color-light-gray: #eeeeee;

    /* Section Colors */
    --color-about-bg: #3498db;
    --color-gallery-bg: #000000;
    --color-contact-bg: #2ecc71;

    /* Spacing */
    --spacing-xs: 10px;
    --spacing-sm: 20px;
    --spacing-md: 40px;
    --spacing-lg: 60px;
    --spacing-xl: 100px;

    /* Animation Timing */
    --timing-fast: 0.3s;
    --timing-medium: 0.5s;
    --timing-slow: 0.8s;
    
    /* Z-index layers */
    --z-background: 0;
    --z-content: 5;
    --z-sections: 10;
    --z-navbar: 9999;
}

/* Base Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Hanken Grotesk', sans-serif;
    line-height: 1.6;
    color: var(--color-text);
    background-color: var(--color-background);
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Syncopate', sans-serif;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--timing-fast) ease;
}

a:hover {
    color: var(--color-secondary);
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Layout */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
}

section {
    padding: var(--spacing-xl) 0;
    position: relative;
}

/* Navbar */
.navbar {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 160px);
    max-width: 1200px;
    height: 60px;
    background-color: var(--color-light);
    z-index: var(--z-navbar);
    padding: 0 var(--spacing-sm);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    height: 40px;
    width: auto;
}

.nav-links {
    display: flex;
    gap: var(--spacing-md);
}

.nav-link {
    color: var(--color-dark);
    text-decoration: none;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    position: relative;
    padding: 5px 0;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--color-primary);
    transition: width var(--timing-fast) ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--color-light);
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 var(--spacing-sm);
}

.hero h1 {
    font-size: 15em;
    margin-bottom: var(--spacing-sm);
    line-height: 0.9;
}

.hero p {
    font-size: 1.5em;
    margin-bottom: var(--spacing-lg);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-decoration: none;
    transition: all var(--timing-fast) ease;
    cursor: pointer;
    border: none;
    font-size: 0.9rem;
}

.btn-primary {
    background-color: var(--color-primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--color-secondary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.section-header h2 {
    font-size: 3em;
    margin-bottom: var(--spacing-sm);
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--color-primary);
}

/* Footer */
.footer {
    background-color: var(--color-dark);
    color: var(--color-light);
    padding: var(--spacing-lg) 0;
    text-align: center;
}

.footer p {
    margin: 0;
    font-size: 0.9rem;
}

/* Responsive Base */
@media (max-width: 768px) {
    .navbar {
        width: calc(100% - 40px);
        padding: 0 var(--spacing-xs);
    }
    
    .hero h1 {
        font-size: 8em;
    }
    
    .hero p {
        font-size: 1.2em;
    }
    
    .section-header h2 {
        font-size: 2.5em;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 5em;
    }
    
    .hero p {
        font-size: 1em;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.8rem;
    }
}

/* ===== gallery.css ===== */
/* Gallery Specific Styles */

.gallery-section {
    background-color: var(--color-gallery-bg);
    padding: var(--spacing-xl) 0;
    position: relative;
    overflow: hidden;
}

.gallery-container {
    position: relative;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    aspect-ratio: 1 / 1.5;
    cursor: pointer;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.gallery-item.revealed {
    opacity: 1;
    transform: translateY(0);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 50%);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: var(--spacing-md);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-item-overlay {
    opacity: 1;
}

.gallery-item-title {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 5px;
    transform: translateY(20px);
    transition: transform 0.3s ease;
    font-weight: 600;
}

.gallery-item-category {
    color: var(--color-primary);
    font-size: 0.9rem;
    transform: translateY(20px);
    transition: transform 0.3s ease 0.1s;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

.gallery-item:hover .gallery-item-title,
.gallery-item:hover .gallery-item-category {
    transform: translateY(0);
}

/* Modal Styles */
.gallery-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.gallery-modal.active {
    opacity: 1;
    pointer-events: all;
}

.modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.modal-image-container {
    position: relative;
    max-height: 80vh;
    overflow: hidden;
    border-radius: 8px;
}

.modal-image {
    max-width: 100%;
    max-height: 80vh;
    display: block;
    margin: 0 auto;
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    background: none;
    border: none;
    z-index: 2;
}

.modal-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 2;
}

.modal-nav-button {
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.5rem;
    transition: background 0.3s ease;
}

.modal-nav-button:hover {
    background: rgba(0, 0, 0, 0.8);
}

.modal-caption {
    color: white;
    text-align: center;
    margin-top: var(--spacing-sm);
    padding: 0 var(--spacing-md);
}

/* Loading Animation */
.gallery-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: var(--color-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Gallery */
@media (max-width: 768px) {
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--spacing-sm);
    }
    
    .modal-nav-button {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .modal-close {
        top: 10px;
        right: 10px;
        font-size: 1.5rem;
    }
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.6s ease forwards;
}

.animate-slide-up {
    animation: slideUp 0.6s ease forwards;
}

.animate-slide-down {
    animation: slideDown 0.6s ease forwards;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from { 
        opacity: 0;
        transform: translateY(-20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

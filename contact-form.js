// Clean, simple contact form handler
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contact-form');
    if (!form) return;

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const button = form.querySelector('button[type="submit"]');
        const originalText = button.textContent;

        // Show loading state
        button.textContent = 'Sending...';
        button.disabled = true;

        // Get form data
        const formData = new FormData(form);

        // Submit to FormSubmit
        fetch(form.action, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                showMessage('✅ Message sent successfully! We\'ll get back to you soon.', 'success');
                form.reset();
            } else {
                throw new Error('Network error');
            }
        })
        .catch(error => {
            showMessage('❌ Error sending message. Please try again.', 'error');
        })
        .finally(() => {
            button.textContent = originalText;
            button.disabled = false;
        });
    });

    function showMessage(text, type) {
        // Remove existing message
        const existing = document.querySelector('.form-message');
        if (existing) existing.remove();

        // Create new message
        const msg = document.createElement('div');
        msg.className = 'form-message';
        msg.textContent = text;
        msg.style.cssText = `
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
            ${type === 'success' ?
                'background: rgba(46, 204, 113, 0.1); color: #27ae60; border: 1px solid #27ae60;' :
                'background: rgba(231, 76, 60, 0.1); color: #e74c3c; border: 1px solid #e74c3c;'
            }
        `;

        form.parentNode.insertBefore(msg, form);

        // Auto remove after 5 seconds
        setTimeout(() => msg.remove(), 5000);
    }
});

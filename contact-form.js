document.addEventListener('DOMContentLoaded', function() {
    // Mark that the contact form handler is loaded
    window.contactFormHandler = true;

    const contactForm = document.getElementById('contact-form');
    if (!contactForm) {
        console.log('Contact form not found');
        return;
    }

    console.log('Contact form found, adding event listener');

    // Add form validation
    contactForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        console.log('Form submission intercepted');

        const submitButton = contactForm.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.textContent;

        try {
            // Disable the submit button
            submitButton.disabled = true;
            submitButton.textContent = 'Sending...';
            submitButton.style.opacity = '0.7';
            submitButton.style.cursor = 'not-allowed';

            // Get form data
            const formData = new FormData(contactForm);

            // Send form data to FormSubmit
            const response = await fetch(contactForm.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                // Show success message
                showMessage('Message sent successfully! We\'ll get back to you soon.', 'success');
                // Reset form
                contactForm.reset();
            } else {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || 'Failed to send message. Please try again.');
            }
        } catch (error) {
            console.error('Form submission error:', error);
            showMessage(error.message || 'An error occurred. Please try again later.', 'error');
        } finally {
            // Re-enable the submit button
            submitButton.disabled = false;
            submitButton.textContent = originalButtonText;
            submitButton.style.opacity = '1';
            submitButton.style.cursor = 'pointer';
        }
    });

    // Function to show status messages
    function showMessage(message, type = 'info') {
        // Remove any existing messages
        const existingMessage = document.querySelector('.form-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `form-message form-message-${type}`;
        messageElement.textContent = message;

        // Style the message
        messageElement.style.padding = '12px 16px';
        messageElement.style.margin = '16px 0';
        messageElement.style.borderRadius = '8px';
        messageElement.style.fontSize = '0.9rem';
        messageElement.style.transition = 'all 0.3s ease';

        // Set colors based on message type
        if (type === 'success') {
            messageElement.style.backgroundColor = 'rgba(46, 204, 113, 0.15)';
            messageElement.style.color = '#2ecc71';
            messageElement.style.border = '1px solid rgba(46, 204, 113, 0.3)';
        } else if (type === 'error') {
            messageElement.style.backgroundColor = 'rgba(231, 76, 60, 0.15)';
            messageElement.style.color = '#e74c3c';
            messageElement.style.border = '1px solid rgba(231, 76, 60, 0.3)';
        } else {
            messageElement.style.backgroundColor = 'rgba(52, 152, 219, 0.15)';
            messageElement.style.color = '#3498db';
            messageElement.style.border = '1px solid rgba(52, 152, 219, 0.3)';
        }

        // Insert the message before the form
        contactForm.parentNode.insertBefore(messageElement, contactForm);

        // Auto-hide the message after 5 seconds
        setTimeout(() => {
            messageElement.style.opacity = '0';
            messageElement.style.margin = '0';
            messageElement.style.padding = '0';
            messageElement.style.height = '0';
            messageElement.style.border = 'none';
            setTimeout(() => {
                messageElement.remove();
            }, 300);
        }, 5000);
    }

    // Add input validation
    const inputs = contactForm.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        // Add focus styles
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        // Remove focus styles
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });

        // Real-time validation for email
        if (input.type === 'email') {
            input.addEventListener('input', function() {
                if (this.validity.typeMismatch) {
                    this.setCustomValidity('Please enter a valid email address');
                } else {
                    this.setCustomValidity('');
                }
            });
        }
    });
});

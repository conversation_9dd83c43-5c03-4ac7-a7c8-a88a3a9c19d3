/* Contact Section */
.contact-section {
    padding: 100px 40px;
    background: linear-gradient(to bottom, #000000 0%, #3498db 100%);
    position: relative;
    min-height: 100vh;
    z-index: calc(var(--z-sections) + 2);
    transform: translateZ(0);
    scroll-margin-top: 80px;
}

.contact-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: left;
    padding: 0 20px;
    box-sizing: border-box;
}

.contact-header {
    text-align: left;
    margin-bottom: 30px;
    width: 100%;
    max-width: 600px;
}

.contact-header h2 {
    margin-bottom: 20px;
    text-align: left;
    max-width: 600px;
}

/* Subheadline with pulsing scale and glow */
.contact-form-subheadline {
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 2.0rem !important;
    margin: 0 0 40px 0;
    width: 100%;
    max-width: 600px;
    line-height: 1.4;
    letter-spacing: 0.01em;
    color: #ffffff !important;
    font-weight: 700 !important;
    position: relative;
    display: block;
    opacity: 1;
    text-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
    transform: none;
    transition: text-shadow 0.3s ease;
    padding: 0;
    box-sizing: border-box;
}

/* Hover effect - enhance the glow */
.contact-content .contact-subheadline:hover {
    text-shadow: 0 0 15px rgba(52, 152, 219, 0.8);
    animation-play-state: paused;
}

/* Keyframes */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px) scale(0.98); }
    to { opacity: 1; transform: translateY(0) scale(1); }
}

@keyframes pulseScale {
    0% {
        transform: scale(1);
        text-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
    }
    50% {
        transform: scale(1.02);
        text-shadow: 0 0 15px rgba(52, 152, 219, 0.8);
    }
    100% {
        transform: scale(1);
        text-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
    }
}

/* Form Container */
/* Contact Widget */
.contact-widget {
    opacity: 1;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
    animation: fadeIn 0.8s ease-out forwards;
}

/* Remove hover effect */
.contact-widget:hover {
    transform: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.contact-form-container {
    max-width: 600px;
    width: 100%;
    margin: 0 auto;
    padding: 40px;
    box-sizing: border-box;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(8px);
}

.contact-form {
    opacity: 0;
    transform: translateY(20px);
    will-change: transform, opacity;
}

/* Contact Form */
.contact-form {
    width: 100%;
    text-align: left;
    opacity: 1;
    transform: none;
}

.contact-form.animate {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.6s ease-out 0.4s, 
                transform 0.6s ease-out 0.4s;
}

/* Form Group */
.form-group {
    position: relative;
    margin-bottom: 25px;
    width: 100%;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1rem;
    transition: all 0.3s ease;
    margin-bottom: 8px;
}

.form-group textarea {
    min-height: 140px;
    resize: vertical;
}

.form-group label {
    position: absolute;
    left: 16px;
    top: 12px;
    color: rgba(255, 255, 255, 0.7);
    pointer-events: none;
    transition: all 0.3s ease;
    background: transparent;
    padding: 0 4px;
    margin: 0 -4px;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #FF42A1;
    box-shadow: none;
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group textarea:focus + label,
.form-group textarea:not(:placeholder-shown) + label {
    top: -10px;
    left: 12px;
    font-size: 0.8rem;
    color: #FF42A1;
    background: rgba(0, 0, 0, 0.8);
    padding: 0 8px;
    border-radius: 4px;
}

.terms-group {
    margin: 20px 0;
}

.terms-group label {
    position: static !important;
    display: inline !important;
    transform: none !important;
    font-size: 0.9rem !important;
    color: rgba(255, 255, 255, 0.8) !important;
}

.cta-button {
    background: #3498db;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-family: 'Hanken Grotesk', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    width: 100%;
    margin-top: 10px;
}

.cta-button:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.cta-button:active {
    transform: translateY(0);
}

/* ===== MODAL STYLES ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    z-index: 9999;
    overflow-y: auto;
    font-family: 'Hanken Grotesk', sans-serif;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.modal.active {
    display: block;
    opacity: 1;
    pointer-events: auto;
}

.modal-content {
    background: #ffffff !important;
    margin: 40px auto !important;
    padding: 0 !important;
    border-radius: 4px !important;
    max-width: 800px !important;
    width: 90% !important;
    max-height: 85vh !important;
    position: relative !important;
    color: #333333 !important;
    overflow-y: auto !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    line-height: 1.6 !important;
    top: 50%;
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    display: flex !important;
    flex-direction: column !important;
}

.modal-header {
    padding: 15px 15px 15px 30px !important;
    position: relative !important;
    border-bottom: 1px solid #eeeeee !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

/* Close button styles are now scoped to .modal */

.modal-body {
    padding: 20px 30px 30px 30px !important;
    flex: 1 !important;
    overflow-y: auto !important;
    font-weight: 400 !important;
    letter-spacing: 0.01em !important;
    line-height: 1.6 !important;
}

.modal-body p,
.modal-body li,
.modal-body div {
    font-weight: 400 !important;
    letter-spacing: 0.01em !important;
    line-height: 1.6 !important;
}

.modal-body ul {
    list-style: none !important;
    padding-left: 0 !important;
    margin: 15px 0 !important;
}

.modal-body li {
    position: relative !important;
    padding-left: 20px !important;
    margin-bottom: 8px !important;
}

.modal-body li:before {
    content: '•' !important;
    position: absolute !important;
    left: 0 !important;
    color: #333 !important;
    font-size: 1.2em !important;
    line-height: 1 !important;
    margin-top: -0.1em !important;
}

.modal h2 {
    color: #333333 !important;
    margin: 0 !important;
    padding: 0 !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.1em !important;
    font-family: 'Hanken Grotesk', sans-serif !important;
    border: none !important;
    width: auto !important;
    display: inline !important;
}

.modal h3 {
    color: #333333 !important;
    margin: 20px 0 10px 0 !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    font-family: 'Hanken Grotesk', sans-serif !important;
}

.modal p, .modal li {
    color: #333333 !important;
    margin-bottom: 15px !important;
    font-size: 1rem !important;
    font-family: 'Hanken Grotesk', sans-serif !important;
}

.modal ul {
    padding-left: 20px !important;
    margin-bottom: 20px !important;
}

.modal li {
    margin-bottom: 10px !important;
    position: relative !important;
    padding-left: 15px !important;
    font-size: 1rem !important;
    font-family: 'Hanken Grotesk', sans-serif !important;
}

.modal li:before {
    content: '•' !important;
    color: #333333 !important;
    position: absolute !important;
    left: 0 !important;
    font-weight: normal !important;
}

.last-updated {
    font-style: italic !important;
    color: #666666 !important;
    font-size: 1rem !important;
    margin-top: 30px !important;
    border-top: 1px solid #eeeeee !important;
    padding-top: 15px !important;
    font-family: 'Hanken Grotesk', sans-serif !important;
}

a[href^="mailto:"] {
    color: #333333 !important;
    text-decoration: none !important;
    transition: color 0.2s ease !important;
    border-bottom: 1px solid #cccccc !important;
    padding-bottom: 1px !important;
    font-family: 'Hanken Grotesk', sans-serif !important;
}

a[href^="mailto:"]:hover {
    color: #000000 !important;
    border-bottom-color: #000000 !important;
}

/* Close button */
.close-modal {
    position: absolute !important;
    right: 25px !important;
    top: 20px !important;
    font-size: 2rem !important;
    font-weight: 300 !important;
    color: #333333 !important;
    text-decoration: none !important;
    transition: color 0.2s ease !important;
    line-height: 1 !important;
    font-family: 'Hanken Grotesk', sans-serif !important;
}

.close-modal:hover {
    color: #000000 !important;
}

.close-modal {
    position: absolute;
    top: 10px;
    right: 20px;
    color: #fff;
    font-size: 30px;
    font-weight: bold;
    text-decoration: none;
    line-height: 1;
}

.close-modal:hover {
    color: #FF42A1;
}

.modal h3 {
    color: #FF42A1;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.modal-body {
    line-height: 1.7;
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 15px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
}

.terms-content h4,
.privacy-content h4 {
    color: #FF42A1;
    margin: 1.5em 0 0.8em;
    font-size: 1.1rem;
    font-weight: 600;
}

.terms-content p,
.privacy-content p {
    margin-bottom: 1em;
    line-height: 1.7;
}

.terms-content ul,
.privacy-content ul {
    margin: 0.8em 0 1.2em 1.5em;
    padding-left: 1em;
}

.terms-content li,
.privacy-content li {
    margin-bottom: 25px;
    position: relative;
    transition: all 0.3s ease;
}

.form-group.error {
    margin-bottom: 30px;
}

.error-message {
    color: #ff4444;
    font-size: 0.85em;
    margin-top: 5px;
    font-weight: 500;
    animation: fadeIn 0.3s ease-out;
}

.form-group.error input,
.form-group.error textarea {
    border-color: #ff4444;
    background-color: rgba(255, 68, 68, 0.05);
}

.form-group.error label {
    color: #ff4444;
}

.terms-group.error {
    border-left: 3px solid #ff4444;
    padding-left: 15px;
    margin-left: -15px;
}

.terms-content li,
.privacy-content li {
    margin-bottom: 0.6em;
    position: relative;
    padding-left: 1.2em;
}

.terms-content li:before,
.privacy-content li:before {
    content: '•';
    color: #FF42A1;
    position: absolute;
    left: 0;
    font-size: 1.2em;
    line-height: 1;
}

.last-updated {
    font-style: italic;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 2em;
    font-size: 0.9rem;
}

.contact-info {
    margin-top: 2em;
    padding-top: 1em;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-info h4 {
    margin-bottom: 0.5em !important;
}

.contact-info a {
    color: #FF42A1;
    text-decoration: none;
    transition: color 0.2s ease;
}

.contact-info a:hover {
    color: #ff6cb8;
    text-decoration: underline;
}

/* Success Message Modal */
.success-message {
    text-align: center;
    max-width: 500px;
    margin: 40px auto;
}

.success-message .success-icon {
    margin-bottom: 20px;
    color: #4CAF50;
}

.success-message h3 {
    color: #4CAF50;
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.success-message p {
    margin-bottom: 25px;
    font-size: 1.1rem;
    line-height: 1.6;
}

.modal-buttons {
    margin-top: 25px;
}

/* Modal Buttons */
.cta-button {
    background: #FF42A1;
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 1.1rem;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: inline-block;
    text-decoration: none;
    font-weight: 600;
}

.cta-button:hover {
    background: #ff6cb8;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 66, 161, 0.3);
}

/* Custom scrollbar for modal content */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #FF42A1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #ff6cb8;
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #FF42A1;
    border-radius: 3px;
}

/* Terms and conditions specific styles */
.terms-group {
    display: flex;
    align-items: flex-start;
    margin: 20px 0;
    gap: 10px;
    flex-wrap: wrap;
}

.terms-label {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.9rem !important;
    line-height: 1.5;
    font-weight: 400 !important;
    margin: 0 !important;
    padding: 0 !important;
    position: static !important;
    transform: none !important;
    pointer-events: auto !important;
}


/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    overflow: auto;
    text-align: center;
    white-space: nowrap;
    padding: 20px 0;
    border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    box-sizing: border-box;
}

.modal::before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -4px;
}

/* Show modal when targeted */
.modal:target {
    display: flex;
}

/* Close Button */
.modal .close-modal {
    position: absolute !important;
    top: 5px !important;
    right: 5px !important;
    font-size: 32px !important;
    font-weight: 300 !important;
    color: #333 !important;
    text-decoration: none !important;
    z-index: 1000 !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important;
    margin: 0 !important;
    line-height: 1 !important;
    transition: color 0.2s ease !important;
}

.modal .close-modal:hover {
    color: #FF42A1 !important;
    background: none !important;
}

/* Close overlay */
.modal-close-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.modal-content,
#termsModal .modal-content,
#privacyModal .modal-content,
.modal .modal-content {
    background: white !important;
    color: #333 !important;
    padding: 30px !important;
    max-width: 800px !important;
    width: 90% !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    position: relative !important;
    display: inline-flex !important;
    flex-direction: column !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25) !important;
    border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    margin: 0 !important;
    text-align: left;
    white-space: normal;
    vertical-align: middle;
}

.close-modal {
position: absolute;
top: 15px;
right: 20px;
font-size: 28px;
font-weight: bold;
color: #fff;
cursor: pointer;
transition: color 0.3s ease;
}

.close-modal:hover {
    color: #FF42A1;
}

.modal h3 {
    color: #FF42A1;
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 24px;
}

.modal-body {
    line-height: 1.6;
}

.modal-body p {
    margin-bottom: 15px;
}

/* Terms Acceptance Modal */
.message-content {
    text-align: center;
    padding: 20px 0;
}

.modal-buttons {
    margin-top: 30px;
    display: flex;
    justify-content: center;
    gap: 15px;
}

.modal-buttons .cta-button {
    padding: 12px 35px;
    font-size: 1rem;
    cursor: pointer;
    background: #FF42A1;
    color: white;
    border: none;
    border-radius: 4px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.modal-buttons .cta-button:hover {
    background: #e03185;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.terms-group input[type="checkbox"] {
    margin-right: 12px;
    flex-shrink: 0;
    width: 18px;
    height: 18px;
    cursor: pointer;
    margin: 0 12px 0 0; /* Reset top/bottom margin */
    position: relative;
    top: 0; /* Reset any positioning */
}

.terms-label {
    position: static !important;
    font-size: 0.9rem !important;
    color: rgba(255, 255, 255, 0.8) !important;
    line-height: 1.5;
    cursor: pointer;
    transform: none !important;
    margin: 0 !important;
    padding: 0 !important;
    font-weight: 400 !important;
}

.terms-link {
    color: #ffffff;
    text-decoration: none;
    transition: all 0.2s ease;
    font-weight: 700;
}

.terms-link:hover {
    color: #FF42A1;
    text-decoration: none;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 12px 15px; /* Reduced vertical padding */
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.1rem;
    color: var(--color-light);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0; /* Square corners */
    outline: none;
    transition: all 0.3s ease;
    margin-bottom: 0;
}

/* Focus states */
.contact-form input:focus,
.contact-form textarea:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Placeholder text */
.contact-form input::placeholder,
.contact-form textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.contact-form textarea {
    resize: none;
    min-height: 100px;
}

.contact-form label {
    position: absolute;
    top: 12px; /* Adjusted to match reduced input height */
    left: 15px;
    padding: 0 4px;
    font-family: 'Hanken Grotesk', sans-serif;
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.7);
    pointer-events: none;
    transition: all 0.3s ease;
    background: transparent;
    margin: 0;
    line-height: 1;
}

.contact-form input:focus,
.contact-form textarea:focus,
.contact-form input:valid,
.contact-form textarea:valid {
    border-bottom-color: var(--color-light);
}

.contact-form input:focus + label,
.contact-form textarea:focus + label,
.contact-form input:valid + label,
.contact-form textarea:valid + label {
    top: -20px; /* Move higher to prevent touching the field */
    left: 5px;
    font-size: 0.8rem;
    color: #FF42A1; /* Artchop pink */
    background: transparent;
    padding: 0 8px;
    z-index: 1;
    font-weight: 500;
}

.contact-form .cta-button {
    margin: 20px 0 0 0;
    font-family: 'Hanken Grotesk', sans-serif;
    background: #FF42A1;
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: auto;
    display: block;
    border-radius: 4px;
}

/* Submit button hover effect removed as requested */
.contact-form .cta-button {
    margin: 20px 0 0 0;
    font-family: 'Hanken Grotesk', sans-serif;
    background: #FF42A1;
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: none; /* Removed transition */
    width: auto;
    display: block;
    border-radius: 4px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .contact-content h2 {
        font-size: 2.5rem;
    }
    
    .contact-subheadline {
        font-size: 1.5rem;
        padding: 0 var(--spacing-sm);
    }
    
    .contact-form {
        padding: 0 var(--spacing-sm);
    }
}

/**
 * Artchop Modal System
 * Handles all modal functionality including:
 * - Loading modal content
 * - Showing/hiding modals
 * - Form validation
 */

console.log('modals.js loaded');

// Make ModalSystem globally available
window.ModalSystem = class ModalSystem {
    constructor() {
        console.log('Initializing ModalSystem...');
        this.modalContainer = document.getElementById('modalContainer');
        if (!this.modalContainer) {
            console.error('Modal container not found');
            return;
        }
        console.log('Modal container found:', this.modalContainer);
        this.currentModal = null;
        this.init();
    }

    init() {
        console.log('Setting up modal event listeners...');
        
        // Handle link clicks for modals
        document.addEventListener('click', (e) => {
            // Check if clicked element or its parent has data-modal attribute or is a terms-link
            let link = e.target.closest('[data-modal]') || 
                      (e.target.matches('.terms-link') ? e.target : null);
            
            if (link) {
                e.preventDefault();
                e.stopPropagation();
                
                const modalName = link.getAttribute('data-modal');
                if (modalName) {
                    console.log('Loading modal:', modalName);
                    this.loadModal(modalName);
                } else {
                    console.warn('No data-modal attribute found on link:', link);
                }
            }
        });
        
        // Add cursor pointer to all terms links
        document.querySelectorAll('.terms-link').forEach(link => {
            link.style.cursor = 'pointer';
        });

        // Close modal when clicking outside content
        this.modalContainer.addEventListener('click', (e) => {
            if (e.target === this.modalContainer) {
                this.closeModal();
            }
        });

        // Close with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modalContainer.classList.contains('active')) {
                this.closeModal();
            }
        });

        // Handle form submission
        const contactForm = document.getElementById('contact-form');
        if (contactForm) {
            contactForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
    }

    async loadModal(modalName) {
        try {
            console.log(`Loading modal: ${modalName}`);
            const response = await fetch(`modals/${modalName}.html`);
            if (!response.ok) throw new Error('Modal not found');
            
            const html = await response.text();
            this.modalContainer.innerHTML = html;
            this.modalContainer.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // Set up close button if it exists
            const closeBtn = this.modalContainer.querySelector('.close-modal');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.closeModal());
            }

            // Set up any modal-specific buttons
            const closeMessageBtn = this.modalContainer.querySelector('#closeTermsMessage, #closeSuccessMessage');
            if (closeMessageBtn) {
                closeMessageBtn.addEventListener('click', () => this.closeModal());
            }

            // Store reference to current modal
            this.currentModal = modalName;
            
        } catch (error) {
            console.error('Error loading modal:', error);
        }
    }

    closeModal() {
        this.modalContainer.classList.remove('active');
        document.body.style.overflow = 'auto';
        this.currentModal = null;
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        const termsCheckbox = document.getElementById('terms');
        
        if (!termsCheckbox.checked) {
            // Show terms acceptance modal
            await this.loadModal('accept-terms');
            return false;
        }
        
        // Here you would typically submit the form via AJAX
        // For now, we'll just show the success message
        this.loadModal('message-sent');
        
        // Reset form
        e.target.reset();
        return false;
    }
}

// The modal system will be initialized by the inline script in index.html
